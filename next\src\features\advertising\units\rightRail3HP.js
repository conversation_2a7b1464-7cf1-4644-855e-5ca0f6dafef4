import { demandConfig, sizeDefaults } from './demandConfig'

export const rightRail3HP = {
  id: 'right-rail-3-hp',
  path: '/21841313772,22554256/kitco/right_rail_three',
  sizes: sizeDefaults.rhpsb2,
  sizeMappingName: 'rightRailMdHP',
  refreshTime: 30,
  targeting: {
    refresh: 'true',
    refresh_time: '30',
  },
  prebid: [
    {
      mediaTypes: {
        banner: {
          sizeConfig: [
            { minViewPort: [0, 0], sizes: [[300, 250]] },
            { minViewPort: [768, 0], sizes: [[300, 250]] },
            {
              minViewPort: [1270, 0],
              sizes: [
                [300, 250],
                [336, 280],
                [300, 600],
              ],
            },
          ],
        },
      },
      bids: [
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '3091240',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '5728128',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'openx',
          params: {
            unit: '*********',
            delDomain: demandConfig.openx.delDomain,
          },
        },
        {
          bidder: 'rubicon',
          params: {
            accountId: demandConfig.rubicon.accountId,
            siteId: demandConfig.rubicon.siteId,
            zoneId: '1802108',
          },
        },
        {
          bidder: 'sovrn',
          params: {
            tagid: '756858',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'sovrn',
          params: {
            tagid: '1200211',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'ix',
          params: {
            siteId: '560690',
          },
        },
        {
          bidder: 'sharethrough',
          params: {
            pkey: 'p7dvw4qXVgdFmobZh0iVrAIg',
          },
        },
        {
          bidder: 'medianet',
          params: {
            cid: demandConfig.medianet.cid,
            crid: '854666137',
          },
        },
        {
          bidder: 'sonobi',
          params: {
            placement_id: '3c3edd9d7ccdf2f0dee7',
          },
        },
        {
          bidder: 'onetag',
          params: {
            pubId: demandConfig.onetag.pubId,
          },
        },
        {
          bidder: 'oms',
          params: {
            publisherId: demandConfig.brightcom.publisherId,
          },
        },
        {
          bidder: 'eplanning',
          params: {
            ci: demandConfig.eplanning.ci,
          },
        },
        {
          bidder: 'nativo',
          params: {
            placementId: 1467947,
          },
        },
        {
          bidder: 'anyclip',
          params: {
            publisherId: '86',
            supplyTagId: 'd10c6ee9-cdba-4a30-9b4a-7fbdc402da3a',
          },
        },
        {
          bidder: 'rumble',
          params: {
            publisherId: 38329,
            siteId: 123,
            zoneId: 511, // 300x250
          },
        },
      ],
    },
  ],
}
