import { confirmPasswordReset, verifyPasswordResetCode } from 'firebase/auth'
import type React from 'react'
import { useEffect, useState } from 'react'
import { BsShieldCheck, BsShieldExclamation } from 'react-icons/bs'
import { CgSpinner } from 'react-icons/cg'
import Input from '~/src/components/Auth/Form/Elements/Input'
import NewPassword from '~/src/components/Auth/Form/Elements/NewPassword'
import SubmitButton from '~/src/components/Auth/Form/Elements/SubmitButton'
import LoginLegalFooter from '~/src/components/Auth/Form/LoginLegalFooter'
import { useRecaptchaVerification } from '~/src/features/auth/recapcha'
import { auth } from '~/src/services/firebase/config'
import firebaseError from '~/src/services/firebase/errors'

interface ResetPasswordProps {
  code: string
  onError: (message: string) => void
  backToLogin?: () => void
  requestPasswordReset: () => void
}

const ResetPassword: React.FC<ResetPasswordProps> = ({
  code,
  onError,
  backToLogin,
  requestPasswordReset,
}) => {
  // Fields
  const [email, setEmail] = useState<string>('')
  const [newPassword, setNewPassword] = useState<string>('')

  // State for the password verification process
  const [isVerifying, setIsVerifying] = useState<boolean>(false)
  const [isResettingPassword, setIsResettingPassword] = useState<boolean>(false)

  // State for the invalid code
  const [invalidCode, setInvalidCode] = useState<boolean>(false)

  // State for the password error
  const [passwordError, setPasswordError] = useState<boolean>(false)

  // State for the password reset success
  const [passwordWasReset, setPasswordWasReset] = useState<boolean>(false)

  // Custom hook to verify the user with reCAPTCHA
  const { checkRecaptcha } = useRecaptchaVerification()

  /**
   * Handle the verification of the password reset code with Firebase
   */
  const handleVerifyPasswordResetCode = async () => {
    setInvalidCode(false)
    setIsVerifying(true)

    // Reset errors and fields
    onError('')
    setEmail('')

    try {
      // Try to verify the password reset code
      const email = await verifyPasswordResetCode(auth, code)

      setEmail(email)
    } catch {
      onError('Failed to verify the password reset code.')
      setInvalidCode(true)
    } finally {
      setIsVerifying(false)
    }
  }

  /**
   * Handle the code verification when the code changes
   */
  useEffect(() => {
    handleVerifyPasswordResetCode()
  }, [code])

  /**
   * Handle the new password value change
   *
   * @param e
   */
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setNewPassword(value)
  }

  /**
   * Handle the form submission to reset the password
   *
   * @param e
   */
  const handleResetPassword = async (e: React.FormEvent<HTMLFormElement>) => {
    // Prevent the form from refreshing the page
    e.preventDefault()

    // Set the state and clear the error message
    setIsResettingPassword(true)
    setPasswordWasReset(false)
    onError('')

    try {
      if (passwordError) {
        onError('Form is not valid. Please check the fields.')
        return
      }

      const recaptchaVerified = await checkRecaptcha()
      if (!recaptchaVerified) {
        onError('The reCAPTCHA verification failed. Please try again.')
        return
      }

      if (!newPassword) {
        onError('Please enter a new password.')
        return
      }

      // Try to reset the password with Firebase
      await confirmPasswordReset(auth, code, newPassword)

      // Password was reset successfully
      setPasswordWasReset(true)
    } catch (error) {
      const errorMessage = firebaseError(
        error.code,
        'Failed to reset password. Please try again.',
      )
      onError(errorMessage)
    } finally {
      setIsResettingPassword(false)
    }
  }

  return (
    <>
      <div className="sm:mx-auto sm:w-full sm:max-w-sm">
        {isVerifying ? (
          <>
            <div className="my-4 flex w-full items-center justify-center text-center">
              <CgSpinner
                className="h-12 w-12 animate-spin text-ktc-blue"
                aria-hidden="true"
              />
            </div>

            <p>Verifying your reset password code. Please wait...</p>
          </>
        ) : invalidCode ? (
          <div className="mt-6 space-y-6">
            <div className="relative flex h-24 w-full items-center justify-center">
              <div className="absolute flex items-center justify-center">
                <div className="flex h-24 w-24 items-center justify-center bg-white">
                  <BsShieldExclamation className="text-6xl text-ktc-blue" />
                </div>
              </div>
            </div>
            <h3 className="mb-4 text-lg font-bold">
              Something went wrong. Please try again.
            </h3>
            <p className="mt-4">
              The password reset link is invalid or expired. Please request a
              new password reset link.
            </p>
            <SubmitButton
              onClick={() => {
                // Clear the error message
                onError('')

                // Request a new password reset
                requestPasswordReset()
              }}
              buttonText="Request Password Reset Again"
              type="button"
            />
          </div>
        ) : passwordWasReset ? (
          <div className="mt-4 space-y-6">
            <div className="relative flex h-24 w-full items-center justify-center">
              <div className="absolute flex items-center justify-center">
                <div className="flex h-24 w-24 items-center justify-center bg-white">
                  <BsShieldCheck className="text-6xl text-ktc-blue" />
                </div>
              </div>
            </div>
            <h3 className="mb-4 text-lg font-bold">
              Success! Your password has been reset.
            </h3>
            <p className="mt-4">
              You can now login with your new password. Please click the button
              below to go back to the login page.
            </p>
            <SubmitButton
              onClick={() => {
                // Clear the error message
                onError('')
                backToLogin()
              }}
              buttonText="Back to Login"
              type="button"
            />
          </div>
        ) : (
          <form className="mt-4 space-y-6" onSubmit={handleResetPassword}>
            <div>
              <Input
                label="Email address"
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required={false}
                value={email}
                readOnly
                disabled
              />
            </div>
            <NewPassword onChange={handleChange} onError={setPasswordError} />
            <div>
              <SubmitButton
                isSending={isResettingPassword}
                disabledCondition={passwordError || isResettingPassword}
                buttonText="Reset Password"
                loadingText="Resetting Password..."
              />
              <div className="mt-4 text-center text-sm">
                <button
                  type="button"
                  onClick={backToLogin}
                  className="font-semibold text-ktc-blue hover:text-ktc-black"
                >
                  Back to Login
                </button>
              </div>
            </div>
          </form>
        )}
      </div>
      <div className="mt-6 sm:mx-auto sm:w-full sm:max-w-sm">
        <LoginLegalFooter />
      </div>
    </>
  )
}

export default ResetPassword
