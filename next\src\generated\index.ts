export type Maybe<T> = T | null
export type InputMaybe<T> = Maybe<T>
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K]
}
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>
}
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>
}
export type MakeEmpty<
  T extends { [key: string]: unknown },
  K extends keyof T,
> = { [_ in K]?: never }
export type Incremental<T> =
  | T
  | {
      [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never
    }
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string }
  String: { input: string; output: string }
  Boolean: { input: boolean; output: boolean }
  Int: { input: number; output: number }
  Float: { input: number; output: number }
}

export type AudioArchiveSnippetInput = {
  id: Scalars['Int']['input']
  isArchived: Scalars['Boolean']['input']
}

export type AudioAsset = {
  __typename?: 'AudioAsset'
  createdAt?: Maybe<Scalars['String']['output']>
  duration?: Maybe<Scalars['Int']['output']>
  id: Scalars['Int']['output']
  origin?: Maybe<Scalars['Int']['output']>
  pitch?: Maybe<Scalars['Int']['output']>
  size?: Maybe<Scalars['Int']['output']>
  speed?: Maybe<Scalars['Int']['output']>
  tagsPlainText?: Maybe<Array<Maybe<Scalars['String']['output']>>>
  tagsRawResponse?: Maybe<AudioTagsRawResponse>
  transcriptPlainText?: Maybe<Scalars['String']['output']>
  transcriptRawResponse?: Maybe<AudioTranscriptRawResponse>
  uploadedName?: Maybe<Scalars['String']['output']>
  uuid: Scalars['String']['output']
  voice?: Maybe<Scalars['String']['output']>
}

export type AudioCategory = {
  __typename?: 'AudioCategory'
  id: Scalars['Int']['output']
  name: Scalars['String']['output']
}

export type AudioConvertTextToSpeechInput = {
  text: Scalars['String']['input']
  userId: Scalars['String']['input']
  uuid: Scalars['String']['input']
}

export type AudioCreateAssetInput = {
  origin: Scalars['Int']['input']
  uploadedName?: InputMaybe<Scalars['String']['input']>
  userId: Scalars['String']['input']
  uuid: Scalars['String']['input']
}

export type AudioCreateCategoryInput = {
  name: Scalars['String']['input']
}

export type AudioCreateGuestInput = {
  name: Scalars['String']['input']
}

export type AudioCreateSettingInput = {
  pitch?: InputMaybe<Scalars['Int']['input']>
  speed?: InputMaybe<Scalars['Int']['input']>
  voice?: InputMaybe<Scalars['String']['input']>
}

export type AudioCreateSnippetInput = {
  assetId?: InputMaybe<Scalars['Int']['input']>
  categoryId?: InputMaybe<Scalars['Int']['input']>
  description?: InputMaybe<Scalars['String']['input']>
  distributionHeadline?: InputMaybe<Scalars['String']['input']>
  endTime?: InputMaybe<Scalars['Int']['input']>
  guestNames?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>
  headline?: InputMaybe<Scalars['String']['input']>
  parentSnippetId?: InputMaybe<Scalars['Int']['input']>
  snippetUuid?: InputMaybe<Scalars['String']['input']>
  startTime?: InputMaybe<Scalars['Int']['input']>
  status?: InputMaybe<Scalars['Int']['input']>
  tagNames?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>
  thumbnailBase64?: InputMaybe<Scalars['String']['input']>
  thumbnailUuid?: InputMaybe<Scalars['String']['input']>
  transcriptPlainText?: InputMaybe<Scalars['String']['input']>
  userId?: InputMaybe<Scalars['String']['input']>
}

export type AudioCreateTagInput = {
  name: Scalars['String']['input']
}

export type AudioDeleteCategoryInput = {
  id: Scalars['Int']['input']
}

export type AudioDeleteJobInput = {
  jobId: Scalars['String']['input']
}

export type AudioDeleteSnippetInput = {
  id: Scalars['Int']['input']
}

export type AudioGuest = {
  __typename?: 'AudioGuest'
  id: Scalars['Int']['output']
  name: Scalars['String']['output']
}

export type AudioImportSnippetFromVcmsInput = {
  asset: AudioImportedAssetInput
  snippet: AudioImportedSnippetInput
}

export type AudioImportedAssetInput = {
  duration: Scalars['Int']['input']
  tagsPlainText: Array<InputMaybe<Scalars['String']['input']>>
  tagsRawResponse: Scalars['String']['input']
  transcriptPlainText: Scalars['String']['input']
  transcriptRawResponse: Scalars['String']['input']
  userId: Scalars['String']['input']
  uuid: Scalars['String']['input']
}

export type AudioImportedSnippetInput = {
  description: Scalars['String']['input']
  distributionHeadline: Scalars['String']['input']
  guests?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>
  headline: Scalars['String']['input']
  isArchived: Scalars['Boolean']['input']
  status: Scalars['Int']['input']
  tags?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>
  thumbnailUuid: Scalars['String']['input']
  transcriptPlainText?: InputMaybe<Scalars['String']['input']>
  userId?: InputMaybe<Scalars['String']['input']>
  uuid: Scalars['String']['input']
}

export type AudioJobCounts = {
  __typename?: 'AudioJobCounts'
  active?: Maybe<Scalars['Int']['output']>
  completed?: Maybe<Scalars['Int']['output']>
  delayed?: Maybe<Scalars['Int']['output']>
  failed?: Maybe<Scalars['Int']['output']>
  paused?: Maybe<Scalars['Int']['output']>
  waiting?: Maybe<Scalars['Int']['output']>
}

export type AudioProgress = {
  __typename?: 'AudioProgress'
  progress?: Maybe<Scalars['Int']['output']>
  state?: Maybe<Scalars['String']['output']>
}

export type AudioReconvertTextToSpeechInput = {
  newUuid: Scalars['String']['input']
  oldUuid: Scalars['String']['input']
  text: Scalars['String']['input']
  userId: Scalars['String']['input']
}

export type AudioSearchSnippetsResponse = {
  __typename?: 'AudioSearchSnippetsResponse'
  snippets?: Maybe<Array<Maybe<AudioSnippet>>>
  total?: Maybe<Scalars['Int']['output']>
}

export type AudioSetting = {
  __typename?: 'AudioSetting'
  id?: Maybe<Scalars['Int']['output']>
  pitch?: Maybe<Scalars['Int']['output']>
  speed?: Maybe<Scalars['Int']['output']>
  voice?: Maybe<Scalars['String']['output']>
}

export type AudioSnippet = {
  __typename?: 'AudioSnippet'
  asset?: Maybe<AudioAsset>
  category?: Maybe<AudioCategory>
  createdAt?: Maybe<Scalars['Int']['output']>
  description?: Maybe<Scalars['String']['output']>
  distributionHeadline?: Maybe<Scalars['String']['output']>
  endTime?: Maybe<Scalars['Int']['output']>
  guests?: Maybe<Array<Maybe<AudioGuest>>>
  headline?: Maybe<Scalars['String']['output']>
  id?: Maybe<Scalars['Int']['output']>
  isArchived?: Maybe<Scalars['Boolean']['output']>
  parentSnippet?: Maybe<AudioSnippet>
  pitch?: Maybe<Scalars['Int']['output']>
  speed?: Maybe<Scalars['Int']['output']>
  startTime?: Maybe<Scalars['Int']['output']>
  status?: Maybe<Scalars['Int']['output']>
  tags?: Maybe<Array<Maybe<AudioTag>>>
  thumbnailUuid?: Maybe<Scalars['String']['output']>
  transcriptPlainText?: Maybe<Scalars['String']['output']>
  userId?: Maybe<Scalars['String']['output']>
  uuid?: Maybe<Scalars['String']['output']>
  voice?: Maybe<Scalars['String']['output']>
}

export enum AudioSnippetSearchSort {
  DateAsc = 'dateAsc',
  DateDesc = 'dateDesc',
  Relevance = 'relevance',
}

export type AudioSpeechRecognitionAlternative = {
  __typename?: 'AudioSpeechRecognitionAlternative'
  words?: Maybe<Array<Maybe<AudioSpeechRecognitionWord>>>
}

export type AudioSpeechRecognitionResult = {
  __typename?: 'AudioSpeechRecognitionResult'
  alternatives?: Maybe<Array<Maybe<AudioSpeechRecognitionAlternative>>>
}

export type AudioSpeechRecognitionTime = {
  __typename?: 'AudioSpeechRecognitionTime'
  nanos?: Maybe<Scalars['Int']['output']>
  seconds?: Maybe<Scalars['String']['output']>
}

export type AudioSpeechRecognitionWord = {
  __typename?: 'AudioSpeechRecognitionWord'
  endTime?: Maybe<AudioSpeechRecognitionTime>
  startTime?: Maybe<AudioSpeechRecognitionTime>
  word?: Maybe<Scalars['String']['output']>
}

export type AudioTag = {
  __typename?: 'AudioTag'
  id: Scalars['Int']['output']
  name: Scalars['String']['output']
  tokens: Scalars['String']['output']
}

export type AudioTagEntities = {
  __typename?: 'AudioTagEntities'
  name?: Maybe<Scalars['String']['output']>
  salience?: Maybe<Scalars['Float']['output']>
  type?: Maybe<Scalars['String']['output']>
}

export type AudioTagsRawResponse = {
  __typename?: 'AudioTagsRawResponse'
  entities?: Maybe<Array<Maybe<AudioTagEntities>>>
}

export type AudioTranscriptRawResponse = {
  __typename?: 'AudioTranscriptRawResponse'
  results?: Maybe<Array<Maybe<AudioSpeechRecognitionResult>>>
}

export type AudioTts = {
  __typename?: 'AudioTts'
  assetUuid?: Maybe<Scalars['String']['output']>
  endTime?: Maybe<Scalars['Int']['output']>
  isPublished?: Maybe<Scalars['Boolean']['output']>
  /** @deprecated No longer supported */
  snippetUuid?: Maybe<Scalars['String']['output']>
  startTime?: Maybe<Scalars['Int']['output']>
  status?: Maybe<Scalars['Boolean']['output']>
}

export type AudioUpdateCategoryInput = {
  id: Scalars['Int']['input']
  name: Scalars['String']['input']
}

export type AudioUpdateSnippetInput = {
  categoryId?: InputMaybe<Scalars['Int']['input']>
  description?: InputMaybe<Scalars['String']['input']>
  distributionHeadline?: InputMaybe<Scalars['String']['input']>
  endTime?: InputMaybe<Scalars['Int']['input']>
  guestNames?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>
  headline?: InputMaybe<Scalars['String']['input']>
  id: Scalars['Int']['input']
  startTime?: InputMaybe<Scalars['Int']['input']>
  status?: InputMaybe<Scalars['Int']['input']>
  tagNames?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>
  thumbnailBase64?: InputMaybe<Scalars['String']['input']>
  thumbnailUuid?: InputMaybe<Scalars['String']['input']>
  transcriptPlainText?: InputMaybe<Scalars['String']['input']>
}

export type AudioUpdateSnippetTranscriptPlainTextInput = {
  id: Scalars['Int']['input']
  transcriptPlainText: Scalars['String']['input']
}

export type Author = {
  __typename?: 'Author'
  /** @deprecated Use roles instead */
  authorType?: Maybe<Scalars['String']['output']>
  authorWebsite?: Maybe<Scalars['String']['output']>
  body?: Maybe<Scalars['String']['output']>
  contactEmail?: Maybe<Scalars['String']['output']>
  email?: Maybe<Scalars['String']['output']>
  facebookId?: Maybe<Scalars['String']['output']>
  hidden?: Maybe<Scalars['Boolean']['output']>
  id: Scalars['Int']['output']
  imageUrl?: Maybe<Scalars['String']['output']>
  linkedInId?: Maybe<Scalars['String']['output']>
  name?: Maybe<Scalars['String']['output']>
  /** @deprecated Use imageUrl instead */
  profileImageUrl?: Maybe<Scalars['String']['output']>
  roles?: Maybe<Array<Maybe<Scalars['String']['output']>>>
  /** @deprecated Use imageUrl instead */
  thumbnailImageUrl?: Maybe<Scalars['String']['output']>
  title?: Maybe<Scalars['String']['output']>
  twitterId?: Maybe<Scalars['String']['output']>
  urlAlias?: Maybe<Scalars['String']['output']>
}

export type BarchartFuture = {
  __typename?: 'BarchartFuture'
  ID: Scalars['Int']['output']
  close: Scalars['Float']['output']
  dayCode: Scalars['String']['output']
  dollarVolume: Scalars['Float']['output']
  flag: Scalars['String']['output']
  high: Scalars['Float']['output']
  lastPrice: Scalars['Float']['output']
  low: Scalars['Float']['output']
  mode: Scalars['String']['output']
  name: Scalars['String']['output']
  netChange: Scalars['Float']['output']
  numTrades: Scalars['Int']['output']
  open: Scalars['Float']['output']
  percentChange: Scalars['Float']['output']
  previousOpenInterest: Scalars['Float']['output']
  previousVolume: Scalars['Int']['output']
  symbol: Scalars['String']['output']
  tradeTimestamp: Scalars['String']['output']
  unitCode: Scalars['String']['output']
  volume: Scalars['Int']['output']
}

export type BarchartGetFuturesByExchange = {
  __typename?: 'BarchartGetFuturesByExchange'
  ID: Scalars['Int']['output']
  exchange: Scalars['String']['output']
  results?: Maybe<Array<Maybe<BarchartFuture>>>
  timestamp: Scalars['Int']['output']
}

export type BarchartGetLeaders = {
  __typename?: 'BarchartGetLeaders'
  ID: Scalars['Int']['output']
  exchanges: Scalars['String']['output']
  leaderboardType: Scalars['String']['output']
  results?: Maybe<Array<Maybe<BarchartLeader>>>
  timestamp: Scalars['Int']['output']
}

export type BarchartGetQuote = {
  __typename?: 'BarchartGetQuote'
  ID: Scalars['Int']['output']
  results?: Maybe<Array<Maybe<BarchartQuote>>>
  symbols: Scalars['String']['output']
  timestamp: Scalars['Int']['output']
}

export type BarchartLeader = {
  __typename?: 'BarchartLeader'
  ID: Scalars['Int']['output']
  country: Scalars['String']['output']
  exchange: Scalars['String']['output']
  industry: Scalars['String']['output']
  lastPrice: Scalars['Float']['output']
  previousClose: Scalars['Float']['output']
  previousVolume: Scalars['Int']['output']
  priceNetChange: Scalars['Float']['output']
  pricePercentChange: Scalars['Float']['output']
  sicSector: Scalars['String']['output']
  standardDeviation: Scalars['Float']['output']
  subIndustry: Scalars['String']['output']
  symbol: Scalars['String']['output']
  symbolName: Scalars['String']['output']
  timestamp: Scalars['String']['output']
  tradeTimestamp: Scalars['String']['output']
  volume: Scalars['Int']['output']
}

export type BarchartQuote = {
  __typename?: 'BarchartQuote'
  ID: Scalars['Int']['output']
  close: Scalars['Float']['output']
  dayCode: Scalars['String']['output']
  dollarVolume: Scalars['Float']['output']
  flag: Scalars['String']['output']
  high: Scalars['Float']['output']
  lastPrice: Scalars['Float']['output']
  low: Scalars['Float']['output']
  mode: Scalars['String']['output']
  name: Scalars['String']['output']
  netChange: Scalars['Float']['output']
  numTrades: Scalars['Int']['output']
  open: Scalars['Float']['output']
  percentChange: Scalars['Float']['output']
  previousVolume: Scalars['Int']['output']
  serverTimestamp: Scalars['String']['output']
  symbol: Scalars['String']['output']
  tradeTimestamp: Scalars['String']['output']
  unitCode: Scalars['String']['output']
  volume: Scalars['Int']['output']
}

export type BasicPage = NodeInterface & {
  __typename?: 'BasicPage'
  author?: Maybe<Author>
  /** @deprecated Use bodyWithEmbeddedMedia instead */
  body?: Maybe<Scalars['String']['output']>
  bodyWithEmbeddedMedia?: Maybe<Body>
  createdAt?: Maybe<Scalars['String']['output']>
  id: Scalars['Int']['output']
  published?: Maybe<Scalars['Boolean']['output']>
  teaserHeadline?: Maybe<Scalars['String']['output']>
  title: Scalars['String']['output']
  updatedAt?: Maybe<Scalars['String']['output']>
  urlAlias?: Maybe<Scalars['String']['output']>
}

export type Body = {
  __typename?: 'Body'
  embeddedMedia?: Maybe<Array<Maybe<EmbeddedMedia>>>
  value?: Maybe<Scalars['String']['output']>
}

export type BreakingNews = NodeInterface & {
  __typename?: 'BreakingNews'
  byline?: Maybe<Scalars['String']['output']>
  category?: Maybe<Scalars['String']['output']>
  createdAt?: Maybe<Scalars['String']['output']>
  expirationTime?: Maybe<Scalars['String']['output']>
  id: Scalars['Int']['output']
  published?: Maybe<Scalars['Boolean']['output']>
  teaserHeadline?: Maybe<Scalars['String']['output']>
  title: Scalars['String']['output']
  updatedAt?: Maybe<Scalars['String']['output']>
  url?: Maybe<Scalars['String']['output']>
}

export enum BundleType {
  BasicPage = 'BasicPage',
  BreakingNews = 'BreakingNews',
  Commentary = 'Commentary',
  LeadGen = 'LeadGen',
  NewsArticle = 'NewsArticle',
  OffTheWire = 'OffTheWire',
  PressRelease = 'PressRelease',
  Sponsored = 'Sponsored',
  StreetTalk = 'StreetTalk',
}

export type Category = {
  __typename?: 'Category'
  children?: Maybe<Array<Maybe<Category>>>
  description?: Maybe<Scalars['String']['output']>
  id: Scalars['Int']['output']
  imageUrl?: Maybe<Scalars['String']['output']>
  name: Scalars['String']['output']
  status?: Maybe<Scalars['Boolean']['output']>
  urlAlias: Scalars['String']['output']
  weight?: Maybe<Scalars['Int']['output']>
}

export type CategoryMenu = {
  __typename?: 'CategoryMenu'
  children?: Maybe<Array<Maybe<Category>>>
  isParent?: Maybe<Scalars['Boolean']['output']>
  parent?: Maybe<Category>
  self?: Maybe<Category>
}

export enum ChartId {
  OneYear = 'ONE_YEAR',
  SixtyDays = 'SIXTY_DAYS',
  SixMonths = 'SIX_MONTHS',
  ThirtyDays = 'THIRTY_DAYS',
  Today = 'TODAY',
}

export type Commentary = NodeInterface & {
  __typename?: 'Commentary'
  /** @deprecated No longer supported */
  allowComments?: Maybe<Scalars['Boolean']['output']>
  audioSnippet?: Maybe<AudioSnippet>
  audioTts?: Maybe<AudioTts>
  author?: Maybe<Author>
  /** @deprecated Use bodyWithEmbeddedMedia instead */
  body?: Maybe<Scalars['String']['output']>
  bodyWithEmbeddedMedia?: Maybe<Body>
  category?: Maybe<Category>
  createdAt?: Maybe<Scalars['String']['output']>
  featured?: Maybe<Scalars['Boolean']['output']>
  featuredContent?: Maybe<EmbeddedMedia>
  id: Scalars['Int']['output']
  image?: Maybe<Image>
  label?: Maybe<Label>
  /** @deprecated No longer supported */
  legacyThumbnailImageUrl?: Maybe<Scalars['String']['output']>
  opinionType?: Maybe<OpinionType>
  published?: Maybe<Scalars['Boolean']['output']>
  source?: Maybe<Source>
  summaryBullets?: Maybe<Array<Maybe<Scalars['String']['output']>>>
  supportingAuthors?: Maybe<Array<Maybe<Author>>>
  tags?: Maybe<Array<Maybe<Tag>>>
  teaserHeadline?: Maybe<Scalars['String']['output']>
  teaserImage?: Maybe<Image>
  teaserSnippet?: Maybe<Scalars['String']['output']>
  title: Scalars['String']['output']
  updatedAt?: Maybe<Scalars['String']['output']>
  urlAlias?: Maybe<Scalars['String']['output']>
  videoSnippet?: Maybe<VideoSnippet>
}

export type Company = {
  __typename?: 'Company'
  ID: Scalars['String']['output']
  Name: Scalars['String']['output']
  Symbol: Scalars['String']['output']
}

export type Crypto = {
  __typename?: 'Crypto'
  ID: Scalars['Int']['output']
  currency: Scalars['String']['output']
  results?: Maybe<Array<Maybe<Quote>>>
  symbol: Scalars['String']['output']
}

export type CryptoComparePriceFull = {
  __typename?: 'CryptoComparePriceFull'
  change7Day: Scalars['Float']['output']
  change24Hour: Scalars['Float']['output']
  changeDay: Scalars['Float']['output']
  changeHour: Scalars['Float']['output']
  changePct7DayCalculated: Scalars['Float']['output']
  changePct24Hour: Scalars['Float']['output']
  changePct24HourCalculated: Scalars['Float']['output']
  changePctDay: Scalars['Float']['output']
  changePctHour: Scalars['Float']['output']
  changePctHourCalculated: Scalars['Float']['output']
  circulatingSupply: Scalars['Float']['output']
  circulatingSupplyMktCap: Scalars['Float']['output']
  conversionSymbol: Scalars['String']['output']
  conversionType: Scalars['String']['output']
  flags: Scalars['String']['output']
  fromSymbol: Scalars['String']['output']
  high24Hour: Scalars['Float']['output']
  highDay: Scalars['Float']['output']
  highHour: Scalars['Float']['output']
  imageUrl: Scalars['String']['output']
  lastMarket: Scalars['String']['output']
  lastTradeId: Scalars['String']['output']
  lastUpdate: Scalars['Int']['output']
  lastVolume: Scalars['Float']['output']
  lastVolumeTo: Scalars['Float']['output']
  low24Hour: Scalars['Float']['output']
  lowDay: Scalars['Float']['output']
  lowHour: Scalars['Float']['output']
  market: Scalars['String']['output']
  median: Scalars['Float']['output']
  mktCap: Scalars['Float']['output']
  mktCapPenalty: Scalars['Float']['output']
  open24Hour: Scalars['Float']['output']
  openDay: Scalars['Float']['output']
  openHour: Scalars['Float']['output']
  price: Scalars['Float']['output']
  supply: Scalars['Float']['output']
  toSymbol: Scalars['String']['output']
  topTierVolume24Hour: Scalars['Float']['output']
  topTierVolume24HourTo: Scalars['Float']['output']
  totalTopTierVolume24h: Scalars['Float']['output']
  totalTopTierVolume24hTo: Scalars['Float']['output']
  totalVolume24h: Scalars['Float']['output']
  totalVolume24hTo: Scalars['Float']['output']
  type: Scalars['String']['output']
  volume24Hour: Scalars['Float']['output']
  volume24HourTo: Scalars['Float']['output']
  volumeDay: Scalars['Float']['output']
  volumeDayTo: Scalars['Float']['output']
  volumeHour: Scalars['Float']['output']
  volumeHourTo: Scalars['Float']['output']
}

export enum Day {
  Today = 'Today',
  Yesterday = 'Yesterday',
}

export type EmbeddedMedia = {
  __typename?: 'EmbeddedMedia'
  assetUuid?: Maybe<Scalars['String']['output']>
  endTime?: Maybe<Scalars['Int']['output']>
  snippetUuid?: Maybe<Scalars['String']['output']>
  startTime?: Maybe<Scalars['Int']['output']>
  status?: Maybe<Scalars['Boolean']['output']>
  thumbnailUuid?: Maybe<Scalars['String']['output']>
  type?: Maybe<Scalars['String']['output']>
}

export type Equity = {
  __typename?: 'Equity'
  Category?: Maybe<Array<Scalars['String']['output']>>
  Change: Scalars['Float']['output']
  ChangePercentage: Scalars['Float']['output']
  Currency: Scalars['String']['output']
  Exchange: Scalars['String']['output']
  High: Scalars['Float']['output']
  ID: Scalars['Int']['output']
  Low: Scalars['Float']['output']
  Name: Scalars['String']['output']
  Price: Scalars['Float']['output']
  ProviderTime: Scalars['String']['output']
  Symbol: Scalars['String']['output']
  SymbolURL: Scalars['String']['output']
  TVExchange: Scalars['String']['output']
  TVSymbol: Scalars['String']['output']
  Timestamp: Scalars['String']['output']
  Volume: Scalars['Float']['output']
}

export type ExchangeRate = {
  __typename?: 'ExchangeRate'
  ChangePercent: Scalars['Float']['output']
  Currency: Scalars['String']['output']
  CurrencyToUsd: Scalars['Float']['output']
  NYTime: Scalars['String']['output']
  UsdToCurrency: Scalars['Float']['output']
}

export type ExitModal = {
  __typename?: 'ExitModal'
  active?: Maybe<Scalars['String']['output']>
  backgroundImage?: Maybe<Scalars['String']['output']>
  buttonColor?: Maybe<Scalars['String']['output']>
  subTitle?: Maybe<Scalars['String']['output']>
  subTitleColor?: Maybe<Scalars['String']['output']>
  title?: Maybe<Scalars['String']['output']>
  titleColor?: Maybe<Scalars['String']['output']>
}

export type Faq = {
  __typename?: 'FAQ'
  answer: Scalars['String']['output']
  question: Scalars['String']['output']
}

export type Forex = {
  __typename?: 'Forex'
  ID: Scalars['Int']['output']
  results?: Maybe<Array<Maybe<ForexQuote>>>
  symbol: Scalars['String']['output']
}

export type ForexQuote = {
  __typename?: 'ForexQuote'
  ID: Scalars['Int']['output']
  ask: Scalars['Float']['output']
  bid: Scalars['Float']['output']
  change: Scalars['Float']['output']
  changePercentage: Scalars['Float']['output']
  ctousd: Scalars['Float']['output']
  mid: Scalars['Float']['output']
  nytime: Scalars['String']['output']
  symbol: Scalars['String']['output']
  time: Scalars['String']['output']
  timestamp: Scalars['Int']['output']
  usdtoc: Scalars['Float']['output']
}

export type Guest = {
  __typename?: 'Guest'
  /** @deprecated No longer supported */
  firstName: Scalars['String']['output']
  /** @deprecated No longer supported */
  fullName: Scalars['String']['output']
  id: Scalars['Int']['output']
  /** @deprecated No longer supported */
  lastName: Scalars['String']['output']
  name?: Maybe<Scalars['String']['output']>
  urlAlias?: Maybe<Scalars['String']['output']>
}

export type HistoricalPoints = {
  __typename?: 'HistoricalPoints'
  ID: Scalars['Int']['output']
  currency: Scalars['String']['output']
  fiveYear: Quote
  now: Quote
  oneYear: Quote
  sixtyDay: Quote
  symbol: Scalars['String']['output']
  thirtyDay: Quote
  timestamp: Scalars['Int']['output']
}

export type Image = {
  __typename?: 'Image'
  detail?: Maybe<ImageDetail>
  teaser?: Maybe<Scalars['String']['output']>
}

export type ImageDetail = {
  __typename?: 'ImageDetail'
  default?: Maybe<SourceAttribute>
  sources?: Maybe<ImageDetailSources>
}

export type ImageDetailSources = {
  __typename?: 'ImageDetailSources'
  desktop?: Maybe<SourceAttribute>
  mobile?: Maybe<SourceAttribute>
  tablet?: Maybe<SourceAttribute>
  teaser_medium?: Maybe<SourceAttribute>
  teaser_small?: Maybe<SourceAttribute>
}

export type Label = {
  __typename?: 'Label'
  backgroundColor?: Maybe<Scalars['String']['output']>
  id?: Maybe<Scalars['Int']['output']>
  imageUrl?: Maybe<Scalars['String']['output']>
  name?: Maybe<Scalars['String']['output']>
  textColor?: Maybe<Scalars['String']['output']>
  type?: Maybe<Scalars['String']['output']>
}

export type LeadGen = NodeInterface & {
  __typename?: 'LeadGen'
  author?: Maybe<Author>
  body?: Maybe<Scalars['String']['output']>
  createdAt?: Maybe<Scalars['String']['output']>
  featured?: Maybe<Scalars['Boolean']['output']>
  id: Scalars['Int']['output']
  published?: Maybe<Scalars['Boolean']['output']>
  title: Scalars['String']['output']
  updatedAt?: Maybe<Scalars['String']['output']>
  urlAlias?: Maybe<Scalars['String']['output']>
}

export type LiveSpotGoldPrice = {
  __typename?: 'LiveSpotGoldPrice'
  Change: Scalars['Float']['output']
  ChangePercent: Scalars['Float']['output']
  Price: Scalars['Float']['output']
}

export type LiveSpotGoldRow = {
  __typename?: 'LiveSpotGoldRow'
  Currency: Scalars['String']['output']
  Gold: LiveSpotGoldPrice
  Rate: ExchangeRate
}

export type LiveSpotGoldTable = {
  __typename?: 'LiveSpotGoldTable'
  ID: Scalars['String']['output']
  Symbol: Scalars['String']['output']
  Table?: Maybe<Array<Maybe<LiveSpotGoldRow>>>
}

export type LondonFix = {
  __typename?: 'LondonFix'
  ID: Scalars['Int']['output']
  currency: Scalars['String']['output']
  endTime: Scalars['Int']['output']
  results?: Maybe<Array<Maybe<LondonQuote>>>
  startTime: Scalars['Int']['output']
}

export type LondonQuote = {
  __typename?: 'LondonQuote'
  ID: Scalars['Int']['output']
  currency: Scalars['String']['output']
  extra?: Maybe<Scalars['String']['output']>
  goldAM: Scalars['Float']['output']
  goldPM: Scalars['Float']['output']
  palladiumAM: Scalars['Float']['output']
  palladiumPM: Scalars['Float']['output']
  platinumAM: Scalars['Float']['output']
  platinumPM: Scalars['Float']['output']
  rates?: Maybe<Array<QuoteRatePair>>
  silver: Scalars['Float']['output']
  time: Scalars['String']['output']
  timestamp: Scalars['Int']['output']
}

export type MarketStatus = {
  __typename?: 'MarketStatus'
  next: Scalars['Int']['output']
  status: Scalars['String']['output']
}

export type Metal = {
  __typename?: 'Metal'
  ID: Scalars['Int']['output']
  currency: Scalars['String']['output']
  name: Scalars['String']['output']
  results?: Maybe<Array<Maybe<Quote>>>
  symbol: Scalars['String']['output']
}

export type Mutation = {
  __typename?: 'Mutation'
  AudioCancelUpload?: Maybe<Scalars['Boolean']['output']>
  VideoCancelUpload?: Maybe<Scalars['Boolean']['output']>
  VideoQueueworkerUpdateVideo?: Maybe<Scalars['Boolean']['output']>
  VideoReorderCategory?: Maybe<Scalars['Int']['output']>
  VideoResetIsExported?: Maybe<Scalars['Boolean']['output']>
  VideoUpdateCategoryDisplay?: Maybe<Scalars['Int']['output']>
}

export type MutationAudioCancelUploadArgs = {
  jobId?: InputMaybe<Scalars['String']['input']>
  queue?: InputMaybe<Scalars['String']['input']>
}

export type MutationVideoCancelUploadArgs = {
  jobId?: InputMaybe<Scalars['String']['input']>
  queue?: InputMaybe<Scalars['String']['input']>
}

export type MutationVideoQueueworkerUpdateVideoArgs = {
  input: Scalars['String']['input']
}

export type MutationVideoReorderCategoryArgs = {
  input?: InputMaybe<VideoReorderCategoryInput>
}

export type MutationVideoResetIsExportedArgs = {
  uuid: Scalars['String']['input']
}

export type MutationVideoUpdateCategoryDisplayArgs = {
  input?: InputMaybe<VideoUpdateCategoryDisplayInput>
}

export type NewsArticle = NodeInterface & {
  __typename?: 'NewsArticle'
  /** @deprecated No longer supported */
  allowComments?: Maybe<Scalars['Boolean']['output']>
  audioSnippet?: Maybe<AudioSnippet>
  audioTts?: Maybe<AudioTts>
  author?: Maybe<Author>
  /** @deprecated Use bodyWithEmbeddedMedia instead */
  body?: Maybe<Scalars['String']['output']>
  bodyWithEmbeddedMedia?: Maybe<Body>
  category?: Maybe<Category>
  createdAt?: Maybe<Scalars['String']['output']>
  featuredContent?: Maybe<EmbeddedMedia>
  id: Scalars['Int']['output']
  image?: Maybe<Image>
  label?: Maybe<Label>
  /** @deprecated No longer supported */
  legacyThumbnailImageUrl?: Maybe<Scalars['String']['output']>
  published?: Maybe<Scalars['Boolean']['output']>
  source?: Maybe<Source>
  summaryBullets?: Maybe<Array<Maybe<Scalars['String']['output']>>>
  supportingAuthors?: Maybe<Array<Maybe<Author>>>
  tags?: Maybe<Array<Maybe<Tag>>>
  teaserHeadline?: Maybe<Scalars['String']['output']>
  teaserImage?: Maybe<Image>
  teaserSnippet?: Maybe<Scalars['String']['output']>
  title: Scalars['String']['output']
  updatedAt?: Maybe<Scalars['String']['output']>
  urlAlias?: Maybe<Scalars['String']['output']>
  videoSnippet?: Maybe<VideoSnippet>
}

export type NodeInterface = {
  createdAt?: Maybe<Scalars['String']['output']>
  id: Scalars['Int']['output']
  published?: Maybe<Scalars['Boolean']['output']>
  title: Scalars['String']['output']
  updatedAt?: Maybe<Scalars['String']['output']>
}

export type NodeListSearchWithPagination = {
  __typename?: 'NodeListSearchWithPagination'
  items?: Maybe<Array<SearchResult>>
  total: Scalars['Int']['output']
}

export type NodeListWithPagination = {
  __typename?: 'NodeListWithPagination'
  items?: Maybe<Array<NodeInterface>>
  total: Scalars['Int']['output']
}

export type OffTheWire = NodeInterface & {
  __typename?: 'OffTheWire'
  audioTts?: Maybe<AudioTts>
  author?: Maybe<Author>
  /** @deprecated Use bodyWithEmbeddedMedia instead */
  body?: Maybe<Scalars['String']['output']>
  bodyWithEmbeddedMedia?: Maybe<Body>
  category?: Maybe<Category>
  createdAt?: Maybe<Scalars['String']['output']>
  featured?: Maybe<Scalars['Boolean']['output']>
  featuredContent?: Maybe<EmbeddedMedia>
  id: Scalars['Int']['output']
  image?: Maybe<Image>
  /** @deprecated Use image instead */
  imageUrl?: Maybe<Scalars['String']['output']>
  label?: Maybe<Label>
  /** @deprecated No longer supported */
  legacyThumbnailImageUrl?: Maybe<Scalars['String']['output']>
  published?: Maybe<Scalars['Boolean']['output']>
  source?: Maybe<Source>
  summaryBullets?: Maybe<Array<Maybe<Scalars['String']['output']>>>
  supportingAuthors?: Maybe<Array<Maybe<Author>>>
  tags?: Maybe<Array<Maybe<Tag>>>
  teaserHeadline?: Maybe<Scalars['String']['output']>
  teaserImage?: Maybe<Image>
  teaserSnippet?: Maybe<Scalars['String']['output']>
  title: Scalars['String']['output']
  updatedAt?: Maybe<Scalars['String']['output']>
  urlAlias?: Maybe<Scalars['String']['output']>
}

export enum OpinionType {
  Corner = 'CORNER',
  Exclusive = 'EXCLUSIVE',
  Kitco = 'KITCO',
}

export type PreExecuteNodeListWithPagination = {
  __typename?: 'PreExecuteNodeListWithPagination'
  items?: Maybe<Array<NodeInterface>>
  total: Scalars['Int']['output']
}

export type PressRelease = NodeInterface & {
  __typename?: 'PressRelease'
  author?: Maybe<Author>
  createdAt?: Maybe<Scalars['String']['output']>
  id: Scalars['Int']['output']
  published?: Maybe<Scalars['Boolean']['output']>
  teaserHeadline?: Maybe<Scalars['String']['output']>
  title: Scalars['String']['output']
  updatedAt?: Maybe<Scalars['String']['output']>
  url: Scalars['String']['output']
}

export type PricePageFaQs = {
  __typename?: 'PricePageFAQs'
  faqs: Array<Maybe<Faq>>
  id: Scalars['Int']['output']
  name: Scalars['String']['output']
  urlAlias: Scalars['String']['output']
}

export type Query = {
  __typename?: 'Query'
  AudioGetSnippet?: Maybe<AudioSnippet>
  GetBarchartFuturesByExchange?: Maybe<BarchartGetFuturesByExchange>
  GetBarchartLeaders?: Maybe<BarchartGetLeaders>
  GetBarchartQuotes?: Maybe<BarchartGetQuote>
  GetCompany?: Maybe<Array<Maybe<Company>>>
  GetCompanyV3?: Maybe<Array<Maybe<Company>>>
  GetCompanyV3Hades?: Maybe<Array<Maybe<Company>>>
  GetCompanyV3Kds2?: Maybe<Array<Maybe<Company>>>
  GetCryptoComparePriceFull?: Maybe<Array<Maybe<CryptoComparePriceFull>>>
  GetCryptoComparePriceFullV3?: Maybe<Array<Maybe<CryptoComparePriceFull>>>
  GetCryptoHistory?: Maybe<Array<Maybe<Crypto>>>
  GetCryptoHistoryV3?: Maybe<Array<Maybe<Crypto>>>
  GetCryptoHistoryV3Hades?: Maybe<Array<Maybe<Crypto>>>
  GetCryptoHistoryV3Kds2?: Maybe<Array<Maybe<Crypto>>>
  GetCryptoQuote?: Maybe<Crypto>
  GetCryptoQuoteV3?: Maybe<Crypto>
  GetEquities?: Maybe<Array<Maybe<Equity>>>
  GetForexQuote?: Maybe<Forex>
  GetForexQuoteV3?: Maybe<Forex>
  GetForexQuoteV3Hades?: Maybe<Forex>
  GetForexQuoteV3Kds2?: Maybe<Forex>
  GetHistoricalPoints?: Maybe<HistoricalPoints>
  GetHistoricalPointsV3?: Maybe<HistoricalPoints>
  GetHistoricalPointsV3Hades?: Maybe<HistoricalPoints>
  GetHistoricalPointsV3Kds2?: Maybe<HistoricalPoints>
  GetKitcoFixPreciousMetalsV3?: Maybe<Metal>
  GetKitcoFixPreciousMetalsV3Hades?: Maybe<Metal>
  GetKitcoFixPreciousMetalsV3Kds2?: Maybe<Metal>
  GetLiveSpotGoldTable?: Maybe<LiveSpotGoldTable>
  GetLiveSpotGoldTableV3?: Maybe<LiveSpotGoldTable>
  GetLiveSpotGoldTableV3Hades?: Maybe<LiveSpotGoldTable>
  GetLiveSpotGoldTableV3Kds2?: Maybe<LiveSpotGoldTable>
  GetLondonFix?: Maybe<LondonFix>
  GetLondonFixByYear?: Maybe<LondonFix>
  GetLondonFixByYearV3?: Maybe<LondonFix>
  GetLondonFixByYearV3Hades?: Maybe<LondonFix>
  GetLondonFixByYearV3Kds2?: Maybe<LondonFix>
  GetLondonFixV3?: Maybe<LondonFix>
  GetLondonFixV3Hades?: Maybe<LondonFix>
  GetLondonFixV3Kds2?: Maybe<LondonFix>
  GetMarketStatus?: Maybe<MarketStatus>
  GetMetalChartData?: Maybe<Metal>
  GetMetalChartDataV3?: Maybe<Metal>
  GetMetalChartDataV3Hades?: Maybe<Metal>
  GetMetalChartDataV3Kds2?: Maybe<Metal>
  GetMetalHistory?: Maybe<Metal>
  GetMetalHistoryV3?: Maybe<Metal>
  GetMetalHistoryV3Hades?: Maybe<Metal>
  GetMetalHistoryV3Kds2?: Maybe<Metal>
  GetMetalQuote?: Maybe<Metal>
  GetMetalQuoteV3?: Maybe<Metal>
  GetMetalQuoteV3Hades?: Maybe<Metal>
  GetMetalQuoteV3Kds2?: Maybe<Metal>
  GetShanghaiFix?: Maybe<ShanghaiFix>
  GetShanghaiFixByYear?: Maybe<ShanghaiFix>
  GetShanghaiFixByYearV3?: Maybe<ShanghaiFix>
  GetShanghaiFixV3?: Maybe<ShanghaiFix>
  GetStock?: Maybe<Array<Maybe<Stock>>>
  GetStockV3?: Maybe<Array<Maybe<Stock>>>
  GetStockV3Hades?: Maybe<Array<Maybe<Stock>>>
  GetStockV3Kds2?: Maybe<Array<Maybe<Stock>>>
  VideoConsumerCategories?: Maybe<Array<Maybe<VideoVcmsCategory>>>
  VideoConsumerCategoryById?: Maybe<VideoVcmsCategory>
  VideoConsumerFeed?: Maybe<VideoFeed>
  VideoConsumerSnippetBySlug?: Maybe<VideoVcmsSnippet>
  VideoConsumerVideoById?: Maybe<VideoVcmsVideo>
  VideoExternalGetVideoByAlias?: Maybe<VideoExternalGetVideoByAlias>
  VideoGetAllByCategoryUrlAlias?: Maybe<VideoCategorySnippets>
  VideoGetCategories?: Maybe<Array<Maybe<VideoCategory>>>
  VideoGetSnippet?: Maybe<VideoSnippet>
  VideoLatestVideos?: Maybe<VideoSearchSnippetsResponse>
  VideoSearchSnippets?: Maybe<VideoSearchSnippetsResponse>
  VideoVideosListPageExternal?: Maybe<VideoVideosListPageExternal>
  authorByUrlAlias?: Maybe<Author>
  categoriesTree?: Maybe<Array<Maybe<Category>>>
  categoryByUrlAlias?: Maybe<Category>
  /** @deprecated No longer supported */
  categoryChildrenByUrlAlias?: Maybe<Array<Maybe<Category>>>
  /** @deprecated Use categoriesTree instead */
  categoryMenu?: Maybe<CategoryMenu>
  commentaryListQueue?: Maybe<PreExecuteNodeListWithPagination>
  exitModal?: Maybe<ExitModal>
  faqsByUrlAlias?: Maybe<PricePageFaQs>
  guestByUrlAlias?: Maybe<Guest>
  latestNewsQueue?: Maybe<PreExecuteNodeListWithPagination>
  marketNews?: Maybe<NodeListWithPagination>
  nodeByUrlAlias?: Maybe<NodeInterface>
  nodeIdsInQueue?: Maybe<Array<Maybe<Scalars['Int']['output']>>>
  nodeList?: Maybe<NodeListWithPagination>
  nodeListByAuthor?: Maybe<NodeListWithPagination>
  nodeListByCategory?: Maybe<PreExecuteNodeListWithPagination>
  nodeListByGuest?: Maybe<NodeListWithPagination>
  nodeListBySponsor?: Maybe<NodeListWithPagination>
  nodeListByTag?: Maybe<NodeListWithPagination>
  nodeListNewsFeed?: Maybe<PreExecuteNodeListWithPagination>
  nodeListQueue?: Maybe<PreExecuteNodeListWithPagination>
  nodeListTrending?: Maybe<Array<Maybe<NodeInterface>>>
  queueListByUrlAlias?: Maybe<Array<Maybe<Scalars['String']['output']>>>
  reporters?: Maybe<Array<Maybe<Author>>>
  search?: Maybe<NodeListSearchWithPagination>
  sponsorByUrlAlias?: Maybe<Sponsor>
  tagByUrlAlias?: Maybe<Tag>
  topContributors?: Maybe<Array<Maybe<Author>>>
  trendingTags?: Maybe<Array<Maybe<Tag>>>
}

export type QueryAudioGetSnippetArgs = {
  uuid?: InputMaybe<Scalars['String']['input']>
}

export type QueryGetBarchartFuturesByExchangeArgs = {
  category?: InputMaybe<Scalars['String']['input']>
  exchange: Scalars['String']['input']
  fields?: InputMaybe<Scalars['String']['input']>
  timestamp?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetBarchartLeadersArgs = {
  assetType: Scalars['String']['input']
  exchanges?: InputMaybe<Scalars['String']['input']>
  leaderboardType: Scalars['String']['input']
  maxRecords?: InputMaybe<Scalars['Int']['input']>
  period?: InputMaybe<Scalars['String']['input']>
  sortDirection?: InputMaybe<Scalars['String']['input']>
  timestamp?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetBarchartQuotesArgs = {
  symbols: Scalars['String']['input']
  timestamp: Scalars['Int']['input']
}

export type QueryGetCryptoComparePriceFullArgs = {
  currency?: Scalars['String']['input']
  symbols: Scalars['String']['input']
}

export type QueryGetCryptoComparePriceFullV3Args = {
  currency?: Scalars['String']['input']
  symbols: Scalars['String']['input']
}

export type QueryGetCryptoHistoryArgs = {
  compareToTime?: InputMaybe<Scalars['String']['input']>
  currency?: Scalars['String']['input']
  endTime?: InputMaybe<Scalars['Int']['input']>
  groupBy?: Scalars['String']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  startTime: Scalars['Int']['input']
  symbol: Scalars['String']['input']
}

export type QueryGetCryptoHistoryV3Args = {
  compareToTime?: InputMaybe<Scalars['String']['input']>
  currency?: Scalars['String']['input']
  endTime?: InputMaybe<Scalars['Int']['input']>
  groupBy?: Scalars['String']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  startTime: Scalars['Int']['input']
  symbol: Scalars['String']['input']
}

export type QueryGetCryptoHistoryV3HadesArgs = {
  compareToTime?: InputMaybe<Scalars['String']['input']>
  currency?: Scalars['String']['input']
  endTime?: InputMaybe<Scalars['Int']['input']>
  groupBy?: Scalars['String']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  startTime: Scalars['Int']['input']
  symbol: Scalars['String']['input']
}

export type QueryGetCryptoHistoryV3Kds2Args = {
  compareToTime?: InputMaybe<Scalars['String']['input']>
  currency?: Scalars['String']['input']
  endTime?: InputMaybe<Scalars['Int']['input']>
  groupBy?: Scalars['String']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  startTime: Scalars['Int']['input']
  symbol: Scalars['String']['input']
}

export type QueryGetCryptoQuoteArgs = {
  compareToTime?: InputMaybe<Scalars['String']['input']>
  currency?: Scalars['String']['input']
  symbol: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetCryptoQuoteV3Args = {
  compareToTime?: InputMaybe<Scalars['String']['input']>
  currency?: Scalars['String']['input']
  symbol: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetForexQuoteArgs = {
  symbol: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetForexQuoteV3Args = {
  symbol: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetForexQuoteV3HadesArgs = {
  symbol: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetForexQuoteV3Kds2Args = {
  symbol: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetHistoricalPointsArgs = {
  currency: Scalars['String']['input']
  symbol: Scalars['String']['input']
  timestamp: Scalars['Int']['input']
}

export type QueryGetHistoricalPointsV3Args = {
  currency: Scalars['String']['input']
  symbol: Scalars['String']['input']
  timestamp: Scalars['Int']['input']
}

export type QueryGetHistoricalPointsV3HadesArgs = {
  currency: Scalars['String']['input']
  symbol: Scalars['String']['input']
  timestamp: Scalars['Int']['input']
}

export type QueryGetHistoricalPointsV3Kds2Args = {
  currency: Scalars['String']['input']
  symbol: Scalars['String']['input']
  timestamp: Scalars['Int']['input']
}

export type QueryGetKitcoFixPreciousMetalsV3Args = {
  currency?: Scalars['String']['input']
  symbol: Scalars['String']['input']
  year: Scalars['Int']['input']
}

export type QueryGetKitcoFixPreciousMetalsV3HadesArgs = {
  currency?: Scalars['String']['input']
  symbol: Scalars['String']['input']
  year: Scalars['Int']['input']
}

export type QueryGetKitcoFixPreciousMetalsV3Kds2Args = {
  currency?: Scalars['String']['input']
  symbol: Scalars['String']['input']
  year: Scalars['Int']['input']
}

export type QueryGetLondonFixArgs = {
  currency?: Scalars['String']['input']
  endTime: Scalars['Int']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  startTime: Scalars['Int']['input']
}

export type QueryGetLondonFixByYearArgs = {
  currency?: InputMaybe<Scalars['String']['input']>
  year: Scalars['String']['input']
}

export type QueryGetLondonFixByYearV3Args = {
  currency?: InputMaybe<Scalars['String']['input']>
  year: Scalars['String']['input']
}

export type QueryGetLondonFixByYearV3HadesArgs = {
  currency?: InputMaybe<Scalars['String']['input']>
  year: Scalars['String']['input']
}

export type QueryGetLondonFixByYearV3Kds2Args = {
  currency?: InputMaybe<Scalars['String']['input']>
  year: Scalars['String']['input']
}

export type QueryGetLondonFixV3Args = {
  currency?: Scalars['String']['input']
  endTime: Scalars['Int']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  startTime: Scalars['Int']['input']
}

export type QueryGetLondonFixV3HadesArgs = {
  currency?: Scalars['String']['input']
  endTime: Scalars['Int']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  startTime: Scalars['Int']['input']
}

export type QueryGetLondonFixV3Kds2Args = {
  currency?: Scalars['String']['input']
  endTime: Scalars['Int']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  startTime: Scalars['Int']['input']
}

export type QueryGetMetalChartDataArgs = {
  chartId: ChartId
  currency?: InputMaybe<Scalars['String']['input']>
  symbol: Scalars['String']['input']
}

export type QueryGetMetalChartDataV3Args = {
  chartId: ChartId
  currency?: InputMaybe<Scalars['String']['input']>
  symbol: Scalars['String']['input']
}

export type QueryGetMetalChartDataV3HadesArgs = {
  chartId: ChartId
  currency?: InputMaybe<Scalars['String']['input']>
  symbol: Scalars['String']['input']
}

export type QueryGetMetalChartDataV3Kds2Args = {
  chartId: ChartId
  currency?: InputMaybe<Scalars['String']['input']>
  symbol: Scalars['String']['input']
}

export type QueryGetMetalHistoryArgs = {
  currency?: Scalars['String']['input']
  endTime?: InputMaybe<Scalars['Int']['input']>
  groupBy?: InputMaybe<Scalars['String']['input']>
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  startTime: Scalars['Int']['input']
  symbol: Scalars['String']['input']
  unit?: InputMaybe<Scalars['String']['input']>
}

export type QueryGetMetalHistoryV3Args = {
  currency?: Scalars['String']['input']
  endTime?: InputMaybe<Scalars['Int']['input']>
  groupBy?: InputMaybe<Scalars['String']['input']>
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  startTime: Scalars['Int']['input']
  symbol: Scalars['String']['input']
  unit?: InputMaybe<Scalars['String']['input']>
}

export type QueryGetMetalHistoryV3HadesArgs = {
  currency?: Scalars['String']['input']
  endTime?: InputMaybe<Scalars['Int']['input']>
  groupBy?: InputMaybe<Scalars['String']['input']>
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  startTime: Scalars['Int']['input']
  symbol: Scalars['String']['input']
  unit?: InputMaybe<Scalars['String']['input']>
}

export type QueryGetMetalHistoryV3Kds2Args = {
  currency?: Scalars['String']['input']
  endTime?: InputMaybe<Scalars['Int']['input']>
  groupBy?: InputMaybe<Scalars['String']['input']>
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  startTime: Scalars['Int']['input']
  symbol: Scalars['String']['input']
  unit?: InputMaybe<Scalars['String']['input']>
}

export type QueryGetMetalQuoteArgs = {
  currency?: Scalars['String']['input']
  symbol: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetMetalQuoteV3Args = {
  currency?: Scalars['String']['input']
  symbol: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetMetalQuoteV3HadesArgs = {
  currency?: Scalars['String']['input']
  symbol: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetMetalQuoteV3Kds2Args = {
  currency?: Scalars['String']['input']
  symbol: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetShanghaiFixArgs = {
  currency?: Scalars['String']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  symbol: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}

export type QueryGetShanghaiFixByYearArgs = {
  year: Scalars['String']['input']
}

export type QueryGetShanghaiFixByYearV3Args = {
  year: Scalars['String']['input']
}

export type QueryGetShanghaiFixV3Args = {
  currency?: Scalars['String']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  symbol: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}

export type QueryVideoConsumerCategoryByIdArgs = {
  id: Scalars['Int']['input']
}

export type QueryVideoConsumerFeedArgs = {
  latest?: InputMaybe<Scalars['Boolean']['input']>
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  upNext?: InputMaybe<Scalars['Boolean']['input']>
}

export type QueryVideoConsumerSnippetBySlugArgs = {
  slug: Scalars['String']['input']
}

export type QueryVideoConsumerVideoByIdArgs = {
  id: Scalars['Int']['input']
}

export type QueryVideoExternalGetVideoByAliasArgs = {
  urlAlias: Scalars['String']['input']
}

export type QueryVideoGetAllByCategoryUrlAliasArgs = {
  urlAlias?: InputMaybe<Scalars['String']['input']>
}

export type QueryVideoGetSnippetArgs = {
  uuid?: InputMaybe<Scalars['String']['input']>
}

export type QueryVideoLatestVideosArgs = {
  limit: Scalars['Int']['input']
  offset: Scalars['Int']['input']
}

export type QueryVideoSearchSnippetsArgs = {
  categoryId?: InputMaybe<Scalars['Int']['input']>
  dateEnd?: InputMaybe<Scalars['String']['input']>
  dateStart?: InputMaybe<Scalars['String']['input']>
  isArchived?: InputMaybe<Scalars['Boolean']['input']>
  isChild?: InputMaybe<Scalars['Boolean']['input']>
  isParent?: InputMaybe<Scalars['Boolean']['input']>
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  query?: InputMaybe<Scalars['String']['input']>
  sort?: InputMaybe<VideoSnippetSearchSort>
  status?: InputMaybe<Scalars['Int']['input']>
  textFields?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>
}

export type QueryAuthorByUrlAliasArgs = {
  urlAlias: Scalars['String']['input']
}

export type QueryCategoryByUrlAliasArgs = {
  urlAlias: Scalars['String']['input']
}

export type QueryCategoryChildrenByUrlAliasArgs = {
  urlAlias: Scalars['String']['input']
}

export type QueryCategoryMenuArgs = {
  urlAlias: Scalars['String']['input']
}

export type QueryCommentaryListQueueArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  opinionType: OpinionType
  queueId: Scalars['String']['input']
}

export type QueryFaqsByUrlAliasArgs = {
  urlAlias: Scalars['String']['input']
}

export type QueryGuestByUrlAliasArgs = {
  urlAlias: Scalars['String']['input']
}

export type QueryLatestNewsQueueArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}

export type QueryMarketNewsArgs = {
  day?: InputMaybe<Day>
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}

export type QueryNodeByUrlAliasArgs = {
  auHash?: InputMaybe<Scalars['String']['input']>
  urlAlias: Scalars['String']['input']
}

export type QueryNodeIdsInQueueArgs = {
  queueId: Scalars['String']['input']
}

export type QueryNodeListArgs = {
  bundles?: InputMaybe<Array<InputMaybe<BundleType>>>
  day?: InputMaybe<Day>
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}

export type QueryNodeListByAuthorArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  urlAlias: Scalars['String']['input']
}

export type QueryNodeListByCategoryArgs = {
  includeEntityQueues?: InputMaybe<Scalars['Boolean']['input']>
  includeRelatedCategories?: InputMaybe<Scalars['Boolean']['input']>
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  urlAlias: Scalars['String']['input']
}

export type QueryNodeListByGuestArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  urlAlias: Scalars['String']['input']
}

export type QueryNodeListBySponsorArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  urlAlias: Scalars['String']['input']
}

export type QueryNodeListByTagArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  urlAlias: Scalars['String']['input']
}

export type QueryNodeListNewsFeedArgs = {
  bundles: Array<InputMaybe<BundleType>>
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  queueId: Scalars['String']['input']
}

export type QueryNodeListQueueArgs = {
  bundles: Array<InputMaybe<BundleType>>
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  queueId: Scalars['String']['input']
}

export type QueryNodeListTrendingArgs = {
  bundles: Array<InputMaybe<BundleType>>
  limit?: InputMaybe<Scalars['Int']['input']>
  sort?: InputMaybe<TrendingSort>
}

export type QueryQueueListByUrlAliasArgs = {
  urlAlias: Scalars['String']['input']
}

export type QuerySearchArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  query: Scalars['String']['input']
  sort: Scalars['String']['input']
}

export type QuerySponsorByUrlAliasArgs = {
  urlAlias: Scalars['String']['input']
}

export type QueryTagByUrlAliasArgs = {
  urlAlias: Scalars['String']['input']
}

export type Quote = {
  __typename?: 'Quote'
  ID: Scalars['Int']['output']
  ask: Scalars['Float']['output']
  bid: Scalars['Float']['output']
  change: Scalars['Float']['output']
  changePercentage: Scalars['Float']['output']
  close: Scalars['Float']['output']
  currency: Scalars['String']['output']
  extra?: Maybe<Scalars['String']['output']>
  high: Scalars['Float']['output']
  low: Scalars['Float']['output']
  mid: Scalars['Float']['output']
  open: Scalars['Float']['output']
  originalTime: Scalars['String']['output']
  rates?: Maybe<Array<QuoteRatePair>>
  symbol: Scalars['String']['output']
  time: Scalars['String']['output']
  timestamp: Scalars['Int']['output']
  unit: Scalars['String']['output']
}

export type QuoteRatePair = {
  __typename?: 'QuoteRatePair'
  currency: Scalars['String']['output']
  rate: Scalars['Float']['output']
}

export type Schema = {
  __typename?: 'Schema'
  query?: Maybe<Query>
}

export type SearchResult = {
  __typename?: 'SearchResult'
  createdAt?: Maybe<Scalars['String']['output']>
  excerpt?: Maybe<Scalars['String']['output']>
  id?: Maybe<Scalars['Int']['output']>
  /** @deprecated No longer supported */
  legacyThumbnailImageUrl?: Maybe<Scalars['String']['output']>
  relevance?: Maybe<Scalars['Float']['output']>
  thumbnail?: Maybe<Scalars['String']['output']>
  title?: Maybe<Scalars['String']['output']>
  updatedAt?: Maybe<Scalars['String']['output']>
  urlAlias?: Maybe<Scalars['String']['output']>
}

export type ShanghaiFix = {
  __typename?: 'ShanghaiFix'
  ID: Scalars['Int']['output']
  currency: Scalars['String']['output']
  results?: Maybe<Array<Maybe<ShanghaiQuote>>>
  symbol: Scalars['String']['output']
}

export type ShanghaiQuote = {
  __typename?: 'ShanghaiQuote'
  ID: Scalars['Int']['output']
  am: Scalars['Float']['output']
  currency: Scalars['String']['output']
  pm: Scalars['Float']['output']
  rates?: Maybe<Array<QuoteRatePair>>
  time: Scalars['String']['output']
  timestamp: Scalars['Int']['output']
}

export type Source = {
  __typename?: 'Source'
  description?: Maybe<Scalars['String']['output']>
  id?: Maybe<Scalars['Int']['output']>
  name?: Maybe<Scalars['String']['output']>
  subtitle?: Maybe<Scalars['String']['output']>
}

export type SourceAttribute = {
  __typename?: 'SourceAttribute'
  media?: Maybe<Scalars['String']['output']>
  srcset?: Maybe<Scalars['String']['output']>
}

export type Sponsor = {
  __typename?: 'Sponsor'
  id?: Maybe<Scalars['Int']['output']>
  name?: Maybe<Scalars['String']['output']>
}

export type Sponsored = NodeInterface & {
  __typename?: 'Sponsored'
  /** @deprecated No longer supported */
  allowComments?: Maybe<Scalars['Boolean']['output']>
  audioSnippet?: Maybe<AudioSnippet>
  audioTts?: Maybe<AudioTts>
  author?: Maybe<Author>
  /** @deprecated Use bodyWithEmbeddedMedia instead */
  body?: Maybe<Scalars['String']['output']>
  bodyWithEmbeddedMedia?: Maybe<Body>
  createdAt?: Maybe<Scalars['String']['output']>
  id: Scalars['Int']['output']
  image?: Maybe<Image>
  /** @deprecated No longer supported */
  legacyThumbnailImageUrl?: Maybe<Scalars['String']['output']>
  published?: Maybe<Scalars['Boolean']['output']>
  source?: Maybe<Source>
  sponsor?: Maybe<Sponsor>
  tags?: Maybe<Array<Maybe<Tag>>>
  teaserHeadline?: Maybe<Scalars['String']['output']>
  teaserImage?: Maybe<Image>
  teaserSnippet?: Maybe<Scalars['String']['output']>
  title: Scalars['String']['output']
  updatedAt?: Maybe<Scalars['String']['output']>
  urlAlias?: Maybe<Scalars['String']['output']>
  videoSnippet?: Maybe<VideoSnippet>
}

export type Stock = {
  __typename?: 'Stock'
  Change: Scalars['Float']['output']
  ChangePercentage: Scalars['Float']['output']
  Currency: Scalars['String']['output']
  High: Scalars['Float']['output']
  ID: Scalars['Int']['output']
  Low: Scalars['Float']['output']
  Name: Scalars['String']['output']
  Price: Scalars['Float']['output']
  Symbol: Scalars['String']['output']
  Timestamp: Scalars['String']['output']
  Volume: Scalars['Int']['output']
}

export type StreetTalk = NodeInterface & {
  __typename?: 'StreetTalk'
  createdAt?: Maybe<Scalars['String']['output']>
  id: Scalars['Int']['output']
  published?: Maybe<Scalars['Boolean']['output']>
  source?: Maybe<Scalars['String']['output']>
  teaserHeadline?: Maybe<Scalars['String']['output']>
  title: Scalars['String']['output']
  updatedAt?: Maybe<Scalars['String']['output']>
  url: Scalars['String']['output']
}

export type Tag = {
  __typename?: 'Tag'
  id: Scalars['Int']['output']
  name: Scalars['String']['output']
  urlAlias: Scalars['String']['output']
}

export enum TrendingSort {
  Day = 'Day',
  Month = 'Month',
  Week = 'Week',
  Year = 'Year',
}

export type VideoArchiveSnippetInput = {
  id: Scalars['Int']['input']
  isArchived: Scalars['Boolean']['input']
}

export type VideoCategoriesWithSnippetNodes = {
  __typename?: 'VideoCategoriesWithSnippetNodes'
  id?: Maybe<Scalars['Int']['output']>
  name?: Maybe<Scalars['String']['output']>
  snippets?: Maybe<Array<Maybe<VideoSnippet>>>
  updatedAt?: Maybe<Scalars['Int']['output']>
}

export type VideoCategory = {
  __typename?: 'VideoCategory'
  id: Scalars['Int']['output']
  isHidden?: Maybe<Scalars['Int']['output']>
  name: Scalars['String']['output']
  position?: Maybe<Scalars['Int']['output']>
  urlAlias?: Maybe<Scalars['String']['output']>
}

export type VideoCategorySnippets = {
  __typename?: 'VideoCategorySnippets'
  category?: Maybe<VideoCategory>
  latest?: Maybe<Array<Maybe<VideoSnippet>>>
  upNext?: Maybe<Array<Maybe<VideoSnippet>>>
  videos?: Maybe<Array<Maybe<VideoSnippet>>>
}

export type VideoCreateCategoryInput = {
  name: Scalars['String']['input']
}

export type VideoCreateGuestInput = {
  name: Scalars['String']['input']
}

export type VideoCreateSnippetInput = {
  author?: InputMaybe<Scalars['String']['input']>
  categoryId?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>
  description?: InputMaybe<Scalars['String']['input']>
  distributionHeadline?: InputMaybe<Scalars['String']['input']>
  endTime?: InputMaybe<Scalars['Int']['input']>
  guestNames?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>
  headline?: InputMaybe<Scalars['String']['input']>
  parentSnippetId?: InputMaybe<Scalars['Int']['input']>
  publishedAt?: InputMaybe<Scalars['String']['input']>
  snippetUuid?: InputMaybe<Scalars['String']['input']>
  source?: InputMaybe<Scalars['String']['input']>
  startTime?: InputMaybe<Scalars['Int']['input']>
  status?: InputMaybe<Scalars['Int']['input']>
  tagNames?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>
  thumbnailBase64?: InputMaybe<Scalars['String']['input']>
  thumbnailUuid?: InputMaybe<Scalars['String']['input']>
  transcriptPlainText?: InputMaybe<Scalars['String']['input']>
  userId?: InputMaybe<Scalars['String']['input']>
  videoId?: InputMaybe<Scalars['Int']['input']>
}

export type VideoCreateTagInput = {
  name: Scalars['String']['input']
}

export type VideoCreateVideoInput = {
  size?: InputMaybe<Scalars['Int']['input']>
  uploadedName?: InputMaybe<Scalars['String']['input']>
  userId?: InputMaybe<Scalars['String']['input']>
  uuid?: InputMaybe<Scalars['String']['input']>
}

export type VideoDeleteCategoryInput = {
  id: Scalars['Int']['input']
}

export type VideoDeleteSnippetInput = {
  id: Scalars['Int']['input']
}

export type VideoExportSnippetToAcmsInput = {
  uuid: Scalars['String']['input']
}

export type VideoExternalGetVideoByAlias = {
  __typename?: 'VideoExternalGetVideoByAlias'
  category?: Maybe<VideoCategory>
  categoryVideos?: Maybe<Array<Maybe<VideoSnippet>>>
  featured?: Maybe<VideoSnippet>
  latest?: Maybe<Array<Maybe<VideoSnippet>>>
  upNext?: Maybe<Array<Maybe<VideoSnippet>>>
}

export type VideoExternalUpNextAndLatest = {
  __typename?: 'VideoExternalUpNextAndLatest'
  latest?: Maybe<Array<Maybe<VideoSnippet>>>
  upNext?: Maybe<Array<Maybe<VideoSnippet>>>
}

export type VideoFeed = {
  __typename?: 'VideoFeed'
  latest?: Maybe<Array<Maybe<VideoVcmsSnippet>>>
  upNext?: Maybe<Array<Maybe<VideoVcmsSnippet>>>
}

export type VideoGuest = {
  __typename?: 'VideoGuest'
  id: Scalars['Int']['output']
  name: Scalars['String']['output']
}

export type VideoJobCounts = {
  __typename?: 'VideoJobCounts'
  active?: Maybe<Scalars['Int']['output']>
  completed?: Maybe<Scalars['Int']['output']>
  delayed?: Maybe<Scalars['Int']['output']>
  failed?: Maybe<Scalars['Int']['output']>
  paused?: Maybe<Scalars['Int']['output']>
  waiting?: Maybe<Scalars['Int']['output']>
}

export type VideoProgress = {
  __typename?: 'VideoProgress'
  progress?: Maybe<Scalars['Int']['output']>
  state?: Maybe<Scalars['String']['output']>
}

export type VideoQueueworkerUpdateVideoInput = {
  duration?: InputMaybe<Scalars['Int']['input']>
  size?: InputMaybe<Scalars['Int']['input']>
  tagsRawResponse?: InputMaybe<Scalars['String']['input']>
  transcriptRawResponse?: InputMaybe<Scalars['String']['input']>
  uuid: Scalars['String']['input']
}

export type VideoReorderCategoryInput = {
  categories: Scalars['String']['input']
}

export type VideoSearchSnippetsResponse = {
  __typename?: 'VideoSearchSnippetsResponse'
  snippets?: Maybe<Array<Maybe<VideoSnippet>>>
  total?: Maybe<Scalars['Int']['output']>
}

export type VideoSnippet = {
  __typename?: 'VideoSnippet'
  author?: Maybe<Scalars['String']['output']>
  categories?: Maybe<Array<Maybe<VideoCategory>>>
  /** @deprecated No longer return value. Use 'categories' array instead. */
  category?: Maybe<VideoCategory>
  createdAt?: Maybe<Scalars['Int']['output']>
  description?: Maybe<Scalars['String']['output']>
  distributionHeadline?: Maybe<Scalars['String']['output']>
  endTime?: Maybe<Scalars['Int']['output']>
  frontendPath?: Maybe<Scalars['String']['output']>
  guests?: Maybe<Array<Maybe<VideoGuest>>>
  headline?: Maybe<Scalars['String']['output']>
  id?: Maybe<Scalars['Int']['output']>
  isArchived?: Maybe<Scalars['Boolean']['output']>
  isExported?: Maybe<Scalars['Boolean']['output']>
  parentSnippet?: Maybe<VideoSnippet>
  publishedAt?: Maybe<Scalars['Int']['output']>
  source?: Maybe<Scalars['String']['output']>
  startTime?: Maybe<Scalars['Int']['output']>
  status?: Maybe<Scalars['Int']['output']>
  tags?: Maybe<Array<Maybe<VideoTag>>>
  thumbnailUuid?: Maybe<Scalars['String']['output']>
  transcriptPlainText?: Maybe<Scalars['String']['output']>
  updatedAt?: Maybe<Scalars['Int']['output']>
  userId?: Maybe<Scalars['String']['output']>
  uuid?: Maybe<Scalars['String']['output']>
  video?: Maybe<VideoVideo>
}

export enum VideoSnippetSearchSort {
  DateAsc = 'dateAsc',
  DateDesc = 'dateDesc',
  Relevance = 'relevance',
}

export type VideoSpeechRecognitionAlternative = {
  __typename?: 'VideoSpeechRecognitionAlternative'
  words?: Maybe<Array<Maybe<VideoSpeechRecognitionWord>>>
}

export type VideoSpeechRecognitionResult = {
  __typename?: 'VideoSpeechRecognitionResult'
  alternatives?: Maybe<Array<Maybe<VideoSpeechRecognitionAlternative>>>
}

export type VideoSpeechRecognitionTime = {
  __typename?: 'VideoSpeechRecognitionTime'
  nanos?: Maybe<Scalars['Int']['output']>
  seconds?: Maybe<Scalars['String']['output']>
}

export type VideoSpeechRecognitionWord = {
  __typename?: 'VideoSpeechRecognitionWord'
  endTime?: Maybe<VideoSpeechRecognitionTime>
  startTime?: Maybe<VideoSpeechRecognitionTime>
  word?: Maybe<Scalars['String']['output']>
}

export type VideoTag = {
  __typename?: 'VideoTag'
  id: Scalars['Int']['output']
  name: Scalars['String']['output']
  tokens: Scalars['String']['output']
}

export type VideoTagEntities = {
  __typename?: 'VideoTagEntities'
  name?: Maybe<Scalars['String']['output']>
  salience?: Maybe<Scalars['Float']['output']>
  type?: Maybe<Scalars['String']['output']>
}

export type VideoTagsRawResponse = {
  __typename?: 'VideoTagsRawResponse'
  entities?: Maybe<Array<Maybe<VideoTagEntities>>>
}

export type VideoTranscriptRawResponse = {
  __typename?: 'VideoTranscriptRawResponse'
  results?: Maybe<Array<Maybe<VideoSpeechRecognitionResult>>>
}

export type VideoUpdateCategoryDisplayInput = {
  id: Scalars['Int']['input']
  isHidden: Scalars['Int']['input']
}

export type VideoUpdateCategoryInput = {
  id: Scalars['Int']['input']
  name: Scalars['String']['input']
}

export type VideoUpdateSnippetInput = {
  author?: InputMaybe<Scalars['String']['input']>
  categoryId?: InputMaybe<Array<InputMaybe<Scalars['Int']['input']>>>
  description?: InputMaybe<Scalars['String']['input']>
  distributionHeadline?: InputMaybe<Scalars['String']['input']>
  endTime?: InputMaybe<Scalars['Int']['input']>
  guestNames?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>
  headline?: InputMaybe<Scalars['String']['input']>
  id: Scalars['Int']['input']
  publishedAt?: InputMaybe<Scalars['String']['input']>
  source?: InputMaybe<Scalars['String']['input']>
  startTime?: InputMaybe<Scalars['Int']['input']>
  status?: InputMaybe<Scalars['Int']['input']>
  tagNames?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>
  thumbnailBase64?: InputMaybe<Scalars['String']['input']>
  thumbnailUuid?: InputMaybe<Scalars['String']['input']>
  transcriptPlainText?: InputMaybe<Scalars['String']['input']>
}

export type VideoVcmsCategory = {
  __typename?: 'VideoVCMSCategory'
  edges?: Maybe<VideoVcmsCategoryEdges>
  id: Scalars['ID']['output']
  name?: Maybe<Scalars['String']['output']>
  position?: Maybe<Scalars['Int']['output']>
  urlAlias?: Maybe<Scalars['String']['output']>
}

export type VideoVcmsCategoryEdges = {
  __typename?: 'VideoVCMSCategoryEdges'
  snippets?: Maybe<Array<Maybe<VideoVcmsSnippet>>>
}

export type VideoVcmsSnippet = {
  __typename?: 'VideoVCMSSnippet'
  categories?: Maybe<Array<Maybe<VideoVcmsCategory>>>
  createdAt?: Maybe<Scalars['String']['output']>
  description?: Maybe<Scalars['String']['output']>
  endTime?: Maybe<Scalars['Int']['output']>
  guests?: Maybe<Array<Maybe<VideoGuest>>>
  headline?: Maybe<Scalars['String']['output']>
  id: Scalars['ID']['output']
  publishedAt?: Maybe<Scalars['String']['output']>
  source?: Maybe<Scalars['String']['output']>
  startTime?: Maybe<Scalars['Int']['output']>
  status?: Maybe<Scalars['Int']['output']>
  thumbnailUuid?: Maybe<Scalars['String']['output']>
  updatedAt?: Maybe<Scalars['String']['output']>
  urlAlias?: Maybe<Scalars['String']['output']>
  uuid?: Maybe<Scalars['String']['output']>
  videoId?: Maybe<Scalars['Int']['output']>
}

export type VideoVcmsVideo = {
  __typename?: 'VideoVCMSVideo'
  duration?: Maybe<Scalars['Int']['output']>
  id: Scalars['ID']['output']
  uuid?: Maybe<Scalars['String']['output']>
}

export type VideoVideo = {
  __typename?: 'VideoVideo'
  createdAt?: Maybe<Scalars['String']['output']>
  duration?: Maybe<Scalars['Int']['output']>
  id: Scalars['Int']['output']
  size?: Maybe<Scalars['Int']['output']>
  tagsPlainText?: Maybe<Array<Maybe<Scalars['String']['output']>>>
  tagsRawResponse?: Maybe<VideoTagsRawResponse>
  transcriptPlainText?: Maybe<Scalars['String']['output']>
  transcriptRawResponse?: Maybe<VideoTranscriptRawResponse>
  uploadedName?: Maybe<Scalars['String']['output']>
  uuid: Scalars['String']['output']
}

export type VideoVideosListPageExternal = {
  __typename?: 'VideoVideosListPageExternal'
  categories?: Maybe<Array<Maybe<VideoCategoriesWithSnippetNodes>>>
  featured?: Maybe<VideoSnippet>
  latest?: Maybe<Array<Maybe<VideoSnippet>>>
  upNext?: Maybe<Array<Maybe<VideoSnippet>>>
}

export type MktQuotesGoldRatiosSidebarQueryVariables = Exact<{
  timestamp: Scalars['Int']['input']
}>

export type MktQuotesGoldRatiosSidebarQuery = {
  __typename?: 'Query'
  GetBarchartQuotes?: {
    __typename?: 'BarchartGetQuote'
    results?: Array<{
      __typename?: 'BarchartQuote'
      symbol: string
      name: string
      lastPrice: number
      netChange: number
      percentChange: number
      open: number
      high: number
      low: number
      close: number
    } | null> | null
  } | null
}

export type GetMarketStatusQueryVariables = Exact<{ [key: string]: never }>

export type GetMarketStatusQuery = {
  __typename?: 'Query'
  GetMarketStatus?: {
    __typename?: 'MarketStatus'
    next: number
    status: string
  } | null
}

export type GetMetalHistoryQueryVariables = Exact<{
  currency: Scalars['String']['input']
  startTime: Scalars['Int']['input']
  endTime: Scalars['Int']['input']
  groupBy: Scalars['String']['input']
  limit: Scalars['Int']['input']
  offset: Scalars['Int']['input']
  symbol: Scalars['String']['input']
}>

export type GetMetalHistoryQuery = {
  __typename?: 'Query'
  GetMetalHistoryV3?: {
    __typename?: 'Metal'
    symbol: string
    results?: Array<{
      __typename?: 'Quote'
      timestamp: number
      high: number
      low: number
      open: number
      close: number
    } | null> | null
  } | null
}

export type GetCryptoHistoryQueryVariables = Exact<{
  symbol: Scalars['String']['input']
  currency: Scalars['String']['input']
  startTime: Scalars['Int']['input']
  endTime?: InputMaybe<Scalars['Int']['input']>
  groupBy: Scalars['String']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type GetCryptoHistoryQuery = {
  __typename?: 'Query'
  GetCryptoHistoryV3?: Array<{
    __typename?: 'Crypto'
    results?: Array<{
      __typename?: 'Quote'
      high: number
      low: number
      open: number
      close: number
      timestamp: number
    } | null> | null
  } | null> | null
}

export type CommoditiesNewsSidebarCategoryFragmentFragment = {
  __typename?: 'Category'
  id: number
  name: string
  urlAlias: string
}

export type CommoditiesNewsSidebarImageFragmentFragment = {
  __typename?: 'Image'
  detail?: {
    __typename?: 'ImageDetail'
    default?: { __typename?: 'SourceAttribute'; srcset?: string | null } | null
    sources?: {
      __typename?: 'ImageDetailSources'
      teaser_small?: {
        __typename?: 'SourceAttribute'
        srcset?: string | null
        media?: string | null
      } | null
      teaser_medium?: {
        __typename?: 'SourceAttribute'
        srcset?: string | null
        media?: string | null
      } | null
      desktop?: {
        __typename?: 'SourceAttribute'
        media?: string | null
        srcset?: string | null
      } | null
      mobile?: {
        __typename?: 'SourceAttribute'
        media?: string | null
        srcset?: string | null
      } | null
      tablet?: {
        __typename?: 'SourceAttribute'
        media?: string | null
        srcset?: string | null
      } | null
    } | null
  } | null
}

export type CommoditiesNewsSidebarArticleTeaserFragmentFragment = {
  __typename?: 'NewsArticle'
  id: number
  teaserSnippet?: string | null
  title: string
  teaserHeadline?: string | null
  urlAlias?: string | null
  createdAt?: string | null
  category?: {
    __typename?: 'Category'
    id: number
    name: string
    urlAlias: string
  } | null
  image?: {
    __typename?: 'Image'
    detail?: {
      __typename?: 'ImageDetail'
      default?: {
        __typename?: 'SourceAttribute'
        srcset?: string | null
      } | null
      sources?: {
        __typename?: 'ImageDetailSources'
        teaser_small?: {
          __typename?: 'SourceAttribute'
          srcset?: string | null
          media?: string | null
        } | null
        teaser_medium?: {
          __typename?: 'SourceAttribute'
          srcset?: string | null
          media?: string | null
        } | null
        desktop?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
        mobile?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
        tablet?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
      } | null
    } | null
  } | null
}

export type CommoditiesNewsSidebarLatestNewsQueryVariables = Exact<{
  urlAlias: Scalars['String']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  includeRelatedCategories?: InputMaybe<Scalars['Boolean']['input']>
  includeEntityQueues?: InputMaybe<Scalars['Boolean']['input']>
}>

export type CommoditiesNewsSidebarLatestNewsQuery = {
  __typename?: 'Query'
  nodeListByCategory?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename?: 'Commentary'
          id: number
          teaserSnippet?: string | null
          title: string
          urlAlias?: string | null
          createdAt?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
        }
      | { __typename?: 'LeadGen' }
      | {
          __typename?: 'NewsArticle'
          id: number
          teaserSnippet?: string | null
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type CommoditiesNewsSidebarTrendingQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
}>

export type CommoditiesNewsSidebarTrendingQuery = {
  __typename?: 'Query'
  nodeListTrending?: Array<
    | { __typename?: 'BasicPage' }
    | { __typename?: 'BreakingNews' }
    | { __typename?: 'Commentary' }
    | { __typename?: 'LeadGen' }
    | {
        __typename?: 'NewsArticle'
        id: number
        teaserSnippet?: string | null
        title: string
        teaserHeadline?: string | null
        urlAlias?: string | null
        createdAt?: string | null
        category?: {
          __typename?: 'Category'
          id: number
          name: string
          urlAlias: string
        } | null
        image?: {
          __typename?: 'Image'
          detail?: {
            __typename?: 'ImageDetail'
            default?: {
              __typename?: 'SourceAttribute'
              srcset?: string | null
            } | null
            sources?: {
              __typename?: 'ImageDetailSources'
              teaser_small?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              teaser_medium?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              desktop?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              mobile?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              tablet?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
            } | null
          } | null
        } | null
      }
    | { __typename?: 'OffTheWire' }
    | { __typename?: 'PressRelease' }
    | { __typename?: 'Sponsored' }
    | { __typename?: 'StreetTalk' }
    | null
  > | null
}

export type GoldIndexQuoteFragmentFragment = {
  __typename?: 'Quote'
  originalTime: string
  bid: number
  change: number
  changePercentage: number
  extra?: string | null
}

export type GoldIndexFragmentFragment = {
  __typename?: 'Metal'
  results?: Array<{
    __typename?: 'Quote'
    originalTime: string
    bid: number
    change: number
    changePercentage: number
    extra?: string | null
  } | null> | null
}

export type GoldIndexTableQueryVariables = Exact<{
  currency: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}>

export type GoldIndexTableQuery = {
  __typename?: 'Query'
  Gold?: {
    __typename?: 'Metal'
    results?: Array<{
      __typename?: 'Quote'
      originalTime: string
      bid: number
      change: number
      changePercentage: number
      extra?: string | null
    } | null> | null
  } | null
  Silver?: {
    __typename?: 'Metal'
    results?: Array<{
      __typename?: 'Quote'
      originalTime: string
      bid: number
      change: number
      changePercentage: number
      extra?: string | null
    } | null> | null
  } | null
  Platinum?: {
    __typename?: 'Metal'
    results?: Array<{
      __typename?: 'Quote'
      originalTime: string
      bid: number
      change: number
      changePercentage: number
      extra?: string | null
    } | null> | null
  } | null
  Palladium?: {
    __typename?: 'Metal'
    results?: Array<{
      __typename?: 'Quote'
      originalTime: string
      bid: number
      change: number
      changePercentage: number
      extra?: string | null
    } | null> | null
  } | null
  Copper?: {
    __typename?: 'Metal'
    results?: Array<{
      __typename?: 'Quote'
      originalTime: string
      bid: number
      change: number
      changePercentage: number
      extra?: string | null
    } | null> | null
  } | null
  Nickel?: {
    __typename?: 'Metal'
    results?: Array<{
      __typename?: 'Quote'
      originalTime: string
      bid: number
      change: number
      changePercentage: number
      extra?: string | null
    } | null> | null
  } | null
  Aluminum?: {
    __typename?: 'Metal'
    results?: Array<{
      __typename?: 'Quote'
      originalTime: string
      bid: number
      change: number
      changePercentage: number
      extra?: string | null
    } | null> | null
  } | null
  Zinc?: {
    __typename?: 'Metal'
    results?: Array<{
      __typename?: 'Quote'
      originalTime: string
      bid: number
      change: number
      changePercentage: number
      extra?: string | null
    } | null> | null
  } | null
  Lead?: {
    __typename?: 'Metal'
    results?: Array<{
      __typename?: 'Quote'
      originalTime: string
      bid: number
      change: number
      changePercentage: number
      extra?: string | null
    } | null> | null
  } | null
}

export type GoldIndexWidgetQueryVariables = Exact<{
  currency: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}>

export type GoldIndexWidgetQuery = {
  __typename?: 'Query'
  Gold?: {
    __typename?: 'Metal'
    results?: Array<{
      __typename?: 'Quote'
      originalTime: string
      bid: number
      change: number
      changePercentage: number
      extra?: string | null
    } | null> | null
  } | null
  Silver?: {
    __typename?: 'Metal'
    results?: Array<{
      __typename?: 'Quote'
      originalTime: string
      bid: number
      change: number
      changePercentage: number
      extra?: string | null
    } | null> | null
  } | null
  Platinum?: {
    __typename?: 'Metal'
    results?: Array<{
      __typename?: 'Quote'
      originalTime: string
      bid: number
      change: number
      changePercentage: number
      extra?: string | null
    } | null> | null
  } | null
  Palladium?: {
    __typename?: 'Metal'
    results?: Array<{
      __typename?: 'Quote'
      originalTime: string
      bid: number
      change: number
      changePercentage: number
      extra?: string | null
    } | null> | null
  } | null
}

export type MiningEquitiesTableQueryVariables = Exact<{ [key: string]: never }>

export type MiningEquitiesTableQuery = {
  __typename?: 'Query'
  GetEquities?: Array<{
    __typename?: 'Equity'
    Category?: Array<string> | null
    Change: number
    ChangePercentage: number
    Currency: string
    Exchange: string
    High: number
    ID: number
    Low: number
    Name: string
    Price: number
    Symbol: string
    SymbolURL: string
    TVExchange: string
    TVSymbol: string
    Timestamp: string
    Volume: number
  } | null> | null
}

export type LatestNewsSidebarByCategoryQueryVariables = Exact<{
  urlAlias: Scalars['String']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  includeRelatedCategories?: InputMaybe<Scalars['Boolean']['input']>
  includeEntityQueues?: InputMaybe<Scalars['Boolean']['input']>
}>

export type LatestNewsSidebarByCategoryQuery = {
  __typename?: 'Query'
  nodeListByCategory?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename?: 'Commentary'
          id: number
          title: string
          urlAlias?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            name: string
            urlAlias: string
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'LeadGen' }
      | {
          __typename?: 'NewsArticle'
          id: number
          title: string
          urlAlias?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            name: string
            urlAlias: string
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
        }
      | {
          __typename?: 'OffTheWire'
          id: number
          title: string
          urlAlias?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            name: string
            urlAlias: string
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type LatestNewsSidebarQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type LatestNewsSidebarQuery = {
  __typename?: 'Query'
  queue?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename?: 'Commentary'
          id: number
          title: string
          urlAlias?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            name: string
            urlAlias: string
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'LeadGen' }
      | {
          __typename?: 'NewsArticle'
          id: number
          title: string
          urlAlias?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            name: string
            urlAlias: string
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
        }
      | {
          __typename?: 'OffTheWire'
          id: number
          title: string
          urlAlias?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            name: string
            urlAlias: string
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type NewsGenericPressReleasesUhmQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type NewsGenericPressReleasesUhmQuery = {
  __typename?: 'Query'
  nodeList?: {
    __typename?: 'NodeListWithPagination'
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | {
          __typename?: 'PressRelease'
          id: number
          title: string
          teaserHeadline?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          url: string
        }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type NewsGenericPressReleasesQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  queueId?: InputMaybe<Scalars['String']['input']>
}>

export type NewsGenericPressReleasesQuery = {
  __typename?: 'Query'
  ids?: Array<number | null> | null
  queue?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | {
          __typename?: 'PressRelease'
          id: number
          title: string
          teaserHeadline?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          url: string
        }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type CryptoQuoteQueryVariables = Exact<{
  symbol: Scalars['String']['input']
  currency: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}>

export type CryptoQuoteQuery = {
  __typename?: 'Query'
  GetCryptoQuoteV3?: {
    __typename?: 'Crypto'
    symbol: string
    currency: string
    results?: Array<{
      __typename?: 'Quote'
      high: number
      low: number
      open: number
      close: number
      change: number
      changePercentage: number
    } | null> | null
  } | null
}

export type CryptoResFragment = {
  __typename?: 'Crypto'
  ID: number
  symbol: string
  results?: Array<{
    __typename?: 'Quote'
    ID: number
    high: number
    low: number
    open: number
    close: number
    change: number
    changePercentage: number
  } | null> | null
}

export type CryptosBtcEthLtcXmrXrpQueryVariables = Exact<{
  currency: Scalars['String']['input']
  timestamp: Scalars['Int']['input']
}>

export type CryptosBtcEthLtcXmrXrpQuery = {
  __typename?: 'Query'
  BTC?: {
    __typename?: 'Crypto'
    ID: number
    symbol: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      high: number
      low: number
      open: number
      close: number
      change: number
      changePercentage: number
    } | null> | null
  } | null
  ETH?: {
    __typename?: 'Crypto'
    ID: number
    symbol: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      high: number
      low: number
      open: number
      close: number
      change: number
      changePercentage: number
    } | null> | null
  } | null
  LTC?: {
    __typename?: 'Crypto'
    ID: number
    symbol: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      high: number
      low: number
      open: number
      close: number
      change: number
      changePercentage: number
    } | null> | null
  } | null
  XMR?: {
    __typename?: 'Crypto'
    ID: number
    symbol: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      high: number
      low: number
      open: number
      close: number
      change: number
      changePercentage: number
    } | null> | null
  } | null
  XRP?: {
    __typename?: 'Crypto'
    ID: number
    symbol: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      high: number
      low: number
      open: number
      close: number
      change: number
      changePercentage: number
    } | null> | null
  } | null
}

export type CryptosTableQueryVariables = Exact<{
  symbols: Scalars['String']['input']
  currency: Scalars['String']['input']
}>

export type CryptosTableQuery = {
  __typename?: 'Query'
  GetCryptoComparePriceFullV3?: Array<{
    __typename?: 'CryptoComparePriceFull'
    price: number
    imageUrl: string
    mktCap: number
    volumeDay: number
    changePctHourCalculated: number
    changePct24HourCalculated: number
    changePct7DayCalculated: number
    fromSymbol: string
    totalVolume24h: number
    totalVolume24hTo: number
    change24Hour: number
    highDay: number
    lowDay: number
  } | null> | null
}

export type DigestStreetTalkQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type DigestStreetTalkQuery = {
  __typename?: 'Query'
  nodeList?: {
    __typename?: 'NodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | {
          __typename?: 'StreetTalk'
          id: number
          title: string
          source?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          url: string
        }
    > | null
  } | null
}

export type DigestLatestNewsQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type DigestLatestNewsQuery = {
  __typename?: 'Query'
  nodeListQueue?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | {
          __typename?: 'NewsArticle'
          id: number
          title: string
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          source?: {
            __typename?: 'Source'
            id?: number | null
            name?: string | null
          } | null
        }
      | {
          __typename?: 'OffTheWire'
          id: number
          title: string
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          source?: {
            __typename?: 'Source'
            id?: number | null
            name?: string | null
          } | null
        }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type ExitModalQueryVariables = Exact<{ [key: string]: never }>

export type ExitModalQuery = {
  __typename?: 'Query'
  exitModal?: {
    __typename: 'ExitModal'
    active?: string | null
    backgroundImage?: string | null
    buttonColor?: string | null
    subTitle?: string | null
    subTitleColor?: string | null
    title?: string | null
    titleColor?: string | null
  } | null
}

export type MarketStatusQueryVariables = Exact<{ [key: string]: never }>

export type MarketStatusQuery = {
  __typename?: 'Query'
  GetMarketStatus?: {
    __typename?: 'MarketStatus'
    status: string
    next: number
  } | null
}

export type BarchartsGoldIndicatorsQueryVariables = Exact<{
  symbols: Scalars['String']['input']
  timestamp: Scalars['Int']['input']
}>

export type BarchartsGoldIndicatorsQuery = {
  __typename?: 'Query'
  GetBarchartQuotes?: {
    __typename?: 'BarchartGetQuote'
    results?: Array<{
      __typename?: 'BarchartQuote'
      symbol: string
      name: string
      lastPrice: number
      netChange: number
      percentChange: number
      open: number
      high: number
      low: number
      close: number
    } | null> | null
  } | null
}

export type BarchartsQuotesQueryVariables = Exact<{
  timestamp: Scalars['Int']['input']
  symbols: Scalars['String']['input']
}>

export type BarchartsQuotesQuery = {
  __typename?: 'Query'
  GetBarchartQuotes?: {
    __typename?: 'BarchartGetQuote'
    timestamp: number
    symbols: string
    results?: Array<{
      __typename?: 'BarchartQuote'
      high: number
      lastPrice: number
      low: number
      name: string
      netChange: number
      open: number
      percentChange: number
      serverTimestamp: string
      symbol: string
      volume: number
    } | null> | null
  } | null
}

export type BarchartsLeadersQueryVariables = Exact<{
  leaderType: Scalars['String']['input']
  limit: Scalars['Int']['input']
}>

export type BarchartsLeadersQuery = {
  __typename?: 'Query'
  leaders?: {
    __typename?: 'BarchartGetLeaders'
    exchanges: string
    timestamp: number
    results?: Array<{
      __typename?: 'BarchartLeader'
      symbol: string
      symbolName: string
      priceNetChange: number
      pricePercentChange: number
      lastPrice: number
      timestamp: string
    } | null> | null
  } | null
}

export type RegionIndicesQueryVariables = Exact<{
  timestamp: Scalars['Int']['input']
}>

export type RegionIndicesQuery = {
  __typename?: 'Query'
  USquotes?: {
    __typename?: 'BarchartGetQuote'
    timestamp: number
    symbols: string
    results?: Array<{
      __typename?: 'BarchartQuote'
      lastPrice: number
      name: string
      netChange: number
      percentChange: number
      serverTimestamp: string
      symbol: string
    } | null> | null
  } | null
  EUquotes?: {
    __typename?: 'BarchartGetQuote'
    timestamp: number
    symbols: string
    results?: Array<{
      __typename?: 'BarchartQuote'
      lastPrice: number
      name: string
      netChange: number
      percentChange: number
      serverTimestamp: string
      symbol: string
    } | null> | null
  } | null
  ASIAquotes?: {
    __typename?: 'BarchartGetQuote'
    timestamp: number
    symbols: string
    results?: Array<{
      __typename?: 'BarchartQuote'
      lastPrice: number
      name: string
      netChange: number
      percentChange: number
      serverTimestamp: string
      symbol: string
    } | null> | null
  } | null
}

export type BarchartsFuturesByExchangeQueryVariables = Exact<{
  exchange: Scalars['String']['input']
  category: Scalars['String']['input']
}>

export type BarchartsFuturesByExchangeQuery = {
  __typename?: 'Query'
  GetBarchartFuturesByExchange?: {
    __typename?: 'BarchartGetFuturesByExchange'
    timestamp: number
    exchange: string
    results?: Array<{
      __typename?: 'BarchartFuture'
      name: string
      lastPrice: number
      percentChange: number
      netChange: number
      symbol: string
      close: number
      low: number
      high: number
    } | null> | null
  } | null
}

export type MetalQuoteQueryVariables = Exact<{
  symbol: Scalars['String']['input']
  currency: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}>

export type MetalQuoteQuery = {
  __typename?: 'Query'
  GetMetalQuoteV3?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
}

export type GetNivoDataQueryVariables = Exact<{
  currency: Scalars['String']['input']
  timestamp: Scalars['Int']['input']
  symbol: Scalars['String']['input']
}>

export type GetNivoDataQuery = {
  __typename?: 'Query'
  now?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  history?: {
    __typename?: 'Metal'
    symbol: string
    currency: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
}

export type SilverPgmQueryVariables = Exact<{
  currency: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}>

export type SilverPgmQuery = {
  __typename?: 'Query'
  silver?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  platinum?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  palladium?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  rhodium?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
}

export type MetalMonthAnnualQueryVariables = Exact<{
  symbol: Scalars['String']['input']
  currency: Scalars['String']['input']
  timestamp: Scalars['Int']['input']
}>

export type MetalMonthAnnualQuery = {
  __typename?: 'Query'
  GetHistoricalPointsV3?: {
    __typename?: 'HistoricalPoints'
    thirtyDay: {
      __typename?: 'Quote'
      ID: number
      change: number
      changePercentage: number
    }
    sixtyDay: {
      __typename?: 'Quote'
      ID: number
      change: number
      changePercentage: number
    }
    oneYear: {
      __typename?: 'Quote'
      ID: number
      change: number
      changePercentage: number
    }
    fiveYear: {
      __typename?: 'Quote'
      ID: number
      change: number
      changePercentage: number
    }
  } | null
}

export type AllMetalsQuoteQueryVariables = Exact<{
  currency: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}>

export type AllMetalsQuoteQuery = {
  __typename?: 'Query'
  gold?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  silver?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  platinum?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  palladium?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  rhodium?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
}

export type GoldSilverPlatinumPalladiumQueryVariables = Exact<{
  currency: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}>

export type GoldSilverPlatinumPalladiumQuery = {
  __typename?: 'Query'
  gold?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  silver?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  platinum?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  palladium?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
}

export type BaseMetalsQueryVariables = Exact<{
  timestamp?: InputMaybe<Scalars['Int']['input']>
  currency: Scalars['String']['input']
}>

export type BaseMetalsQuery = {
  __typename?: 'Query'
  AluminumPrice?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  CopperPrice?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  NickelPrice?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  ZincPrice?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  LeadPrice?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  Uranium?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
}

export type MetalPointsInTimeQueryVariables = Exact<{
  now?: InputMaybe<Scalars['Int']['input']>
  thirtyday?: InputMaybe<Scalars['Int']['input']>
  oneyear?: InputMaybe<Scalars['Int']['input']>
  sixmonths?: InputMaybe<Scalars['Int']['input']>
  symbol: Scalars['String']['input']
}>

export type MetalPointsInTimeQuery = {
  __typename?: 'Query'
  now?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  thirtyday?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  sixmonths?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  oneyear?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
}

export type MetalHistoryQueryVariables = Exact<{
  symbol: Scalars['String']['input']
  startTime: Scalars['Int']['input']
  endTime: Scalars['Int']['input']
  groupBy?: InputMaybe<Scalars['String']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  limit?: InputMaybe<Scalars['Int']['input']>
  currency: Scalars['String']['input']
}>

export type MetalHistoryQuery = {
  __typename?: 'Query'
  GetMetalHistoryV3?: {
    __typename?: 'Metal'
    currency: string
    symbol: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
}

export type GetKitcoFixPmv3QueryVariables = Exact<{
  currency: Scalars['String']['input']
  year: Scalars['Int']['input']
}>

export type GetKitcoFixPmv3Query = {
  __typename?: 'Query'
  gold?: {
    __typename?: 'Metal'
    currency: string
    symbol: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  silver?: {
    __typename?: 'Metal'
    currency: string
    symbol: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  platinum?: {
    __typename?: 'Metal'
    currency: string
    symbol: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  palladium?: {
    __typename?: 'Metal'
    currency: string
    symbol: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
}

export type GetNivoData30DaysQueryVariables = Exact<{
  currency: Scalars['String']['input']
  endTime: Scalars['Int']['input']
  symbol: Scalars['String']['input']
}>

export type GetNivoData30DaysQuery = {
  __typename?: 'Query'
  now?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  history?: {
    __typename?: 'Metal'
    symbol: string
    currency: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
}

export type MetalAndCurrenciesQueryVariables = Exact<{
  symbol: Scalars['String']['input']
  timestamp?: InputMaybe<Scalars['Int']['input']>
}>

export type MetalAndCurrenciesQuery = {
  __typename?: 'Query'
  metal?: {
    __typename?: 'Metal'
    results?: Array<{
      __typename?: 'Quote'
      ask: number
      bid: number
      change: number
      changePercentage: number
      high: number
      low: number
      mid: number
      unit: string
    } | null> | null
  } | null
  AUD?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  BRL?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  GBP?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  CAD?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  CNY?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  EURO?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  HKD?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  INR?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  JPY?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  MXN?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  RUB?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  ZAR?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  CHF?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
}

export type CurrenciesQueryVariables = Exact<{
  timestamp?: InputMaybe<Scalars['Int']['input']>
}>

export type CurrenciesQuery = {
  __typename?: 'Query'
  AUD?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  BRL?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  GBP?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  CAD?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  CNY?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  EUR?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  HKD?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  INR?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  JPY?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  MXN?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  RUB?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  ZAR?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
  CHF?: {
    __typename?: 'Forex'
    results?: Array<{
      __typename?: 'ForexQuote'
      timestamp: number
      ask: number
      mid: number
      bid: number
      change: number
      changePercentage: number
      ctousd: number
      usdtoc: number
    } | null> | null
  } | null
}

export type ShanghaiFixQueryVariables = Exact<{
  currency: Scalars['String']['input']
  timestamp: Scalars['Int']['input']
  symbol: Scalars['String']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
}>

export type ShanghaiFixQuery = {
  __typename?: 'Query'
  GetShanghaiFixV3?: {
    __typename?: 'ShanghaiFix'
    ID: number
    currency: string
    symbol: string
    results?: Array<{
      __typename?: 'ShanghaiQuote'
      ID: number
      timestamp: number
      am: number
      pm: number
    } | null> | null
  } | null
}

export type ShanghaiFixByYearQueryVariables = Exact<{
  year: Scalars['String']['input']
}>

export type ShanghaiFixByYearQuery = {
  __typename?: 'Query'
  GetShanghaiFixByYearV3?: {
    __typename?: 'ShanghaiFix'
    ID: number
    currency: string
    symbol: string
    results?: Array<{
      __typename?: 'ShanghaiQuote'
      ID: number
      timestamp: number
      am: number
      pm: number
    } | null> | null
  } | null
}

export type LondonFixQueryVariables = Exact<{
  year: Scalars['String']['input']
}>

export type LondonFixQuery = {
  __typename?: 'Query'
  londonFixUSD?: {
    __typename?: 'LondonFix'
    ID: number
    currency: string
    results?: Array<{
      __typename?: 'LondonQuote'
      ID: number
      goldAM: number
      goldPM: number
      timestamp: number
      silver: number
      platinumAM: number
      platinumPM: number
      palladiumAM: number
      palladiumPM: number
    } | null> | null
  } | null
  londonFixEUR?: {
    __typename?: 'LondonFix'
    ID: number
    currency: string
    results?: Array<{
      __typename?: 'LondonQuote'
      ID: number
      goldAM: number
      goldPM: number
      timestamp: number
      silver: number
      platinumAM: number
      platinumPM: number
      palladiumAM: number
      palladiumPM: number
    } | null> | null
  } | null
  londonFixGBP?: {
    __typename?: 'LondonFix'
    ID: number
    currency: string
    results?: Array<{
      __typename?: 'LondonQuote'
      ID: number
      goldAM: number
      goldPM: number
      timestamp: number
      silver: number
      platinumAM: number
      platinumPM: number
      palladiumAM: number
      palladiumPM: number
    } | null> | null
  } | null
}

export type LondonFixDynamicQueryVariables = Exact<{
  currency: Scalars['String']['input']
  startTime: Scalars['Int']['input']
  endTime: Scalars['Int']['input']
}>

export type LondonFixDynamicQuery = {
  __typename?: 'Query'
  GetLondonFixV3?: {
    __typename?: 'LondonFix'
    ID: number
    currency: string
    results?: Array<{
      __typename?: 'LondonQuote'
      ID: number
      goldAM: number
      goldPM: number
      timestamp: number
      silver: number
      platinumAM: number
      platinumPM: number
      palladiumAM: number
      palladiumPM: number
    } | null> | null
  } | null
}

export type LondonFixByYearQueryVariables = Exact<{
  year: Scalars['String']['input']
  currency: Scalars['String']['input']
}>

export type LondonFixByYearQuery = {
  __typename?: 'Query'
  GetLondonFixByYearV3?: {
    __typename?: 'LondonFix'
    ID: number
    currency: string
    results?: Array<{
      __typename?: 'LondonQuote'
      ID: number
      goldAM: number
      goldPM: number
      timestamp: number
      silver: number
      platinumAM: number
      platinumPM: number
      palladiumAM: number
      palladiumPM: number
    } | null> | null
  } | null
}

export type LondonFixAndShanghaiFixQueryVariables = Exact<{
  currency: Scalars['String']['input']
  yesterday: Scalars['Int']['input']
  today: Scalars['Int']['input']
  symbol: Scalars['String']['input']
}>

export type LondonFixAndShanghaiFixQuery = {
  __typename?: 'Query'
  londonFix?: {
    __typename?: 'LondonFix'
    ID: number
    currency: string
    startTime: number
    endTime: number
    results?: Array<{
      __typename?: 'LondonQuote'
      ID: number
      goldAM: number
      goldPM: number
      timestamp: number
      silver: number
      platinumAM: number
      platinumPM: number
      palladiumAM: number
      palladiumPM: number
    } | null> | null
  } | null
  shanghaiFix?: {
    __typename?: 'ShanghaiFix'
    ID: number
    currency: string
    symbol: string
    results?: Array<{
      __typename?: 'ShanghaiQuote'
      ID: number
      timestamp: number
      am: number
      pm: number
    } | null> | null
  } | null
}

export type GoldRatiosQueryVariables = Exact<{
  symbols: Scalars['String']['input']
  timestamp: Scalars['Int']['input']
}>

export type GoldRatiosQuery = {
  __typename?: 'Query'
  gold?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  silver?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  palladium?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  platinum?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  quotes?: {
    __typename?: 'BarchartGetQuote'
    timestamp: number
    symbols: string
    results?: Array<{
      __typename?: 'BarchartQuote'
      lastPrice: number
      name: string
      serverTimestamp: string
      symbol: string
    } | null> | null
  } | null
  crudeOil?: {
    __typename?: 'BarchartGetFuturesByExchange'
    timestamp: number
    exchange: string
    results?: Array<{
      __typename?: 'BarchartFuture'
      name: string
      lastPrice: number
      netChange: number
      symbol: string
    } | null> | null
  } | null
}

export type ExchangeRatesTableQueryQueryVariables = Exact<{
  [key: string]: never
}>

export type ExchangeRatesTableQueryQuery = {
  __typename?: 'Query'
  GetLiveSpotGoldTableV3?: {
    __typename?: 'LiveSpotGoldTable'
    ID: string
    Symbol: string
    Table?: Array<{
      __typename?: 'LiveSpotGoldRow'
      Currency: string
      Rate: {
        __typename?: 'ExchangeRate'
        CurrencyToUsd: number
        UsdToCurrency: number
        ChangePercent: number
        NYTime: string
      }
      Gold: {
        __typename?: 'LiveSpotGoldPrice'
        Price: number
        Change: number
        ChangePercent: number
      }
    } | null> | null
  } | null
}

export type ForexFragmentFragment = {
  __typename?: 'ForexQuote'
  timestamp: number
  ask: number
  mid: number
  bid: number
  change: number
  changePercentage: number
  ctousd: number
  usdtoc: number
}

export type MetalFragmentFragment = {
  __typename?: 'Metal'
  ID: number
  symbol: string
  currency: string
  name: string
  results?: Array<{
    __typename?: 'Quote'
    ID: number
    ask: number
    bid: number
    change: number
    changePercentage: number
    close: number
    high: number
    low: number
    mid: number
    open: number
    originalTime: string
    timestamp: number
    unit: string
  } | null> | null
}

export type MetalQuoteFragmentFragment = {
  __typename?: 'Quote'
  ID: number
  ask: number
  bid: number
  change: number
  changePercentage: number
  close: number
  high: number
  low: number
  mid: number
  open: number
  originalTime: string
  timestamp: number
  unit: string
}

export type BarchartFragmentFragment = {
  __typename?: 'BarchartQuote'
  symbol: string
  name: string
  lastPrice: number
  netChange: number
  percentChange: number
  open: number
  high: number
  low: number
  close: number
}

export type LondonQuoteFragmentFragment = {
  __typename?: 'LondonQuote'
  ID: number
  goldAM: number
  goldPM: number
  timestamp: number
  silver: number
  platinumAM: number
  platinumPM: number
  palladiumAM: number
  palladiumPM: number
}

export type MiningPressReleaseQueueQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  queueId?: Scalars['String']['input']
}>

export type MiningPressReleaseQueueQuery = {
  __typename?: 'Query'
  ids?: Array<number | null> | null
  queue?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | {
          __typename?: 'PressRelease'
          id: number
          title: string
          teaserHeadline?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          url: string
        }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type NewsByCategoryGenericQueryVariables = Exact<{
  urlAlias: Scalars['String']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  includeRelatedCategories?: InputMaybe<Scalars['Boolean']['input']>
  includeEntityQueues?: InputMaybe<Scalars['Boolean']['input']>
}>

export type NewsByCategoryGenericQuery = {
  __typename?: 'Query'
  nodeListByCategory?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename: 'Commentary'
          title: string
          summaryBullets?: Array<string | null> | null
          urlAlias?: string | null
          teaserSnippet?: string | null
          legacyThumbnailImageUrl?: string | null
          author?: {
            __typename?: 'Author'
            authorWebsite?: string | null
            body?: string | null
            email?: string | null
            facebookId?: string | null
            name?: string | null
            imageUrl?: string | null
            linkedInId?: string | null
            title?: string | null
            twitterId?: string | null
            authorType?: string | null
            urlAlias?: string | null
            roles?: Array<string | null> | null
          } | null
          bodyWithEmbeddedMedia?: {
            __typename?: 'Body'
            value?: string | null
            embeddedMedia?: Array<{
              __typename?: 'EmbeddedMedia'
              assetUuid?: string | null
              snippetUuid?: string | null
              status?: boolean | null
              startTime?: number | null
              endTime?: number | null
              type?: string | null
              thumbnailUuid?: string | null
            } | null> | null
          } | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          supportingAuthors?: Array<{
            __typename?: 'Author'
            id: number
            name?: string | null
            urlAlias?: string | null
            imageUrl?: string | null
            twitterId?: string | null
            linkedInId?: string | null
            email?: string | null
            body?: string | null
          } | null> | null
          featuredContent?: {
            __typename?: 'EmbeddedMedia'
            type?: string | null
            assetUuid?: string | null
            snippetUuid?: string | null
            status?: boolean | null
            startTime?: number | null
            endTime?: number | null
            thumbnailUuid?: string | null
          } | null
          source?: {
            __typename?: 'Source'
            id?: number | null
            name?: string | null
            description?: string | null
            subtitle?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          audioTts?: {
            __typename?: 'AudioTts'
            isPublished?: boolean | null
            assetUuid?: string | null
            status?: boolean | null
            endTime?: number | null
            startTime?: number | null
          } | null
          tags?: Array<{
            __typename?: 'Tag'
            id: number
            name: string
            urlAlias: string
          } | null> | null
        }
      | { __typename?: 'LeadGen' }
      | {
          __typename: 'NewsArticle'
          id: number
          teaserSnippet?: string | null
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          source?: {
            __typename?: 'Source'
            name?: string | null
            subtitle?: string | null
            description?: string | null
          } | null
          audioTts?: {
            __typename?: 'AudioTts'
            isPublished?: boolean | null
            snippetUuid?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | {
          __typename: 'OffTheWire'
          id: number
          body?: string | null
          title: string
          teaserHeadline?: string | null
          teaserSnippet?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          imageUrl?: string | null
          featured?: boolean | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          source?: {
            __typename?: 'Source'
            name?: string | null
            subtitle?: string | null
            description?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type NewsOtwListQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type NewsOtwListQuery = {
  __typename?: 'Query'
  nodeList?: {
    __typename?: 'NodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | {
          __typename?: 'OffTheWire'
          id: number
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          teaserSnippet?: string | null
          legacyThumbnailImageUrl?: string | null
          summaryBullets?: Array<string | null> | null
          author?: {
            __typename?: 'Author'
            email?: string | null
            name?: string | null
            imageUrl?: string | null
            urlAlias?: string | null
          } | null
          source?: {
            __typename?: 'Source'
            id?: number | null
            description?: string | null
            name?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          featuredContent?: {
            __typename?: 'EmbeddedMedia'
            type?: string | null
            assetUuid?: string | null
            snippetUuid?: string | null
            status?: boolean | null
            startTime?: number | null
            endTime?: number | null
            thumbnailUuid?: string | null
          } | null
          tags?: Array<{
            __typename?: 'Tag'
            id: number
            name: string
            urlAlias: string
          } | null> | null
          audioTts?: {
            __typename?: 'AudioTts'
            isPublished?: boolean | null
            assetUuid?: string | null
            status?: boolean | null
            endTime?: number | null
            startTime?: number | null
          } | null
        }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type NewsOffTheWireQueryVariables = Exact<{
  urlAlias: Scalars['String']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type NewsOffTheWireQuery = {
  __typename?: 'Query'
  nodeListByCategory?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | {
          __typename?: 'OffTheWire'
          id: number
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          teaserSnippet?: string | null
          legacyThumbnailImageUrl?: string | null
          author?: {
            __typename?: 'Author'
            email?: string | null
            name?: string | null
            imageUrl?: string | null
            urlAlias?: string | null
          } | null
          source?: {
            __typename?: 'Source'
            id?: number | null
            description?: string | null
            name?: string | null
          } | null
        }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type NewsTrendingGenericQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
}>

export type NewsTrendingGenericQuery = {
  __typename?: 'Query'
  nodeListTrending?: Array<
    | { __typename?: 'BasicPage' }
    | { __typename?: 'BreakingNews' }
    | { __typename?: 'Commentary' }
    | { __typename?: 'LeadGen' }
    | {
        __typename: 'NewsArticle'
        id: number
        teaserSnippet?: string | null
        title: string
        teaserHeadline?: string | null
        urlAlias?: string | null
        createdAt?: string | null
        updatedAt?: string | null
        legacyThumbnailImageUrl?: string | null
        category?: {
          __typename?: 'Category'
          id: number
          name: string
          urlAlias: string
        } | null
        source?: {
          __typename?: 'Source'
          name?: string | null
          subtitle?: string | null
          description?: string | null
        } | null
        audioTts?: {
          __typename?: 'AudioTts'
          isPublished?: boolean | null
          snippetUuid?: string | null
        } | null
        image?: {
          __typename?: 'Image'
          detail?: {
            __typename?: 'ImageDetail'
            default?: {
              __typename?: 'SourceAttribute'
              srcset?: string | null
            } | null
            sources?: {
              __typename?: 'ImageDetailSources'
              teaser_small?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              teaser_medium?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              desktop?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              mobile?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              tablet?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
            } | null
          } | null
        } | null
        teaserImage?: {
          __typename?: 'Image'
          detail?: {
            __typename?: 'ImageDetail'
            default?: {
              __typename?: 'SourceAttribute'
              srcset?: string | null
            } | null
            sources?: {
              __typename?: 'ImageDetailSources'
              teaser_small?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              teaser_medium?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              desktop?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              mobile?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              tablet?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
            } | null
          } | null
        } | null
      }
    | { __typename?: 'OffTheWire' }
    | { __typename?: 'PressRelease' }
    | { __typename?: 'Sponsored' }
    | { __typename?: 'StreetTalk' }
    | null
  > | null
}

export type NewsOpinionsGenericQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type NewsOpinionsGenericQuery = {
  __typename?: 'Query'
  opinions?: {
    __typename?: 'NodeListWithPagination'
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename: 'Commentary'
          id: number
          teaserSnippet?: string | null
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          opinionType?: OpinionType | null
          legacyThumbnailImageUrl?: string | null
          author?: {
            __typename?: 'Author'
            authorWebsite?: string | null
            body?: string | null
            email?: string | null
            facebookId?: string | null
            name?: string | null
            imageUrl?: string | null
            linkedInId?: string | null
            title?: string | null
            twitterId?: string | null
            authorType?: string | null
            urlAlias?: string | null
            roles?: Array<string | null> | null
          } | null
          tags?: Array<{
            __typename?: 'Tag'
            id: number
            name: string
            urlAlias: string
          } | null> | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type NodeListQueueQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  queueId: Scalars['String']['input']
}>

export type NodeListQueueQuery = {
  __typename?: 'Query'
  ids?: Array<number | null> | null
  queue?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename: 'Commentary'
          id: number
          teaserSnippet?: string | null
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          opinionType?: OpinionType | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          author?: {
            __typename?: 'Author'
            authorWebsite?: string | null
            body?: string | null
            email?: string | null
            facebookId?: string | null
            name?: string | null
            imageUrl?: string | null
            linkedInId?: string | null
            title?: string | null
            twitterId?: string | null
            authorType?: string | null
            urlAlias?: string | null
            roles?: Array<string | null> | null
          } | null
          tags?: Array<{
            __typename?: 'Tag'
            id: number
            name: string
            urlAlias: string
          } | null> | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'LeadGen' }
      | {
          __typename: 'NewsArticle'
          id: number
          teaserSnippet?: string | null
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          source?: {
            __typename?: 'Source'
            name?: string | null
            subtitle?: string | null
            description?: string | null
          } | null
          audioTts?: {
            __typename?: 'AudioTts'
            isPublished?: boolean | null
            snippetUuid?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type NodeListNewsFeedQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  queueId: Scalars['String']['input']
}>

export type NodeListNewsFeedQuery = {
  __typename?: 'Query'
  ids?: Array<number | null> | null
  nodeListNewsFeed?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename: 'Commentary'
          id: number
          title: string
          featured?: boolean | null
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          source?: {
            __typename?: 'Source'
            name?: string | null
            subtitle?: string | null
            description?: string | null
          } | null
        }
      | { __typename?: 'LeadGen' }
      | {
          __typename: 'NewsArticle'
          id: number
          teaserSnippet?: string | null
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          source?: {
            __typename?: 'Source'
            name?: string | null
            subtitle?: string | null
            description?: string | null
          } | null
          audioTts?: {
            __typename?: 'AudioTts'
            isPublished?: boolean | null
            snippetUuid?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | {
          __typename: 'OffTheWire'
          id: number
          title: string
          featured?: boolean | null
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          source?: {
            __typename?: 'Source'
            name?: string | null
            subtitle?: string | null
            description?: string | null
          } | null
        }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type CommentaryListFilterCommentariessQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  opinionType: OpinionType
}>

export type CommentaryListFilterCommentariessQuery = {
  __typename?: 'Query'
  ids?: Array<number | null> | null
  commentaries?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename: 'Commentary'
          id: number
          teaserSnippet?: string | null
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          opinionType?: OpinionType | null
          legacyThumbnailImageUrl?: string | null
          author?: {
            __typename?: 'Author'
            authorWebsite?: string | null
            body?: string | null
            email?: string | null
            facebookId?: string | null
            name?: string | null
            imageUrl?: string | null
            linkedInId?: string | null
            title?: string | null
            twitterId?: string | null
            authorType?: string | null
            urlAlias?: string | null
            roles?: Array<string | null> | null
          } | null
          tags?: Array<{
            __typename?: 'Tag'
            id: number
            name: string
            urlAlias: string
          } | null> | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type NodeListQueueCommentariesQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type NodeListQueueCommentariesQuery = {
  __typename?: 'Query'
  ids?: Array<number | null> | null
  commentaries?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename: 'Commentary'
          id: number
          teaserSnippet?: string | null
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          opinionType?: OpinionType | null
          legacyThumbnailImageUrl?: string | null
          author?: {
            __typename?: 'Author'
            authorWebsite?: string | null
            body?: string | null
            email?: string | null
            facebookId?: string | null
            name?: string | null
            imageUrl?: string | null
            linkedInId?: string | null
            title?: string | null
            twitterId?: string | null
            authorType?: string | null
            urlAlias?: string | null
            roles?: Array<string | null> | null
          } | null
          tags?: Array<{
            __typename?: 'Tag'
            id: number
            name: string
            urlAlias: string
          } | null> | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type MarketNewsHomePageQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type MarketNewsHomePageQuery = {
  __typename?: 'Query'
  marketNews?: {
    __typename?: 'NodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | {
          __typename?: 'NewsArticle'
          id: number
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          source?: {
            __typename?: 'Source'
            description?: string | null
            id?: number | null
            name?: string | null
            subtitle?: string | null
          } | null
        }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type NewsGenericByTagQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  urlAlias: Scalars['String']['input']
}>

export type NewsGenericByTagQuery = {
  __typename?: 'Query'
  nodeListByTag?: {
    __typename?: 'NodeListWithPagination'
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | {
          __typename: 'NewsArticle'
          id: number
          teaserSnippet?: string | null
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          source?: {
            __typename?: 'Source'
            name?: string | null
            subtitle?: string | null
            description?: string | null
          } | null
          audioTts?: {
            __typename?: 'AudioTts'
            isPublished?: boolean | null
            snippetUuid?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type NewsGenericCommentariesQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type NewsGenericCommentariesQuery = {
  __typename?: 'Query'
  ids?: Array<number | null> | null
  commentaries?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename: 'Commentary'
          id: number
          createdAt?: string | null
          updatedAt?: string | null
          title: string
          teaserHeadline?: string | null
          teaserSnippet?: string | null
          urlAlias?: string | null
          legacyThumbnailImageUrl?: string | null
          source?: {
            __typename?: 'Source'
            description?: string | null
            id?: number | null
            name?: string | null
            subtitle?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          category?: {
            __typename?: 'Category'
            id: number
            urlAlias: string
            name: string
          } | null
          author?: {
            __typename?: 'Author'
            authorWebsite?: string | null
            body?: string | null
            email?: string | null
            facebookId?: string | null
            name?: string | null
            imageUrl?: string | null
            linkedInId?: string | null
            title?: string | null
            twitterId?: string | null
            authorType?: string | null
            urlAlias?: string | null
            roles?: Array<string | null> | null
          } | null
        }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type ArticleByAliasQueryVariables = Exact<{
  urlAlias: Scalars['String']['input']
  auHash?: InputMaybe<Scalars['String']['input']>
}>

export type ArticleByAliasQuery = {
  __typename?: 'Query'
  nodeByUrlAlias?:
    | {
        __typename: 'BasicPage'
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
      }
    | {
        __typename: 'BreakingNews'
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
      }
    | {
        __typename: 'Commentary'
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
      }
    | {
        __typename: 'LeadGen'
        body?: string | null
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
      }
    | {
        __typename: 'NewsArticle'
        summaryBullets?: Array<string | null> | null
        urlAlias?: string | null
        teaserSnippet?: string | null
        legacyThumbnailImageUrl?: string | null
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
        author?: {
          __typename?: 'Author'
          authorWebsite?: string | null
          body?: string | null
          email?: string | null
          contactEmail?: string | null
          facebookId?: string | null
          name?: string | null
          imageUrl?: string | null
          linkedInId?: string | null
          title?: string | null
          twitterId?: string | null
          authorType?: string | null
          urlAlias?: string | null
          roles?: Array<string | null> | null
        } | null
        featuredContent?: {
          __typename?: 'EmbeddedMedia'
          type?: string | null
          assetUuid?: string | null
          snippetUuid?: string | null
          status?: boolean | null
          startTime?: number | null
          endTime?: number | null
          thumbnailUuid?: string | null
        } | null
        bodyWithEmbeddedMedia?: {
          __typename?: 'Body'
          value?: string | null
          embeddedMedia?: Array<{
            __typename?: 'EmbeddedMedia'
            assetUuid?: string | null
            snippetUuid?: string | null
            status?: boolean | null
            startTime?: number | null
            endTime?: number | null
            type?: string | null
            thumbnailUuid?: string | null
          } | null> | null
        } | null
        source?: {
          __typename?: 'Source'
          id?: number | null
          name?: string | null
          description?: string | null
          subtitle?: string | null
        } | null
        category?: {
          __typename?: 'Category'
          id: number
          name: string
          urlAlias: string
        } | null
        image?: {
          __typename?: 'Image'
          detail?: {
            __typename?: 'ImageDetail'
            default?: {
              __typename?: 'SourceAttribute'
              srcset?: string | null
            } | null
            sources?: {
              __typename?: 'ImageDetailSources'
              teaser_small?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              teaser_medium?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              desktop?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              mobile?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              tablet?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
            } | null
          } | null
        } | null
        audioTts?: {
          __typename?: 'AudioTts'
          isPublished?: boolean | null
          assetUuid?: string | null
          status?: boolean | null
          endTime?: number | null
          startTime?: number | null
        } | null
        tags?: Array<{
          __typename?: 'Tag'
          id: number
          name: string
          urlAlias: string
        } | null> | null
        supportingAuthors?: Array<{
          __typename?: 'Author'
          id: number
          name?: string | null
          urlAlias?: string | null
          imageUrl?: string | null
          twitterId?: string | null
          linkedInId?: string | null
          email?: string | null
          body?: string | null
        } | null> | null
      }
    | {
        __typename: 'OffTheWire'
        urlAlias?: string | null
        summaryBullets?: Array<string | null> | null
        featured?: boolean | null
        body?: string | null
        legacyThumbnailImageUrl?: string | null
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
        author?: {
          __typename?: 'Author'
          authorWebsite?: string | null
          body?: string | null
          email?: string | null
          facebookId?: string | null
          name?: string | null
          imageUrl?: string | null
          linkedInId?: string | null
          title?: string | null
          twitterId?: string | null
          authorType?: string | null
          urlAlias?: string | null
          roles?: Array<string | null> | null
        } | null
        source?: {
          __typename?: 'Source'
          name?: string | null
          subtitle?: string | null
          description?: string | null
        } | null
        image?: {
          __typename?: 'Image'
          detail?: {
            __typename?: 'ImageDetail'
            default?: {
              __typename?: 'SourceAttribute'
              srcset?: string | null
            } | null
            sources?: {
              __typename?: 'ImageDetailSources'
              teaser_small?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              teaser_medium?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              desktop?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              mobile?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              tablet?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
            } | null
          } | null
        } | null
        featuredContent?: {
          __typename?: 'EmbeddedMedia'
          type?: string | null
          assetUuid?: string | null
          snippetUuid?: string | null
          status?: boolean | null
          startTime?: number | null
          endTime?: number | null
          thumbnailUuid?: string | null
        } | null
        tags?: Array<{
          __typename?: 'Tag'
          id: number
          name: string
          urlAlias: string
        } | null> | null
      }
    | {
        __typename: 'PressRelease'
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
      }
    | {
        __typename: 'Sponsored'
        urlAlias?: string | null
        teaserSnippet?: string | null
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
        author?: {
          __typename?: 'Author'
          authorWebsite?: string | null
          body?: string | null
          email?: string | null
          facebookId?: string | null
          name?: string | null
          imageUrl?: string | null
          linkedInId?: string | null
          title?: string | null
          twitterId?: string | null
          authorType?: string | null
          urlAlias?: string | null
          roles?: Array<string | null> | null
        } | null
        bodyWithEmbeddedMedia?: {
          __typename?: 'Body'
          value?: string | null
          embeddedMedia?: Array<{
            __typename?: 'EmbeddedMedia'
            assetUuid?: string | null
            snippetUuid?: string | null
            status?: boolean | null
            startTime?: number | null
            endTime?: number | null
            type?: string | null
            thumbnailUuid?: string | null
          } | null> | null
        } | null
        source?: {
          __typename?: 'Source'
          id?: number | null
          name?: string | null
          description?: string | null
          subtitle?: string | null
        } | null
        image?: {
          __typename?: 'Image'
          detail?: {
            __typename?: 'ImageDetail'
            default?: {
              __typename?: 'SourceAttribute'
              srcset?: string | null
            } | null
            sources?: {
              __typename?: 'ImageDetailSources'
              teaser_small?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              teaser_medium?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              desktop?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              mobile?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              tablet?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
            } | null
          } | null
        } | null
        audioTts?: {
          __typename?: 'AudioTts'
          isPublished?: boolean | null
          assetUuid?: string | null
          status?: boolean | null
          endTime?: number | null
          startTime?: number | null
        } | null
        tags?: Array<{
          __typename?: 'Tag'
          id: number
          name: string
          urlAlias: string
        } | null> | null
      }
    | {
        __typename: 'StreetTalk'
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
      }
    | null
}

export type NodeListByAuthorQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  urlAlias: Scalars['String']['input']
}>

export type NodeListByAuthorQuery = {
  __typename?: 'Query'
  nodeListByAuthor?: {
    __typename?: 'NodeListWithPagination'
    total: number
    items?: Array<
      | {
          __typename: 'BasicPage'
          id: number
          title: string
          createdAt?: string | null
          updatedAt?: string | null
        }
      | {
          __typename: 'BreakingNews'
          id: number
          title: string
          createdAt?: string | null
          updatedAt?: string | null
        }
      | {
          __typename: 'Commentary'
          id: number
          teaserSnippet?: string | null
          title: string
          urlAlias?: string | null
          legacyThumbnailImageUrl?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          source?: {
            __typename?: 'Source'
            description?: string | null
            id?: number | null
            name?: string | null
            subtitle?: string | null
          } | null
          author?: {
            __typename?: 'Author'
            id: number
            name?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | {
          __typename: 'LeadGen'
          id: number
          title: string
          createdAt?: string | null
          updatedAt?: string | null
        }
      | {
          __typename: 'NewsArticle'
          id: number
          title: string
          createdAt?: string | null
          updatedAt?: string | null
          teaserSnippet?: string | null
          teaserHeadline?: string | null
          urlAlias?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          source?: {
            __typename?: 'Source'
            name?: string | null
            subtitle?: string | null
            description?: string | null
          } | null
          audioTts?: {
            __typename?: 'AudioTts'
            isPublished?: boolean | null
            snippetUuid?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | {
          __typename: 'OffTheWire'
          id: number
          title: string
          createdAt?: string | null
          updatedAt?: string | null
          body?: string | null
          teaserHeadline?: string | null
          teaserSnippet?: string | null
          urlAlias?: string | null
          imageUrl?: string | null
          featured?: boolean | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          source?: {
            __typename?: 'Source'
            name?: string | null
            subtitle?: string | null
            description?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | {
          __typename: 'PressRelease'
          id: number
          title: string
          createdAt?: string | null
          updatedAt?: string | null
        }
      | {
          __typename: 'Sponsored'
          id: number
          title: string
          createdAt?: string | null
          updatedAt?: string | null
        }
      | {
          __typename: 'StreetTalk'
          id: number
          title: string
          createdAt?: string | null
          updatedAt?: string | null
        }
    > | null
  } | null
}

export type AuthorByUrlAliasQueryVariables = Exact<{
  urlAlias: Scalars['String']['input']
}>

export type AuthorByUrlAliasQuery = {
  __typename?: 'Query'
  authorByUrlAlias?: {
    __typename?: 'Author'
    authorWebsite?: string | null
    body?: string | null
    email?: string | null
    contactEmail?: string | null
    facebookId?: string | null
    name?: string | null
    hidden?: boolean | null
    id: number
    imageUrl?: string | null
    linkedInId?: string | null
    title?: string | null
    authorType?: string | null
    twitterId?: string | null
    roles?: Array<string | null> | null
  } | null
}

export type NewsCategoriesTreeQueryVariables = Exact<{ [key: string]: never }>

export type NewsCategoriesTreeQuery = {
  __typename?: 'Query'
  categoriesTree?: Array<{
    __typename?: 'Category'
    id: number
    name: string
    urlAlias: string
    status?: boolean | null
    children?: Array<{
      __typename?: 'Category'
      id: number
      name: string
      urlAlias: string
      status?: boolean | null
      children?: Array<{
        __typename?: 'Category'
        id: number
        name: string
        urlAlias: string
        status?: boolean | null
        children?: Array<{ __typename?: 'Category'; id: number } | null> | null
      } | null> | null
    } | null> | null
  } | null> | null
}

export type SponsorByUrlAliasQueryVariables = Exact<{
  urlAlias: Scalars['String']['input']
}>

export type SponsorByUrlAliasQuery = {
  __typename?: 'Query'
  sponsor?: {
    __typename?: 'Sponsor'
    id?: number | null
    name?: string | null
  } | null
}

export type SponsoredContentQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type SponsoredContentQuery = {
  __typename?: 'Query'
  nodeList?: {
    __typename?: 'NodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | {
          __typename?: 'Sponsored'
          id: number
          title: string
          teaserHeadline?: string | null
          teaserSnippet?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          legacyThumbnailImageUrl?: string | null
          bodyWithEmbeddedMedia?: {
            __typename?: 'Body'
            value?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          author?: {
            __typename?: 'Author'
            id: number
            name?: string | null
            urlAlias?: string | null
          } | null
        }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type ReportersQueryVariables = Exact<{ [key: string]: never }>

export type ReportersQuery = {
  __typename?: 'Query'
  reporters?: Array<{
    __typename?: 'Author'
    id: number
    name?: string | null
    imageUrl?: string | null
    urlAlias?: string | null
    hidden?: boolean | null
  } | null> | null
}

export type NewsTopContributorsQueryVariables = Exact<{ [key: string]: never }>

export type NewsTopContributorsQuery = {
  __typename?: 'Query'
  topContributors?: Array<{
    __typename?: 'Author'
    id: number
    name?: string | null
    imageUrl?: string | null
    urlAlias?: string | null
    hidden?: boolean | null
  } | null> | null
}

export type NodeListBySponsorQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  urlAlias: Scalars['String']['input']
}>

export type NodeListBySponsorQuery = {
  __typename?: 'Query'
  nodeListBySponsor?: {
    __typename?: 'NodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | {
          __typename?: 'Sponsored'
          id: number
          createdAt?: string | null
          updatedAt?: string | null
          title: string
          urlAlias?: string | null
          teaserSnippet?: string | null
          legacyThumbnailImageUrl?: string | null
          sponsor?: {
            __typename?: 'Sponsor'
            id?: number | null
            name?: string | null
          } | null
          author?: {
            __typename?: 'Author'
            id: number
            name?: string | null
            urlAlias?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type NewsTrendingTagsQueryVariables = Exact<{ [key: string]: never }>

export type NewsTrendingTagsQuery = {
  __typename?: 'Query'
  trendingTags?: Array<{
    __typename?: 'Tag'
    id: number
    urlAlias: string
    name: string
  } | null> | null
}

export type NewsIndexPageQueryQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type NewsIndexPageQueryQuery = {
  __typename?: 'Query'
  queue?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename: 'Commentary'
          id: number
          title: string
          teaserSnippet?: string | null
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          source?: {
            __typename?: 'Source'
            name?: string | null
            subtitle?: string | null
            description?: string | null
          } | null
          audioTts?: {
            __typename?: 'AudioTts'
            isPublished?: boolean | null
            assetUuid?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'LeadGen' }
      | {
          __typename: 'NewsArticle'
          id: number
          title: string
          teaserSnippet?: string | null
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          source?: {
            __typename?: 'Source'
            name?: string | null
            subtitle?: string | null
            description?: string | null
          } | null
          audioTts?: {
            __typename?: 'AudioTts'
            isPublished?: boolean | null
            assetUuid?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
        }
      | {
          __typename: 'OffTheWire'
          id: number
          title: string
          body?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          imageUrl?: string | null
          featured?: boolean | null
          legacyThumbnailImageUrl?: string | null
          teaserSnippet?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          source?: {
            __typename?: 'Source'
            name?: string | null
            subtitle?: string | null
            description?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
            } | null
          } | null
          audioTts?: {
            __typename?: 'AudioTts'
            isPublished?: boolean | null
            assetUuid?: string | null
          } | null
        }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type BreakingNewsQueryVariables = Exact<{ [key: string]: never }>

export type BreakingNewsQuery = {
  __typename?: 'Query'
  nodeList?: {
    __typename?: 'NodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | {
          __typename?: 'BreakingNews'
          id: number
          title: string
          createdAt?: string | null
          updatedAt?: string | null
          category?: string | null
          byline?: string | null
          url?: string | null
        }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type NewsLeadGenQueryQueryVariables = Exact<{ [key: string]: never }>

export type NewsLeadGenQueryQuery = {
  __typename?: 'Query'
  queue?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | {
          __typename?: 'LeadGen'
          id: number
          title: string
          urlAlias?: string | null
          featured?: boolean | null
        }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type StreetNewsHomePageQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type StreetNewsHomePageQuery = {
  __typename?: 'Query'
  nodeList?: {
    __typename?: 'NodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | {
          __typename?: 'StreetTalk'
          id: number
          title: string
          teaserHeadline?: string | null
          source?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          url: string
        }
    > | null
  } | null
}

export type NewsCategoryChildrenQueryVariables = Exact<{
  urlAlias: Scalars['String']['input']
}>

export type NewsCategoryChildrenQuery = {
  __typename?: 'Query'
  categoryChildrenByUrlAlias?: Array<{
    __typename?: 'Category'
    id: number
    urlAlias: string
    name: string
  } | null> | null
}

export type TagByUrlAliasQueryVariables = Exact<{
  urlAlias: Scalars['String']['input']
}>

export type TagByUrlAliasQuery = {
  __typename?: 'Query'
  tagByUrlAlias?: {
    __typename?: 'Tag'
    id: number
    name: string
    urlAlias: string
  } | null
}

export type NodeListByTagQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  urlAlias: Scalars['String']['input']
}>

export type NodeListByTagQuery = {
  __typename?: 'Query'
  nodeListByTag?: {
    __typename?: 'NodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename: 'Commentary'
          id: number
          teaserSnippet?: string | null
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          opinionType?: OpinionType | null
          legacyThumbnailImageUrl?: string | null
          author?: {
            __typename?: 'Author'
            authorWebsite?: string | null
            body?: string | null
            email?: string | null
            facebookId?: string | null
            name?: string | null
            imageUrl?: string | null
            linkedInId?: string | null
            title?: string | null
            twitterId?: string | null
            authorType?: string | null
            urlAlias?: string | null
            roles?: Array<string | null> | null
          } | null
          tags?: Array<{
            __typename?: 'Tag'
            id: number
            name: string
            urlAlias: string
          } | null> | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'LeadGen' }
      | {
          __typename: 'NewsArticle'
          id: number
          teaserSnippet?: string | null
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          source?: {
            __typename?: 'Source'
            name?: string | null
            subtitle?: string | null
            description?: string | null
          } | null
          audioTts?: {
            __typename?: 'AudioTts'
            isPublished?: boolean | null
            snippetUuid?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type GuestByUrlAliasQueryVariables = Exact<{
  urlAlias: Scalars['String']['input']
}>

export type GuestByUrlAliasQuery = {
  __typename?: 'Query'
  guest?: {
    __typename?: 'Guest'
    id: number
    fullName: string
    urlAlias?: string | null
  } | null
}

export type NodeListByGuestQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
  urlAlias: Scalars['String']['input']
}>

export type NodeListByGuestQuery = {
  __typename?: 'Query'
  guestNodes?: {
    __typename?: 'NodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename: 'Commentary'
          id: number
          teaserSnippet?: string | null
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          opinionType?: OpinionType | null
          legacyThumbnailImageUrl?: string | null
          author?: {
            __typename?: 'Author'
            authorWebsite?: string | null
            body?: string | null
            email?: string | null
            facebookId?: string | null
            name?: string | null
            imageUrl?: string | null
            linkedInId?: string | null
            title?: string | null
            twitterId?: string | null
            authorType?: string | null
            urlAlias?: string | null
            roles?: Array<string | null> | null
          } | null
          tags?: Array<{
            __typename?: 'Tag'
            id: number
            name: string
            urlAlias: string
          } | null> | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'LeadGen' }
      | {
          __typename: 'NewsArticle'
          id: number
          teaserSnippet?: string | null
          title: string
          teaserHeadline?: string | null
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          source?: {
            __typename?: 'Source'
            name?: string | null
            subtitle?: string | null
            description?: string | null
          } | null
          audioTts?: {
            __typename?: 'AudioTts'
            isPublished?: boolean | null
            snippetUuid?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          teaserImage?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
        }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | {
          __typename?: 'Sponsored'
          title: string
          teaserHeadline?: string | null
          id: number
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
        }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type SearchMetalsQueryVariables = Exact<{
  symbol: Scalars['String']['input']
  currency: Scalars['String']['input']
  timestamp: Scalars['Int']['input']
}>

export type SearchMetalsQuery = {
  __typename?: 'Query'
  metalData?: {
    __typename?: 'Metal'
    ID: number
    symbol: string
    currency: string
    name: string
    results?: Array<{
      __typename?: 'Quote'
      ID: number
      ask: number
      bid: number
      change: number
      changePercentage: number
      close: number
      high: number
      low: number
      mid: number
      open: number
      originalTime: string
      timestamp: number
      unit: string
    } | null> | null
  } | null
  cryptoData?: {
    __typename?: 'Crypto'
    symbol: string
    currency: string
    results?: Array<{
      __typename?: 'Quote'
      ask: number
      bid: number
      high: number
      low: number
      open: number
      close: number
      change: number
      changePercentage: number
    } | null> | null
  } | null
}

export type SearchNewsQueryVariables = Exact<{
  query: Scalars['String']['input']
  sort: Scalars['String']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type SearchNewsQuery = {
  __typename?: 'Query'
  searchData?: {
    __typename?: 'NodeListSearchWithPagination'
    total: number
    items?: Array<{
      __typename?: 'SearchResult'
      excerpt?: string | null
      id?: number | null
      relevance?: number | null
      title?: string | null
      urlAlias?: string | null
      updatedAt?: string | null
      legacyThumbnailImageUrl?: string | null
      thumbnail?: string | null
    }> | null
  } | null
}

export type CategoryFragmentFragment = {
  __typename?: 'Category'
  id: number
  name: string
  urlAlias: string
}

export type SourceFragmentFragment = {
  __typename?: 'Source'
  description?: string | null
  id?: number | null
  name?: string | null
  subtitle?: string | null
}

export type ImageFragmentFragment = {
  __typename?: 'Image'
  detail?: {
    __typename?: 'ImageDetail'
    default?: { __typename?: 'SourceAttribute'; srcset?: string | null } | null
    sources?: {
      __typename?: 'ImageDetailSources'
      teaser_small?: {
        __typename?: 'SourceAttribute'
        srcset?: string | null
        media?: string | null
      } | null
      teaser_medium?: {
        __typename?: 'SourceAttribute'
        srcset?: string | null
        media?: string | null
      } | null
      desktop?: {
        __typename?: 'SourceAttribute'
        media?: string | null
        srcset?: string | null
      } | null
      mobile?: {
        __typename?: 'SourceAttribute'
        media?: string | null
        srcset?: string | null
      } | null
      tablet?: {
        __typename?: 'SourceAttribute'
        media?: string | null
        srcset?: string | null
      } | null
    } | null
  } | null
}

export type ArticleTeaserFragmentFragment = {
  __typename: 'NewsArticle'
  id: number
  teaserSnippet?: string | null
  title: string
  teaserHeadline?: string | null
  urlAlias?: string | null
  createdAt?: string | null
  updatedAt?: string | null
  legacyThumbnailImageUrl?: string | null
  category?: {
    __typename?: 'Category'
    id: number
    name: string
    urlAlias: string
  } | null
  source?: {
    __typename?: 'Source'
    name?: string | null
    subtitle?: string | null
    description?: string | null
  } | null
  audioTts?: {
    __typename?: 'AudioTts'
    isPublished?: boolean | null
    snippetUuid?: string | null
  } | null
  image?: {
    __typename?: 'Image'
    detail?: {
      __typename?: 'ImageDetail'
      default?: {
        __typename?: 'SourceAttribute'
        srcset?: string | null
      } | null
      sources?: {
        __typename?: 'ImageDetailSources'
        teaser_small?: {
          __typename?: 'SourceAttribute'
          srcset?: string | null
          media?: string | null
        } | null
        teaser_medium?: {
          __typename?: 'SourceAttribute'
          srcset?: string | null
          media?: string | null
        } | null
        desktop?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
        mobile?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
        tablet?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
      } | null
    } | null
  } | null
  teaserImage?: {
    __typename?: 'Image'
    detail?: {
      __typename?: 'ImageDetail'
      default?: {
        __typename?: 'SourceAttribute'
        srcset?: string | null
      } | null
      sources?: {
        __typename?: 'ImageDetailSources'
        teaser_small?: {
          __typename?: 'SourceAttribute'
          srcset?: string | null
          media?: string | null
        } | null
        teaser_medium?: {
          __typename?: 'SourceAttribute'
          srcset?: string | null
          media?: string | null
        } | null
        desktop?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
        mobile?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
        tablet?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
      } | null
    } | null
  } | null
}

export type OffTheWireFragmentFragment = {
  __typename: 'OffTheWire'
  id: number
  body?: string | null
  title: string
  teaserHeadline?: string | null
  teaserSnippet?: string | null
  urlAlias?: string | null
  createdAt?: string | null
  updatedAt?: string | null
  imageUrl?: string | null
  featured?: boolean | null
  legacyThumbnailImageUrl?: string | null
  category?: {
    __typename?: 'Category'
    id: number
    name: string
    urlAlias: string
  } | null
  source?: {
    __typename?: 'Source'
    name?: string | null
    subtitle?: string | null
    description?: string | null
  } | null
  image?: {
    __typename?: 'Image'
    detail?: {
      __typename?: 'ImageDetail'
      default?: {
        __typename?: 'SourceAttribute'
        srcset?: string | null
      } | null
      sources?: {
        __typename?: 'ImageDetailSources'
        teaser_small?: {
          __typename?: 'SourceAttribute'
          srcset?: string | null
          media?: string | null
        } | null
        teaser_medium?: {
          __typename?: 'SourceAttribute'
          srcset?: string | null
          media?: string | null
        } | null
        desktop?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
        mobile?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
        tablet?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
      } | null
    } | null
  } | null
}

export type VideoSnippetFragmentFragment = {
  __typename: 'VideoSnippet'
  id?: number | null
  createdAt?: number | null
  description?: string | null
  endTime?: number | null
  frontendPath?: string | null
  headline?: string | null
  source?: string | null
  startTime?: number | null
  status?: number | null
  thumbnailUuid?: string | null
  updatedAt?: number | null
  uuid?: string | null
  category?: {
    __typename?: 'VideoCategory'
    id: number
    name: string
    urlAlias?: string | null
  } | null
  guests?: Array<{
    __typename?: 'VideoGuest'
    id: number
    name: string
  } | null> | null
  tags?: Array<{
    __typename?: 'VideoTag'
    id: number
    name: string
  } | null> | null
  video?: { __typename?: 'VideoVideo'; id: number; uuid: string } | null
}

export type CommentaryTeaserFragmentFragment = {
  __typename: 'Commentary'
  id: number
  teaserSnippet?: string | null
  title: string
  teaserHeadline?: string | null
  urlAlias?: string | null
  createdAt?: string | null
  updatedAt?: string | null
  opinionType?: OpinionType | null
  legacyThumbnailImageUrl?: string | null
  author?: {
    __typename?: 'Author'
    authorWebsite?: string | null
    body?: string | null
    email?: string | null
    facebookId?: string | null
    name?: string | null
    imageUrl?: string | null
    linkedInId?: string | null
    title?: string | null
    twitterId?: string | null
    authorType?: string | null
    urlAlias?: string | null
    roles?: Array<string | null> | null
  } | null
  tags?: Array<{
    __typename?: 'Tag'
    id: number
    name: string
    urlAlias: string
  } | null> | null
  image?: {
    __typename?: 'Image'
    detail?: {
      __typename?: 'ImageDetail'
      default?: {
        __typename?: 'SourceAttribute'
        srcset?: string | null
      } | null
      sources?: {
        __typename?: 'ImageDetailSources'
        teaser_small?: {
          __typename?: 'SourceAttribute'
          srcset?: string | null
          media?: string | null
        } | null
        teaser_medium?: {
          __typename?: 'SourceAttribute'
          srcset?: string | null
          media?: string | null
        } | null
        desktop?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
        mobile?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
        tablet?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
      } | null
    } | null
  } | null
  teaserImage?: {
    __typename?: 'Image'
    detail?: {
      __typename?: 'ImageDetail'
      default?: {
        __typename?: 'SourceAttribute'
        srcset?: string | null
      } | null
      sources?: {
        __typename?: 'ImageDetailSources'
        teaser_small?: {
          __typename?: 'SourceAttribute'
          srcset?: string | null
          media?: string | null
        } | null
        teaser_medium?: {
          __typename?: 'SourceAttribute'
          srcset?: string | null
          media?: string | null
        } | null
        desktop?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
        mobile?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
        tablet?: {
          __typename?: 'SourceAttribute'
          media?: string | null
          srcset?: string | null
        } | null
      } | null
    } | null
  } | null
}

export type AuthorFragmentFragment = {
  __typename?: 'Author'
  authorWebsite?: string | null
  body?: string | null
  email?: string | null
  facebookId?: string | null
  name?: string | null
  imageUrl?: string | null
  linkedInId?: string | null
  title?: string | null
  twitterId?: string | null
  authorType?: string | null
  urlAlias?: string | null
  roles?: Array<string | null> | null
}

export type OpinionByUrlAliasQueryVariables = Exact<{
  urlAlias: Scalars['String']['input']
  auHash?: InputMaybe<Scalars['String']['input']>
}>

export type OpinionByUrlAliasQuery = {
  __typename?: 'Query'
  nodeByUrlAlias?:
    | {
        __typename: 'BasicPage'
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
      }
    | {
        __typename: 'BreakingNews'
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
      }
    | {
        __typename: 'Commentary'
        summaryBullets?: Array<string | null> | null
        urlAlias?: string | null
        teaserSnippet?: string | null
        legacyThumbnailImageUrl?: string | null
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
        author?: {
          __typename?: 'Author'
          authorWebsite?: string | null
          body?: string | null
          email?: string | null
          facebookId?: string | null
          name?: string | null
          imageUrl?: string | null
          linkedInId?: string | null
          title?: string | null
          twitterId?: string | null
          authorType?: string | null
          urlAlias?: string | null
          roles?: Array<string | null> | null
        } | null
        bodyWithEmbeddedMedia?: {
          __typename?: 'Body'
          value?: string | null
          embeddedMedia?: Array<{
            __typename?: 'EmbeddedMedia'
            assetUuid?: string | null
            snippetUuid?: string | null
            status?: boolean | null
            startTime?: number | null
            endTime?: number | null
            type?: string | null
            thumbnailUuid?: string | null
          } | null> | null
        } | null
        category?: {
          __typename?: 'Category'
          id: number
          name: string
          urlAlias: string
        } | null
        supportingAuthors?: Array<{
          __typename?: 'Author'
          id: number
          name?: string | null
          urlAlias?: string | null
          imageUrl?: string | null
          twitterId?: string | null
          linkedInId?: string | null
          email?: string | null
          body?: string | null
        } | null> | null
        featuredContent?: {
          __typename?: 'EmbeddedMedia'
          type?: string | null
          assetUuid?: string | null
          snippetUuid?: string | null
          status?: boolean | null
          startTime?: number | null
          endTime?: number | null
          thumbnailUuid?: string | null
        } | null
        source?: {
          __typename?: 'Source'
          id?: number | null
          name?: string | null
          description?: string | null
          subtitle?: string | null
        } | null
        image?: {
          __typename?: 'Image'
          detail?: {
            __typename?: 'ImageDetail'
            default?: {
              __typename?: 'SourceAttribute'
              srcset?: string | null
            } | null
            sources?: {
              __typename?: 'ImageDetailSources'
              teaser_small?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              teaser_medium?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              desktop?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              mobile?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              tablet?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
            } | null
          } | null
        } | null
        teaserImage?: {
          __typename?: 'Image'
          detail?: {
            __typename?: 'ImageDetail'
            default?: {
              __typename?: 'SourceAttribute'
              srcset?: string | null
            } | null
            sources?: {
              __typename?: 'ImageDetailSources'
              teaser_small?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              teaser_medium?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
                media?: string | null
              } | null
              desktop?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              mobile?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
              tablet?: {
                __typename?: 'SourceAttribute'
                media?: string | null
                srcset?: string | null
              } | null
            } | null
          } | null
        } | null
        audioTts?: {
          __typename?: 'AudioTts'
          isPublished?: boolean | null
          assetUuid?: string | null
          status?: boolean | null
          endTime?: number | null
          startTime?: number | null
        } | null
        tags?: Array<{
          __typename?: 'Tag'
          id: number
          name: string
          urlAlias: string
        } | null> | null
      }
    | {
        __typename: 'LeadGen'
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
      }
    | {
        __typename: 'NewsArticle'
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
      }
    | {
        __typename: 'OffTheWire'
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
      }
    | {
        __typename: 'PressRelease'
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
      }
    | {
        __typename: 'Sponsored'
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
      }
    | {
        __typename: 'StreetTalk'
        createdAt?: string | null
        updatedAt?: string | null
        id: number
        published?: boolean | null
        title: string
      }
    | null
}

export type OpinionsByCategoryGenericQueryVariables = Exact<{
  urlAlias: Scalars['String']['input']
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type OpinionsByCategoryGenericQuery = {
  __typename?: 'Query'
  nodeListByCategory?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    total: number
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename: 'Commentary'
          id: number
          teaserSnippet?: string | null
          title: string
          urlAlias?: string | null
          createdAt?: string | null
          updatedAt?: string | null
          legacyThumbnailImageUrl?: string | null
          category?: {
            __typename?: 'Category'
            id: number
            name: string
            urlAlias: string
          } | null
          bodyWithEmbeddedMedia?: {
            __typename?: 'Body'
            value?: string | null
          } | null
          image?: {
            __typename?: 'Image'
            detail?: {
              __typename?: 'ImageDetail'
              default?: {
                __typename?: 'SourceAttribute'
                srcset?: string | null
              } | null
              sources?: {
                __typename?: 'ImageDetailSources'
                teaser_small?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                teaser_medium?: {
                  __typename?: 'SourceAttribute'
                  srcset?: string | null
                  media?: string | null
                } | null
                desktop?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                mobile?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
                tablet?: {
                  __typename?: 'SourceAttribute'
                  media?: string | null
                  srcset?: string | null
                } | null
              } | null
            } | null
          } | null
          author?: {
            __typename?: 'Author'
            authorWebsite?: string | null
            id: number
            name?: string | null
            urlAlias?: string | null
            body?: string | null
            email?: string | null
            facebookId?: string | null
            imageUrl?: string | null
            linkedInId?: string | null
            title?: string | null
            twitterId?: string | null
            authorType?: string | null
            roles?: Array<string | null> | null
          } | null
        }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type GetAllAuthorsQueryVariables = Exact<{ [key: string]: never }>

export type GetAllAuthorsQuery = {
  __typename?: 'Query'
  reporters?: Array<{
    __typename?: 'Author'
    urlAlias?: string | null
    hidden?: boolean | null
  } | null> | null
}

export type GetAllCategoriesQueryVariables = Exact<{ [key: string]: never }>

export type GetAllCategoriesQuery = {
  __typename?: 'Query'
  categoriesTree?: Array<{
    __typename?: 'Category'
    urlAlias: string
    children?: Array<{
      __typename?: 'Category'
      urlAlias: string
      children?: Array<{
        __typename?: 'Category'
        urlAlias: string
        children?: Array<{
          __typename?: 'Category'
          urlAlias: string
        } | null> | null
      } | null> | null
    } | null> | null
  } | null> | null
}

export type GetAllLeadGenQueryVariables = Exact<{ [key: string]: never }>

export type GetAllLeadGenQuery = {
  __typename?: 'Query'
  queue?: {
    __typename?: 'PreExecuteNodeListWithPagination'
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | {
          __typename?: 'LeadGen'
          urlAlias?: string | null
          updatedAt?: string | null
        }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type GetAllNewsArticlesQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type GetAllNewsArticlesQuery = {
  __typename?: 'Query'
  nodeList?: {
    __typename?: 'NodeListWithPagination'
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | {
          __typename?: 'NewsArticle'
          urlAlias?: string | null
          updatedAt?: string | null
          title: string
        }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type GetAllOffTheWireQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type GetAllOffTheWireQuery = {
  __typename?: 'Query'
  nodeList?: {
    __typename?: 'NodeListWithPagination'
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | { __typename?: 'Commentary' }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | {
          __typename?: 'OffTheWire'
          urlAlias?: string | null
          updatedAt?: string | null
          title: string
        }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type GetAllOpinionsQueryVariables = Exact<{
  limit?: InputMaybe<Scalars['Int']['input']>
  offset?: InputMaybe<Scalars['Int']['input']>
}>

export type GetAllOpinionsQuery = {
  __typename?: 'Query'
  nodeList?: {
    __typename?: 'NodeListWithPagination'
    items?: Array<
      | { __typename?: 'BasicPage' }
      | { __typename?: 'BreakingNews' }
      | {
          __typename?: 'Commentary'
          urlAlias?: string | null
          updatedAt?: string | null
          title: string
        }
      | { __typename?: 'LeadGen' }
      | { __typename?: 'NewsArticle' }
      | { __typename?: 'OffTheWire' }
      | { __typename?: 'PressRelease' }
      | { __typename?: 'Sponsored' }
      | { __typename?: 'StreetTalk' }
    > | null
  } | null
}

export type GetAllTagsQueryVariables = Exact<{ [key: string]: never }>

export type GetAllTagsQuery = {
  __typename?: 'Query'
  trendingTags?: Array<{ __typename?: 'Tag'; urlAlias: string } | null> | null
}

export type GetAllVideosQueryVariables = Exact<{
  limit: Scalars['Int']['input']
  offset: Scalars['Int']['input']
}>

export type GetAllVideosQuery = {
  __typename?: 'Query'
  VideoLatestVideos?: {
    __typename?: 'VideoSearchSnippetsResponse'
    snippets?: Array<{
      __typename?: 'VideoSnippet'
      thumbnailUuid?: string | null
      frontendPath?: string | null
      headline?: string | null
      uuid?: string | null
      video?: {
        __typename?: 'VideoVideo'
        createdAt?: string | null
        duration?: number | null
        tagsPlainText?: Array<string | null> | null
        uuid: string
      } | null
    } | null> | null
  } | null
}

export type VideoSnippetByUuidQueryVariables = Exact<{
  uuid: Scalars['String']['input']
}>

export type VideoSnippetByUuidQuery = {
  __typename?: 'Query'
  VideoGetSnippet?: {
    __typename?: 'VideoSnippet'
    description?: string | null
  } | null
}

export type VideoConsumerFeedQueryVariables = Exact<{
  upNext?: InputMaybe<Scalars['Boolean']['input']>
  latest?: InputMaybe<Scalars['Boolean']['input']>
}>

export type VideoConsumerFeedQuery = {
  __typename?: 'Query'
  VideoConsumerFeed?: {
    __typename?: 'VideoFeed'
    upNext?: Array<{
      __typename?: 'VideoVCMSSnippet'
      id: string
      headline?: string | null
      uuid?: string | null
      thumbnailUuid?: string | null
      description?: string | null
      updatedAt?: string | null
      publishedAt?: string | null
      source?: string | null
      videoId?: number | null
      urlAlias?: string | null
      guests?: Array<{
        __typename?: 'VideoGuest'
        id: number
        name: string
      } | null> | null
      categories?: Array<{
        __typename?: 'VideoVCMSCategory'
        id: string
        name?: string | null
        urlAlias?: string | null
      } | null> | null
    } | null> | null
    latest?: Array<{
      __typename?: 'VideoVCMSSnippet'
      id: string
      headline?: string | null
      uuid?: string | null
      thumbnailUuid?: string | null
      description?: string | null
      updatedAt?: string | null
      publishedAt?: string | null
      source?: string | null
      videoId?: number | null
      urlAlias?: string | null
      guests?: Array<{
        __typename?: 'VideoGuest'
        id: number
        name: string
      } | null> | null
      categories?: Array<{
        __typename?: 'VideoVCMSCategory'
        id: string
        name?: string | null
        urlAlias?: string | null
      } | null> | null
    } | null> | null
  } | null
}

export type VideoConsumerSnippetBySlugQueryVariables = Exact<{
  slug: Scalars['String']['input']
}>

export type VideoConsumerSnippetBySlugQuery = {
  __typename?: 'Query'
  VideoConsumerSnippetBySlug?: {
    __typename?: 'VideoVCMSSnippet'
    id: string
    headline?: string | null
    uuid?: string | null
    thumbnailUuid?: string | null
    description?: string | null
    updatedAt?: string | null
    publishedAt?: string | null
    source?: string | null
    videoId?: number | null
    urlAlias?: string | null
    guests?: Array<{
      __typename?: 'VideoGuest'
      id: number
      name: string
    } | null> | null
    categories?: Array<{
      __typename?: 'VideoVCMSCategory'
      id: string
      name?: string | null
      urlAlias?: string | null
    } | null> | null
  } | null
}

export type VideoConsumerVideoByIdQueryVariables = Exact<{
  id: Scalars['Int']['input']
}>

export type VideoConsumerVideoByIdQuery = {
  __typename?: 'Query'
  VideoConsumerVideoById?: {
    __typename?: 'VideoVCMSVideo'
    id: string
    duration?: number | null
    uuid?: string | null
  } | null
}

export type VideoConsumerCategoriesQueryVariables = Exact<{
  [key: string]: never
}>

export type VideoConsumerCategoriesQuery = {
  __typename?: 'Query'
  VideoConsumerCategories?: Array<{
    __typename?: 'VideoVCMSCategory'
    id: string
    name?: string | null
    urlAlias?: string | null
    position?: number | null
  } | null> | null
}

export type VideoConsumerCategoryByIdQueryVariables = Exact<{
  id: Scalars['Int']['input']
}>

export type VideoConsumerCategoryByIdQuery = {
  __typename?: 'Query'
  VideoConsumerCategoryById?: {
    __typename?: 'VideoVCMSCategory'
    id: string
    name?: string | null
    urlAlias?: string | null
    position?: number | null
    edges?: {
      __typename?: 'VideoVCMSCategoryEdges'
      snippets?: Array<{
        __typename?: 'VideoVCMSSnippet'
        id: string
        headline?: string | null
        uuid?: string | null
        thumbnailUuid?: string | null
        description?: string | null
        updatedAt?: string | null
        publishedAt?: string | null
        source?: string | null
        videoId?: number | null
        urlAlias?: string | null
        guests?: Array<{
          __typename?: 'VideoGuest'
          id: number
          name: string
        } | null> | null
        categories?: Array<{
          __typename?: 'VideoVCMSCategory'
          id: string
          name?: string | null
          urlAlias?: string | null
        } | null> | null
      } | null> | null
    } | null
  } | null
}
