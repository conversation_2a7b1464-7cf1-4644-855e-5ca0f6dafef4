import { GraphQLClient } from 'graphql-request'
import { z } from 'zod'
import { env } from '../../env/client.mjs'

/**
 * Client for fetching data from the GraphQL endpoint (API).
 */
const pricesClient = new GraphQLClient(env.NEXT_PUBLIC_GRAPH_GATEWAY_URL)

/**
 * Backup client for fetching data from the GraphQL endpoint (API).
 */
const backupPricesClient = new GraphQLClient(
  env.NEXT_PUBLIC_GRAPH_GATEWAY_BACKUP_URL,
)

/**
 * Client for fetching data from the GraphQL endpoint (DRUPAL).
 */
const contentClient = new GraphQLClient(env.NEXT_PUBLIC_GRAPH_NEWS_URL)

/**
 * Validate URL alias
 *
 * @param path
 */
function validateUrlAlias(path: string): boolean {
  const validate = z
    .string()
    .url({ message: 'Invalid URL' })
    .safeParse(`${env.NEXT_PUBLIC_GRAPH_GATEWAY_URL}${path}`)
  return validate.success
}

/**
 * Retry fetch function
 * This function is used handle retries and keep old data if the fetch fails
 *
 * @param fetchFunction
 * @param retries
 * @param interval
 */
async function retryFetch(fetchFunction, retries = 3, interval = 15000) {
  let lastError
  for (let i = 0; i < retries; i++) {
    try {
      return await fetchFunction()
    } catch (error) {
      console.error('Error fetching data: ', error)

      // We have data, return it
      if (error?.response?.data) {
        return error.response
      }

      lastError = error
      await new Promise((resolve) => setTimeout(resolve, interval))
    }
  }
  throw lastError
}

/**
 * Minify query
 * This function is used to minify the query string
 *
 * @param query
 */
function minifyQuery(query: string) {
  return query
    .replace(/#.*$/gm, '') // Remove single-line comments
    .replace(/\/\*[\s\S]*?\*\//g, '') // Remove multi-line comments
    .replace(/\s*({|}|\[|\]|:|,|\(|\))\s*/g, '$1') // Remove spaces around delimiters
    .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
    .trim() // Trim leading and trailing spaces
}

/**
 * Produce query ID
 * This function is used to produce a query ID
 *
 * @param query
 * @param variables
 */
function produceQueryId(query: string, variables: any) {
  const getQueryName = query.split(' ')[1]
  return JSON.stringify({ query: getQueryName, variables })
}

/**
 * Handle errors and return the error message
 *
 * @param error
 */
function handleErrors(error) {
  // Log the error so we can debug it
  console.error(error)

  if (error?.response?.data) {
    return error.response.data || {}
  }

  // No data, return empty object
  return {}
}

/**
 * Handle response
 * This function is used to handle the response
 *
 * @param res
 */
function handleResponse(res: any) {
  if (res?.timeout) {
    throw new Error('Query timeout', { cause: res })
  }

  if (res?.status !== 200) {
    throw new Error('Query not successful', { cause: res })
  }

  if (Object.keys(res?.data).length === 0) {
    throw new Error('No data returned', { cause: res })
  }

  if (res?.error?.response?.data) {
    return res.error.response?.data
  }

  if (res?.data) {
    return res?.data
  }

  return res || {}
}

/**
 * Graphs
 * This object is used to fetch data from the graphql endpoint
 */
export const graphs = {
  /**
   * Prices fetch
   * This function is used to fetch prices data
   *
   * @param {string} query
   * @param variables
   */
  pricesFetch: async (query: string, variables) => {
    try {
      const res = await retryFetch(() => {
        const minifiedQuery = minifyQuery(query)

        return pricesClient.rawRequest(minifiedQuery, variables, {
          'X-Query-Id': produceQueryId(minifiedQuery, variables),
        })
      })

      return handleResponse(res)
    } catch (error) {
      return handleErrors(error)
    }
  },

  /**
   * Search prices fetch
   * This function is used to fetch search data
   *
   * @param {string} query
   * @param variables
   */
  searchPricesFetch: async (query: string, variables) => {
    return await graphs.pricesFetch(query, variables)
  },

  /**
   * Videos prices fetch
   * This function is used to fetch videos data
   *
   * @param {string} query
   * @param variables
   */
  videosPricesFetch: async (query: string, variables) => {
    return await graphs.pricesFetch(query, variables)
  },

  /**
   * Backup prices fetch
   * This function is used to fetch prices data from the backup endpoint
   *
   * @param {string} query
   * @param variables
   */
  backupPricesFetch: async (query: string, variables) => {
    try {
      const res = await retryFetch(() => {
        const minifiedQuery = minifyQuery(query)

        return backupPricesClient.rawRequest(minifiedQuery, variables, {
          'X-Query-Id': produceQueryId(minifiedQuery, variables),
        })
      })

      return handleResponse(res)
    } catch (error) {
      return handleErrors(error)
    }
  },

  /**
   * Backup search prices fetch
   * This function is used to fetch search data from the backup endpoint
   *
   * @param {string} query
   * @param variables
   */
  backupSearchPricesFetch: async (query: string, variables) => {
    return await graphs.backupPricesFetch(query, variables)
  },

  /**
   * Backup videos prices fetch
   * This function is used to fetch videos data from the backup endpoint
   *
   * @param {string} query
   * @param variables
   */
  backupVideosPricesFetch: async (query: string, variables) => {
    return await graphs.backupPricesFetch(query, variables)
  },

  /**
   * Content fetch
   * This function is used to fetch content data
   *
   * @param query
   * @param variables
   */
  contentFetch: async (query, variables) => {
    try {
      // Validate the URL alias
      if (typeof variables !== 'undefined' && 'urlAlias' in variables) {
        const isPathValid = validateUrlAlias(variables.urlAlias)
        if (!isPathValid)
          throw new Error('Invalid URL', { cause: variables.urlAlias })
      }

      const res = await retryFetch(() =>
        contentClient.rawRequest(minifyQuery(query), variables),
      )

      return {
        ...handleResponse(res),
        cacheTagsHeader: res?.headers.get('Surrogate-Key'),
      }
    } catch (error) {
      return handleErrors(error)
    }
  },
} as const
