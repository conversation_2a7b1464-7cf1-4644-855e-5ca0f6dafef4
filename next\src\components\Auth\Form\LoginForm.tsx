import { onAuthStateChanged, type User } from 'firebase/auth'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/router'
import type React from 'react'
import { type FC, useEffect, useState } from 'react'
import ForgotPassword from '~/src/components/Auth/Form/States/ForgotPassword'
import LoginMethods from '~/src/components/Auth/Form/States/LoginMethods'
import RecoverEmail from '~/src/components/Auth/Form/States/RecoverEmail'
import Register from '~/src/components/Auth/Form/States/Register'
import ResetPassword from '~/src/components/Auth/Form/States/ResetPassword'
import UsernameSetup from '~/src/components/Auth/Form/States/UsernameSetup'
import { Alert, AlertType } from '~/src/components/Auth/Messages/Alert'
import AlreadyLoggedIn from '~/src/components/Auth/Messages/AlreadyLoggedIn'
import VerifyEmail from '~/src/components/Auth/Messages/VerifyEmail'
import VerifyEmailCode from '~/src/components/Auth/Messages/VerifyEmailCode'
import type { UserData } from '~/src/components/Auth/Types/UserData'
import { auth } from '~/src/services/firebase/config'
import { logout } from '~/src/services/firebase/service'

/**
 * Props for the LoginForm component.
 */
interface LoginFormProps {
  defaultState?: FormState
  handleAuthStatus?: boolean
  link?: string
  logo?: string
  modal?: boolean
  oobCode?: string
  onIsRegistering?: (isRegistering: boolean) => void
  onSuccess?: (shouldReload?: boolean) => void
  redirectToForum?: boolean
  showRegister?: boolean
  userInfo?: UserData
}

/**
 * Manages the form state.
 */
export enum FormState {
  LOGIN = 'LOGIN',
  REGISTER = 'REGISTER',
  FORGOT_PASSWORD = 'FORGOT_PASSWORD',
  VERIFY_EMAIL = 'VERIFY_EMAIL',
  VERIFY_EMAIL_CODE = 'VERIFY_EMAIL_CODE',
  RECOVER_EMAIL = 'RECOVER_EMAIL',
  RECOVER_FORUM_USER = 'RECOVER_FORUM_USER',
  RESET_PASSWORD = 'RESET_PASSWORD',
  SETUP_USERNAME = 'SETUP_USERNAME',
}

/**
 * Auth form component.
 *
 * @param {LoginFormProps} props - Component properties
 * @returns {React.ReactElement} - Auth form component
 */
const LoginForm: FC<LoginFormProps> = ({
  defaultState = FormState.LOGIN,
  handleAuthStatus = true,
  link = '/',
  logo = '/logo_kitco.png',
  modal = false,
  oobCode = '',
  onIsRegistering = () => {}, // Default to an empty function
  onSuccess = () => {}, // Default to an empty function
  redirectToForum = false,
  showRegister = true,
  userInfo,
}: LoginFormProps): React.ReactElement => {
  const router = useRouter()

  const [formState, setFormState] = useState<FormState>(defaultState)

  // State for alerts.
  const [alertMessage, setAlertMessage] = useState<string>('')
  const [alertType, setAlertType] = useState<AlertType>(AlertType.ERROR)

  // State for the user.
  const [user, setUser] = useState<User | null>(null)

  // State for the modal title.
  const [formTitle, setFormTitle] = useState<string>('Sign in to your account')
  const [customTitle, setCustomTitle] = useState<string>('')

  // State for the email
  const [userData, setUserData] = useState<UserData>()

  // State for the user authentication status.
  const [isLoggedIn, setIsLoggedIn] = useState(false)

  // State for the login process started
  const [loginProcessStarted, setLoginProcessStarted] = useState(false)
  const [userHasLoggedIn, setUserHasLoggedIn] = useState(false)
  const [loginMethod, setLoginMethod] = useState('')

  // State for the user's registration status
  const [isRegistering, setIsRegistering] = useState(false)

  /**
   * Check if the user is logged in and changes the state accordingly.
   */
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      // If the handleAuthStatus is set to false, do not check the user status
      if (!handleAuthStatus) return

      if (user) {
        setIsLoggedIn(true)
      } else {
        setIsLoggedIn(false)
      }
    })

    // Clean up the observer
    return () => unsubscribe()
  }, [handleAuthStatus])

  /**
   * Effect to check the user's registration status.
   */
  useEffect(() => {
    onIsRegistering(isRegistering)
  }, [isRegistering])

  /**
   * Effect to check the form state.
   */
  useEffect(() => {
    setFormState(defaultState)
  }, [defaultState])

  /**
   * Toggles the verify email form.
   *
   * @param user
   */
  const toggleVerifyEmail = (user: User) => {
    setUser(user)
    setFormState(FormState.VERIFY_EMAIL)
  }

  /**
   * Handles the logged in state.
   * Redirects the user to the forum or the main site.
   * If the modal is set to true, the redirect will happen in the same window.
   */
  const handleLoggedIn = () => {
    const url = redirectToForum
      ? process.env.NEXT_PUBLIC_DISCOURSE_URL
      : process.env.NEXT_PUBLIC_URL

    // If we are in a modal, we need to call the onSuccess function
    if (modal) {
      onSuccess(true)
    } else {
      router.push(url)
    }
  }

  /**
   * Effect to check the form state.
   * Sets the modal title
   */
  useEffect(() => {
    let title = 'Sign in to your account'

    if (isLoggedIn && !loginProcessStarted) {
      title = userHasLoggedIn
        ? 'You are logged in'
        : 'You are already logged in'
    }

    switch (formState) {
      case FormState.FORGOT_PASSWORD:
        title = 'Reset your password'
        break
      case FormState.RECOVER_EMAIL:
        title = 'Recover your email address'
        break
      case FormState.RECOVER_FORUM_USER:
        title = 'Recover your account'
        break
      case FormState.REGISTER:
        title = 'Register for an account'
        break
      case FormState.RESET_PASSWORD:
        title = 'Reset your password'
        break
      case FormState.SETUP_USERNAME:
        title = 'Please fill in your data'
        break
      case FormState.VERIFY_EMAIL:
        title = 'Verify your email address'
        break
      case FormState.VERIFY_EMAIL_CODE:
        title = 'Email address verification'
        break
    }

    // If a custom title is set, use that instead
    if (customTitle) {
      title = customTitle
    }

    setFormTitle(title)
  }, [
    customTitle,
    formState,
    isLoggedIn,
    loginProcessStarted,
    loginMethod,
    userHasLoggedIn,
  ])

  /**
   * Handles the alert message.
   * Sets the alert message and type.
   *
   * @param message
   * @param type
   */
  const handleAlert = (message: string, type: AlertType = AlertType.ERROR) => {
    setAlertMessage(message)
    setAlertType(type)
  }

  /**
   * Handles the forgot password form.
   *
   * @param _email
   */
  const handleForgotPassword = (_email?: string) => {
    // If the email is provided, set the email in the state
    if (_email) {
      setUserData({ email: _email })
    }

    // Set the form state to forgot password
    setFormState(FormState.FORGOT_PASSWORD)
  }

  /**
   * Handles the register form.
   *
   * @param _email
   */
  const handleRegister = (_email?: string) => {
    // If the email is provided, set the email in the state
    if (_email) {
      setUserData({ email: _email })
    }

    // Set the form state to forgot password
    setFormState(FormState.REGISTER)
  }

  /**
   * Handles the login process started.
   */
  const handleLoginProcessStarted = (method: string): void => {
    setLoginProcessStarted(true)
    setLoginMethod(method)
  }

  /**
   * Handles the login process ended.
   *
   * @param shouldReload - Should the page reload? For use with CoralTalk
   */
  const handleLoginProcessEnded = (shouldReload = false): void => {
    setLoginProcessStarted(false)
    setUserHasLoggedIn(true)
    onSuccess(shouldReload)
  }

  /**
   * Handles the custom title.
   *
   * @param {string} value - The custom title
   */
  const handleCustomTitle = (value: string) => {
    setCustomTitle(value)
  }

  /**
   * Handles the back to login form.
   * Clears the alert message and sets the form state to login.
   */
  const handleBackToLogin = () => {
    setAlertMessage('')
    setFormState(FormState.LOGIN)
  }

  /**
   * Effect to reset the custom title when the form state changes.
   */
  useEffect(() => {
    setCustomTitle('')
  }, [formState])

  return (
    <>
      <div className="justify-top flex min-h-full flex-col px-6 py-8 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
          <Link href={link}>
            <Image
              className="mx-auto h-10 w-auto"
              src={logo}
              alt="Logo"
              unoptimized={true}
              width={0}
              height={0}
              priority={true}
            />
          </Link>
          <h2 className="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">
            {formTitle}
          </h2>
        </div>

        {alertMessage && (
          <div className="mt-4 sm:mx-auto sm:w-full sm:max-w-sm">
            <Alert message={alertMessage} type={alertType} />
          </div>
        )}

        {formState === FormState.REGISTER && (
          <Register
            onSuccess={() => {
              handleBackToLogin()
              onSuccess(false)
            }}
            onCustomTitle={handleCustomTitle}
            onError={handleAlert}
            onIsRegistering={(value) => setIsRegistering(value)}
            backToLogin={handleBackToLogin}
            userData={userData}
            redirectToForum={redirectToForum}
          />
        )}

        {formState === FormState.RECOVER_FORUM_USER && (
          <Register
            backToLogin={handleBackToLogin}
            onCustomTitle={handleCustomTitle}
            onError={handleAlert}
            onIsRegistering={(value) => setIsRegistering(value)}
            onSuccess={() => {
              handleBackToLogin()
              onSuccess(false)
            }}
            redirectToForum={true}
            showBackButton={false}
            userData={userInfo}
            itsRecovering={true}
          />
        )}

        {formState === FormState.FORGOT_PASSWORD && (
          <ForgotPassword
            onResetLinkSent={() => {
              handleBackToLogin()
              onSuccess(false)
            }}
            onError={handleAlert}
            onBackToLogin={handleBackToLogin}
            userData={userData}
            redirectToForum={redirectToForum}
          />
        )}

        {formState === FormState.VERIFY_EMAIL && (
          <VerifyEmail
            onAlert={handleAlert}
            onBack={handleBackToLogin}
            redirectToForum={redirectToForum}
            user={user}
          />
        )}

        {formState === FormState.VERIFY_EMAIL_CODE && (
          <VerifyEmailCode
            code={oobCode}
            onBack={onSuccess}
            onAlert={handleAlert}
          />
        )}

        {formState === FormState.RECOVER_EMAIL && (
          <RecoverEmail
            code={oobCode}
            onError={handleAlert}
            onBack={handleBackToLogin}
          />
        )}

        {formState === FormState.RESET_PASSWORD && (
          <ResetPassword
            code={oobCode}
            onError={handleAlert}
            backToLogin={handleBackToLogin}
            requestPasswordReset={handleForgotPassword}
          />
        )}

        {formState === FormState.SETUP_USERNAME && (
          <UsernameSetup
            backToLogin={handleBackToLogin}
            onError={handleAlert}
            onSuccess={() => onSuccess(false)}
          />
        )}

        {formState === FormState.LOGIN &&
          (isLoggedIn && !loginProcessStarted ? (
            <AlreadyLoggedIn
              onClose={handleLoggedIn}
              onLogout={() => logout()}
            />
          ) : (
            <LoginMethods
              onForgotPassword={handleForgotPassword}
              onLoginError={handleAlert}
              onLoginProcessStarted={handleLoginProcessStarted}
              onNeedUsername={() => setFormState(FormState.SETUP_USERNAME)}
              onNeedVerification={toggleVerifyEmail}
              onRegister={handleRegister}
              onSuccess={handleLoginProcessEnded}
              showRegister={showRegister}
            />
          ))}
      </div>
    </>
  )
}

export default LoginForm
