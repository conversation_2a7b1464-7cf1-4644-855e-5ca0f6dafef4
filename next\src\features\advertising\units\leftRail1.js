import { demandConfig, sizeDefaults } from './demandConfig'

export const leftRail1 = {
  id: 'left-rail-1',
  path: '/21841313772,22554256/kitco/left_rail_one',
  sizes: sizeDefaults.skyscraper,
  sizeMappingName: 'leftRail',
  refreshTime: 30,
  targeting: {
    refresh: 'true',
    refresh_time: '30',
  },
  prebid: [
    {
      mediaTypes: {
        banner: {
          sizeConfig: [
            { minViewPort: [0, 0], sizes: [] },
            { minViewPort: [768, 0], sizes: [] },
            { minViewPort: [1270, 0], sizes: [[160, 600]] },
          ],
        },
      },
      bids: [
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '3091224',
          },
        },
        {
          bidder: 'openx',
          params: {
            unit: '*********',
            delDomain: demandConfig.openx.delDomain,
          },
        },
        {
          bidder: 'rubicon',
          params: {
            accountId: demandConfig.rubicon.accountId,
            siteId: demandConfig.rubicon.siteId,
            zoneId: '1792630',
          },
        },
        {
          bidder: 'sovrn',
          params: {
            tagid: '756860',
          },
        },
        {
          bidder: 'ix',
          params: {
            siteId: '560682',
          },
        },
        {
          bidder: 'sharethrough',
          params: {
            pkey: 'KlmfOBCyXCJ8Zps5QHRUVwDD',
          },
        },
        {
          bidder: 'medianet',
          params: {
            cid: demandConfig.medianet.cid,
            crid: '*********',
          },
        },
        {
          bidder: 'sonobi',
          params: {
            placement_id: 'c86e096fa91e147f3cf3',
          },
        },
        {
          bidder: 'onetag',
          params: {
            pubId: demandConfig.onetag.pubId,
          },
        },
        {
          bidder: 'oms',
          params: {
            publisherId: demandConfig.brightcom.publisherId,
          },
        },
        {
          bidder: 'eplanning',
          params: {
            ci: demandConfig.eplanning.ci,
          },
        },
        {
          bidder: 'nativo',
          params: {
            placementId: 1467943,
          },
        },
        {
          bidder: 'anyclip',
          params: {
            publisherId: '86',
            supplyTagId: 'd10c6ee9-cdba-4a30-9b4a-7fbdc402da3a',
          },
        },
      ],
    },
  ],
}
