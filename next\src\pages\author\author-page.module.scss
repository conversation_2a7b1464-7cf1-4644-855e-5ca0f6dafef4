@use './../../styles/vars' as *;

.gridTwoColumn {
  display: grid;
  grid-template-columns: 1fr 300px;
  column-gap: 4em;
  margin-top: 6em;
}

.pageTitle {
  font-size: 1.5em;
}

.sectionTitle {
  margin-top: 30px;
  padding-bottom: 0.5em;
  font-size: 1.25em;
  font-family: 'Mulish';
  font-weight: 700;
  color: #373737;
  text-transform: uppercase;
  border-bottom: 1px solid $dark-grey;
}

.aboutText {
  margin: 1em 0;
  color: #777777;
}

.advertBlock {
  margin: 2em 0;
  height: 200px;
  width: 100%;
  background-color: $light-grey;
}

.readMore {
  position: relative;
  color: #34495e;
  margin: -25px 0 0 0;
  text-decoration: none;
  cursor: pointer;
}

.loadMoreArticles {
  width: 100%;
  text-align: center;
  background: #f4f4f4;
  padding: 15px 0;
  margin-top: 25px;

  & p {
    text-transform: uppercase;
    text-decoration: none;
    font-size: 20px;
    color: #003366;
  }
}
