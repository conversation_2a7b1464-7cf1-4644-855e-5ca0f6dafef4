import clsx from 'clsx'
import type { FC } from 'react'

/**
 * Filter item component
 *
 * @param {boolean} border - The border state of the filter item
 * @param {string} className - The class name for the component
 * @param {string} name - The name of the filter item
 * @param {() => void} onSelect - The function to call when the filter item is selected
 * @param {boolean} selected - The selected state of the filter item
 * @param {boolean} disabled - The disabled state of the filter item
 * @param {boolean} isNew - The new state of the filter item
 */
interface FilterItemProps {
  border?: boolean // true = top border, false = bottom border
  className?: string
  name: string
  onSelect?: () => void
  selected: boolean
  isNew?: boolean
  disabled?: boolean
}

/**
 * Filter item component
 */
const FilterItem: FC<FilterItemProps> = ({
  className = '',
  border = false,
  name,
  onSelect,
  selected = false,
  disabled = false,
  isNew = false,
}: FilterItemProps) => {
  return (
    <div
      className={clsx(
        'flex items-center justify-center gap-2.5 self-stretch px-4 py-2',
        selected
          ? border
            ? 'border-t-2 border-sky-700'
            : 'border-b-2 border-sky-700'
          : '',
        disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',
        className,
      )}
    >
      <button
        type="button"
        className={clsx(
          "text-center font-['Mulish'] text-xs text-neutral-900 text-nowrap",
          selected ? 'font-bold leading-3' : 'font-normal leading-none',
          disabled && 'cursor-not-allowed',
        )}
        onClick={!disabled ? onSelect : undefined}
        disabled={disabled}
        aria-label={name}
      >
        {name}
        {isNew && (
          <span className="ml-2 inline-flex items-center rounded-md bg-ktc-blue px-1 py-0.5 text-xs font-medium text-white group-hover:bg-kitco-black">
            NEW
          </span>
        )}
      </button>
    </div>
  )
}

export default FilterItem
