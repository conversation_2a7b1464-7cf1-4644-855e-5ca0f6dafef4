import useSWR from 'swr'
import type CommodityData from '~/src/types/DataTable/CommodityData'
import { refetchInterval } from '~/src/utils/timestamps'

const API_BASE_URL = 'http://localhost:3000/api/v1/kgx/getCR'
const API_KEY =
  '61d6e1f73101fa0b13d6a5301e649fef78dea03b50b6b6c44a10a4600ceefe57'

interface CryptoCurrency {
  name: string
  symbol: string
}

interface CryptoData {
  Symbol: string
  Bid: number
  Change: number
  ChangePercentage: number
  ChangePercentTrade: number
  ChangePercentUSD: number
  ChangeTrade: number
  ChangeUSD: number
  High: number
  Low: number
  Timestamp: string
  [key: string]: string | number
}

interface CryptoApiResponse {
  Cryptocurrencies: {
    CR: CryptoData[]
  }
}

const CRYPTO_CURRENCIES: CryptoCurrency[] = [
  { name: 'Bitcoin', symbol: 'BTC' },
  { name: '<PERSON>ther<PERSON>', symbol: 'ETH' },
  { name: '<PERSON><PERSON>', symbol: '<PERSON><PERSON>' },
  { name: '<PERSON><PERSON>', symbol: 'ADA' },
  { name: 'Avalanche', symbol: 'AVAX' },
  { name: 'Chainlink', symbol: 'LINK' },
  { name: 'Tron', symbol: 'TRX' },
  { name: 'Ripple', symbol: 'XRP' },
  { name: 'Binance', symbol: 'BNB' },
  { name: 'Sui', symbol: 'SUI' },
]

const fetcher = async (): Promise<Record<string, CryptoData>> => {
  const symbols = CRYPTO_CURRENCIES.map((c) => c.symbol).join(',')
  const params = new URLSearchParams({
    apikey: API_KEY,
    symbol: symbols,
    type: 'json',
  })

  const response = await fetch(`${API_BASE_URL}?${params.toString()}`)
  if (!response.ok) {
    throw new Error('Failed to fetch crypto data')
  }

  const data: CryptoApiResponse = await response.json()

  return data.Cryptocurrencies.CR.reduce(
    (acc, item) => {
      acc[item.Symbol] = item
      return acc
    },
    {} as Record<string, CryptoData>,
  )
}

export const useGICryptoData = (): CommodityData[] => {
  const { data, error, isLoading } = useSWR<Record<string, CryptoData>>(
    'cryptoData',
    fetcher,
    {
      refreshInterval:
        typeof refetchInterval === 'function'
          ? refetchInterval() || 0
          : refetchInterval || 0,
      revalidateOnFocus: false,
      suspense: false, // Disable suspense mode
      revalidateOnMount: true,
      shouldRetryOnError: true,
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    },
  )

  if (isLoading) {
    return CRYPTO_CURRENCIES.map(({ name }) => createDefaultCommodityData(name))
  }

  if (error) {
    console.error('Error fetching crypto data:', error)
    return CRYPTO_CURRENCIES.map(({ name }) => createDefaultCommodityData(name))
  }

  if (!data) {
    console.error('No data received from API')
    return CRYPTO_CURRENCIES.map(({ name }) => createDefaultCommodityData(name))
  }

  return CRYPTO_CURRENCIES.map(({ symbol, name }) => {
    const cryptoData = data[symbol]
    return processCryptoData(cryptoData, name)
  })
}

const createDefaultCommodityData = (name: string): CommodityData => ({
  commodity: name,
  lastBid: {
    bid: '0',
    bidVal: 0,
    currency: 'USD',
    originalTime: new Date().toISOString(),
  },
  changeDueToUSD: {
    change: '0',
    changeVal: 0,
    percentage: '0',
    percentageVal: 0,
  },
  changeDueToTrade: {
    change: '0',
    changeVal: 0,
    percentage: '0',
    percentageVal: 0,
  },
  totalChange: {
    change: '0',
    changeVal: 0,
    percentage: '0',
    percentageVal: 0,
  },
})

const processCryptoData = (
  data: CryptoData | undefined,
  name: string,
): CommodityData => {
  if (!data) {
    return createDefaultCommodityData(name)
  }

  return {
    commodity: name,
    lastBid: {
      bid: data.Bid?.toString() || '0',
      bidVal: data.Bid || 0,
      currency: 'USD',
      originalTime: data.Timestamp || new Date().toISOString(),
    },
    changeDueToUSD: {
      change: data.ChangeUSD?.toString() || '0',
      changeVal: Number(data.ChangeUSD) || 0,
      percentage: data.ChangePercentUSD?.toString() || '0',
      percentageVal: Number(data.ChangePercentUSD) || 0,
    },
    changeDueToTrade: {
      change: data.ChangeTrade?.toString() || '0',
      changeVal: Number(data.ChangeTrade) || 0,
      percentage: data.ChangePercentTrade?.toString() || '0',
      percentageVal: Number(data.ChangePercentTrade) || 0,
    },
    totalChange: {
      change: data.Change?.toString() || '0',
      changeVal: data.Change || 0,
      percentage: data.ChangePercentage?.toString() || '0',
      percentageVal: data.ChangePercentage || 0,
    },
  }
}

export default useGICryptoData
