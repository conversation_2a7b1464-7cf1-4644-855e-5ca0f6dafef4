@use '../../styles/vars' as *;

.breakingNewsContainer {
  margin: 0 auto 4em auto;
}

.main {
  margin-top: 20px;
  display: grid;
  grid-gap: 20px;
  padding: 0 20px;

  @media screen and (min-width: 600px) {
    grid-template-columns: 1fr 1fr;
  }

  @media screen and (min-width: 1000px) {
    grid-template-columns: 200px 1fr 280px;
    grid-template-areas: none;
  }
}

.dividerDark {
  height: 2px;
  width: 100%;
  margin-top: 20px;
  background-color: #373737;
}

.block {
  margin-bottom: 20px;
}

.tradingView {
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 1em;
}
