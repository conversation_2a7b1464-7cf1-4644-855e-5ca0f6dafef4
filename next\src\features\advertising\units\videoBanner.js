import { demandConfig, sizeDefaults } from './demandConfig'

export const videoBanner = {
  id: 'vid-banner',
  path: '/21841313772,22554256/kitco/video_banner',
  sizes: sizeDefaults.vidBanner,
  sizeMappingName: 'vidBanner',
  refreshTime: 30,
  targeting: {
    refresh: 'true',
    refresh_time: '30',
  },
  prebid: [
    {
      mediaTypes: {
        banner: {
          sizeConfig: [
            { minViewPort: [0, 0], sizes: [[320, 100]] },
            { minViewPort: [768, 0], sizes: [[320, 100]] },
            { minViewPort: [1270, 0], sizes: [[320, 100]] },
          ],
        },
      },
      bids: [
        {
          bidder: 'onetag',
          params: {
            pubId: demandConfig.onetag.pubId,
          },
        },
        {
          bidder: 'oms',
          params: {
            publisherId: demandConfig.brightcom.publisherId,
          },
        },
        {
          bidder: 'eplanning',
          params: {
            ci: demandConfig.eplanning.ci,
          },
        },
        {
          bidder: 'rumble',
          params: {
            publisherId: 38329,
            siteId: 123,
            zoneId: 526, // 300x100
          },
        },
      ],
    },
  ],
}
