import clsx from 'clsx'
import { atom, useAtom } from 'jotai'
import Link from 'next/link'
import { useRouter } from 'next/router'
import * as process from 'process'
import { useEffect, useMemo, useRef, useState } from 'react'

const symbolsAtom = atom<string[]>([])

async function loadLibrary() {
  try {
    const lib = await import('@barchart/chart-lib')
    if (!lib) {
      throw new Error('Barchart library failed to load')
    }
    return lib
  } catch (error) {
    console.error('Failed to load Barchart library:', error)
    throw error
  }
}

async function barcharts() {
  try {
    // Check if we're on the client side
    if (typeof window === 'undefined') {
      console.warn('Barchart library can only be used on the client side')
      return null
    }

    const lib = await loadLibrary()

    if (!lib) {
      console.error('Barchart library failed to load')
      return null
    }

    // Check if API key is available
    if (!process.env.NEXT_PUBLIC_BARCHART_API_KEY) {
      console.error('Barchart API key is not configured')
      return null
    }

    const feedConfig = {
      defaultTemplate: '/thumbnail.def.json',
      throttleMillis: 5000,
      mode: 'InternalFeed',
      credentials: { username: 'kitcoChart', password: 'data' },
      apiKey: process.env.NEXT_PUBLIC_BARCHART_API_KEY,
    }

    if (!lib?.getFeed()) {
      try {
        // @ts-ignore
        lib.initFeed(lib.BarchartSiteDataFeed, feedConfig)
      } catch (initError) {
        console.error('Failed to initialize Barchart feed:', initError)
        return null
      }
    }

    return {
      init: async () => {
        try {
          const feed = lib?.getFeed()
          if (!feed) {
            console.error('No feed available for initialization')
            return false
          }

          const isReady = await feed.ready()
          if (!isReady) {
            try {
              // @ts-ignore
              lib.initFeed(lib.BarchartSiteDataFeed, feedConfig)
              return true
            } catch (reinitError) {
              console.error(
                'Failed to reinitialize Barchart feed:',
                reinitError,
              )
              return false
            }
          }
          return true
        } catch (error) {
          console.error('Error during Barchart initialization:', error)
          return false
        }
      },
      syncSymbols: async (symbols: string[]) => {
        try {
          const feed = lib?.getFeed()
          if (!feed) {
            console.error('No feed available for symbol sync')
            return
          }

          const isReady = await feed.ready()
          if (isReady) {
            for (let i = 1; i < symbols.length + 1; ++i) {
              const chartID = `chart${i}`
              const chartExistsInLib = feed.getChart(chartID)
              const chartExistsInDOM = document.getElementById(chartID)

              if (!chartExistsInLib && chartExistsInDOM) {
                try {
                  // Add additional validation before adding chart
                  const symbol = symbols[i - 1]
                  if (symbol && typeof symbol === 'string') {
                    // Additional safety check for the feed configuration
                    const chartConfig = {
                      symbol: symbol,
                    }

                    // Ensure the feed has a valid configuration before adding chart
                    if (feed.config) {
                      feed.addChart(chartID, chartConfig)
                    } else {
                      console.warn(
                        `Feed configuration is not available for chart ${chartID}`,
                      )
                    }
                  } else {
                    console.warn(`Invalid symbol at index ${i - 1}:`, symbol)
                  }
                } catch (chartError) {
                  console.error(`Error adding chart ${chartID}:`, chartError)
                  // Continue with other charts even if one fails
                }
              }
            }
          } else {
            console.warn('Feed is not ready for symbol sync')
          }
        } catch (error) {
          console.error('Could not sync symbols:', error)
        }
      },
      destroy: async () => {
        try {
          const feed = lib.getFeed()
          if (!feed) {
            console.log('No feed available to destroy')
            return
          }

          const isReady = await feed.ready()
          if (isReady) {
            // Safely shutdown without trying to remove charts individually
            // The library should handle chart cleanup during shutdown

            // Now shutdown the feed
            try {
              lib.shutdownFeed()
            } catch (shutdownError) {
              console.log('Error during feed shutdown:', shutdownError)
            }
          }
        } catch (error) {
          console.log('Error during chart destruction:', error)
        }
      },
    }
  } catch (error) {
    console.error('Error in barcharts function:', error)
    return null
  }
}

// Global flag to disable charts if they're causing errors
let chartsDisabled = false

export function Barcharts(props: {
  symbol: string
  title?: string
  href?: string
  className?: string
}) {
  const [read, write] = useAtom(symbolsAtom)
  const isMounted = useRef<HTMLDivElement>(null)
  const router = useRouter()

  // If charts are disabled, render a placeholder
  if (chartsDisabled) {
    return (
      <div className={props.className}>
        {props.href && props.title && (
          <Link
            href={props.href}
            className="block text-base font-semibold text-black hover:underline"
          >
            {props.title}
          </Link>
        )}
        <div className="flex items-center justify-center h-[280px] w-[280px] bg-gray-100 rounded-md">
          <p className="text-gray-500 text-sm">Chart temporarily unavailable</p>
        </div>
      </div>
    )
  }
  const [isClient, setIsClient] = useState(false)

  // Set client-side flag after component mounts
  useEffect(() => {
    setIsClient(true)
  }, [])

  const mountingAtom = useMemo(() => {
    const a = atom(
      (get) => get(symbolsAtom),
      async (get, set) => {
        // Only run on client side
        if (!isClient) return

        try {
          const bc = await barcharts()
          if (!bc) {
            console.error('Barchart initialization failed')
            return
          }
          const current = get(symbolsAtom)
          set(symbolsAtom, [...current, props.symbol])
          const updated = get(symbolsAtom)
          await bc.syncSymbols(updated)
        } catch (error) {
          console.error('Error in mounting atom:', error)
        }
      },
    )
    a.onMount = (set) => {
      // Only run on client side
      if (isClient) {
        // @ts-ignore
        set([props.symbol])
      }
    }
    return a
  }, [props.symbol, isClient])

  const [, mount] = useAtom(mountingAtom)

  useEffect(() => {
    // Only run on client side
    if (!isClient) return

    // Add global error handler for unhandled chart errors
    const handleGlobalError = (event: ErrorEvent) => {
      if (
        event.error &&
        event.error.message &&
        (event.error.message.includes('config') ||
          event.error.message.includes('removeAllCharts') ||
          event.error.message.includes('barchart'))
      ) {
        console.warn(
          'Barchart library error detected, disabling charts:',
          event.error.message,
        )
        chartsDisabled = true
        event.preventDefault() // Prevent the error from crashing the app
      }
    }

    window.addEventListener('error', handleGlobalError)

    try {
      mount()
    } catch (error) {
      console.error('Failed to mount charts:', error)
      chartsDisabled = true
    }

    const handleRouteChange = async () => {
      try {
        const bc = await barcharts()
        if (bc && bc.destroy) {
          await bc.destroy()
        }
        write([])
      } catch (error) {
        console.error('Error during route change cleanup:', error)
        write([])
      }
    }

    router.events.on('routeChangeStart', handleRouteChange)

    return () => {
      window.removeEventListener('error', handleGlobalError)
      router.events.off('routeChangeStart', handleRouteChange)
    }
  }, [mount, router.events, write, isClient])

  return (
    <div>
      {props.href && props.title && (
        <Link
          href={props.href}
          className={clsx(
            'block',
            'text-base font-semibold text-black',
            'hover:underline',
          )}
        >
          {props.title}
        </Link>
      )}
      <div
        className={clsx(
          props.className,
          !isClient || !isMounted.current
            ? 'animate-loading h-[280px] w-[280px] rounded-md !bg-black/10'
            : 'chart',
        )}
      >
        {isClient && (
          <div ref={isMounted} id={`chart${read.indexOf(props.symbol) + 1}`} />
        )}
        {/* Show loading text when not client-side or chart not loaded */}
        {(!isClient || !isMounted.current) && (
          <div className="flex items-center justify-center h-full text-gray-500">
            Loading chart...
          </div>
        )}
        {/* Fallback message if chart fails to load */}
        {process.env.NODE_ENV === 'development' && isClient && (
          <div className="text-xs text-gray-500 mt-2">
            Chart Symbol: {props.symbol}
          </div>
        )}
      </div>
    </div>
  )
}
