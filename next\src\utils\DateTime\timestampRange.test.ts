import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import timestampRange from './timestampRange'

// Extend dayjs to use timezone and UTC plugins
dayjs.extend(utc)
dayjs.extend(timezone)

describe('timestampRange', () => {
  const timezoneName = 'America/New_York'

  // Set up the environment variable for timezone
  beforeAll(() => {
    process.env.NEXT_PUBLIC_TIMEZONE = timezoneName
  })

  /**
   * Test case to verify that timestampRange generates correct intervals within the range.
   */
  it('should generate timestamps at the specified interval between two dates', () => {
    // Test input: start and end times
    const start = dayjs.tz('2023-10-16T10:15:00', timezoneName)
    const end = dayjs.tz('2023-10-16T12:45:00', timezoneName)
    const interval = 30 // 30-minute interval

    // Expected timestamps, aligned with the interval
    const expectedTimestamps = [
      start.clone().minute(0).second(0).unix(), // 10:00
      start.clone().minute(30).second(0).unix(), // 10:30
      start.clone().add(1, 'hour').minute(0).unix(), // 11:00
      start.clone().add(1, 'hour').minute(30).unix(), // 11:30
      start.clone().add(2, 'hours').minute(0).unix(), // 12:00
      start.clone().add(2, 'hours').minute(30).unix(), // 12:30
    ]

    // Call the function under test
    const result = timestampRange(start, end, interval)

    // Assertions
    expect(result).toEqual(expectedTimestamps)
  })

  /**
   * Test case to verify that the start time is correctly rounded down to the nearest interval.
   */
  it('should round down the start time to the nearest interval', () => {
    const start = dayjs.tz('2023-10-16T10:17:00', timezoneName) // Start time not aligned
    const end = dayjs.tz('2023-10-16T11:00:00', timezoneName)
    const interval = 15 // 15-minute interval

    // Expected timestamps, rounded down to the nearest 15-minute mark
    const expectedTimestamps = [
      start.clone().minute(15).second(0).unix(), // Rounded to 10:15
      start.clone().minute(15).second(0).add(15, 'minute').unix(), // 10:30
      start.clone().minute(15).second(0).add(30, 'minute').unix(), // 10:45
      start.clone().minute(15).second(0).add(45, 'minute').unix(), // 11:00
    ]

    // Call the function under test
    const result = timestampRange(start, end, interval)

    // Assertions
    expect(result).toEqual(expectedTimestamps)
  })

  /**
   * Test case to verify that the end time is correctly rounded up to the nearest interval.
   */
  it('should round up the end time to the nearest interval', () => {
    const start = dayjs.tz('2023-10-16T10:00:00', timezoneName)
    const end = dayjs.tz('2023-10-16T11:43:00', timezoneName) // End time not aligned
    const interval = 15 // 15-minute interval

    // Expected timestamps, rounded up to the nearest 15-minute mark
    const expectedTimestamps = [
      start.clone().minute(0).unix(), // 10:00
      start.clone().add(15, 'minute').unix(), // 10:15
      start.clone().add(30, 'minute').unix(), // 10:30
      start.clone().add(45, 'minute').unix(), // 10:45
      start.clone().add(1, 'hour').unix(), // 11:00
      start.clone().add(1, 'hour').minute(15).unix(), // 11:15
      start.clone().add(1, 'hour').minute(30).unix(), // Rounded to 11:30
    ]

    // Call the function under test
    const result = timestampRange(start, end, interval)

    // Assertions
    expect(result).toEqual(expectedTimestamps)
  })

  /**
   * Test case when the start and end times are the same.
   */
  it('should return a single timestamp when start and end times are the same', () => {
    const time = dayjs.tz('2023-10-16T12:00:00', timezoneName)
    const interval = 15

    const result = timestampRange(time, time, interval)

    // Assertions
    expect(result).toEqual([time.unix()])
  })

  /**
   * Test case when the start time is after the end time.
   * The result should be an empty array.
   */
  it('should return an empty array if start time is after end time', () => {
    const start = dayjs.tz('2023-10-16T12:00:00', timezoneName)
    const end = dayjs.tz('2023-10-16T10:00:00', timezoneName) // End time is before start time
    const interval = 30

    const result = timestampRange(start, end, interval)

    // Assertions
    expect(result).toEqual([])
  })
})
