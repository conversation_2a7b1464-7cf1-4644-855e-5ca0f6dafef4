import { useEffect, useRef, useState } from 'react'
import type CommodityData from '~/src/types/DataTable/CommodityData'

type KgxDataResult = {
  crypto: CommodityData[]
  preciousMetals: CommodityData[]
  baseMetals: CommodityData[]
  energy: CommodityData[]
}

// Using the proxy endpoint instead of direct API access
const KGX_API_ENDPOINT = '/api/kgx-proxy'

function parseKgxApiDataByType(rawArray: any[]): KgxDataResult {
  const crypto: CommodityData[] = []
  const preciousMetals: CommodityData[] = []
  const baseMetals: CommodityData[] = []
  const energy: CommodityData[] = []

  console.log('parseKgxApiDataByType rawArray: ', rawArray)

  rawArray.forEach((item) => {
    const nameMap = {
      PB: 'Lead',
      RH: 'Rhodium',
      UR: 'Uranium',
      Oil: 'Crude Oil',
    }

    const originalName = item.name ?? item.symbol ?? ''
    const commodityName = nameMap[originalName] || originalName

    const data: CommodityData = {
      commodity: commodityName,
      lastBid: {
        bid:
          item.price?.toString() ??
          item.bid ??
          item.kgx_value?.toString() ??
          '0',
        bidVal: item.price ?? item.bid ?? item.kgx_value ?? 0,
        currency: 'USD',
        originalTime: item.time ?? item.timestamp ?? new Date().toISOString(),
      },
      changeDueToUSD: {
        change: item.change_due_usd?.toString() ?? '0',
        changeVal: item.change_due_usd ?? 0,
        percentage: item.change_due_usd_percent?.toString() ?? '0',
        percentageVal: item.change_due_usd_percent ?? 0,
      },
      changeDueToTrade: {
        change: item.change_due_trade?.toString() ?? '0',
        changeVal: item.change_due_trade ?? 0,
        percentage: item.change_due_trade_percent?.toString() ?? '0',
        percentageVal: item.change_due_trade_percent ?? 0,
      },
      totalChange: {
        change: item.total_change?.toString() ?? '0',
        changeVal: item.total_change ?? 0,
        percentage: item.total_change_percent?.toString() ?? '0',
        percentageVal: item.total_change_percent ?? 0,
      },
    }

    // TODO: fix the categorization at the api level
    const baseMetalNames = [
      'Aluminum',
      'Copper',
      'Nickel',
      'Zinc',
      'Lead',
      'Uranium',
      'PB',
      'UR',
    ]
    const preciousMetalNames = [
      'Gold',
      'Silver',
      'Platinum',
      'Palladium',
      'Rhodium',
      'RH',
    ]
    const energyNames = ['Crude Oil']
    const cryptoNames = ['BNB']

    if (energyNames.includes(data.commodity)) {
      energy.push(data)
    } else if (baseMetalNames.includes(data.commodity)) {
      baseMetals.push(data)
    } else if (preciousMetalNames.includes(data.commodity)) {
      preciousMetals.push(data)
    } else if (cryptoNames.includes(data.commodity)) {
      crypto.push(data)
    } else if (item.asset_type === 'CRYPTO') {
      crypto.push(data)
    }
  })

  crypto.sort((a, b) => b.lastBid.bidVal - a.lastBid.bidVal)

  return { crypto, preciousMetals, baseMetals, energy }
}

const useKgxData = (): KgxDataResult => {
  const [kgxData, setKgxData] = useState<KgxDataResult>({
    crypto: [],
    preciousMetals: [],
    baseMetals: [],
    energy: [],
  })
  const eventSourceRef = useRef<EventSource | null>(null)

  useEffect(() => {
    // Only run on client-side
    if (typeof window === 'undefined') return

    const connectToSSE = () => {
      try {
        const es = new EventSource(KGX_API_ENDPOINT)
        eventSourceRef.current = es

        es.onopen = () => {
          console.log('SSE connection established')
        }

        es.onmessage = (event) => {
          try {
            const raw = JSON.parse(event.data)
            const records = Array.isArray(raw) ? raw : raw.data
            if (Array.isArray(records)) {
              setKgxData(parseKgxApiDataByType(records))
            }
          } catch (err) {
            console.error('Error parsing KGX data:', err)
          }
        }

        es.onerror = (error) => {
          console.error('SSE error:', error)
          // Close and attempt to reconnect
          if (eventSourceRef.current) {
            eventSourceRef.current.close()
            eventSourceRef.current = null
          }

          // Attempt to reconnect after a delay
          setTimeout(connectToSSE, 5000)
        }

        return () => {
          if (eventSourceRef.current) {
            eventSourceRef.current.close()
            eventSourceRef.current = null
          }
        }
      } catch (error) {
        console.error('Failed to initialize SSE connection:', error)
        // Retry connection on error
        setTimeout(connectToSSE, 5000)
      }
    }

    // Initial connection
    connectToSSE()

    // Cleanup function
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close()
        eventSourceRef.current = null
      }
    }
  }, [])

  return kgxData
}

export { useKgxData }
