$modal-main-color: #0a87d2;
$modal-black-color: #404040;
$modal-error-color: #d42626;

.exitModalOverlay {
  position: fixed;
  z-index: 99998;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 16px;
}

.exitModal {
  position: fixed;
  z-index: 99999;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 16px;

  * {
    font-family: 'Mulish', sans-serif !important;
    box-sizing: border-box;
  }

  a {
    text-decoration: underline;
    color: white;
  }
}

.container {
  width: 684px;
  height: 546px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-size: cover;
  background-repeat: no-repeat;
  padding: 26px 46px;
}

.closeButtonImage {
  position: absolute;
  width: 40px;
  top: 10px;
  right: 20px;
  font-size: 20px;
  cursor: pointer;
}

.title {
  margin: 80px 0 10px 0;
  color: var(--main-color);
  text-align: center;
  text-transform: uppercase;
  font-size: 56px;
  font-weight: 900;
  line-height: 1.2;
}

.subTitle {
  font-size: 20px;
  margin: 0 0 170px 0;
  text-align: center;
  color: #373737;
  font-weight: 400;
  line-height: 1.2;
}

.form {
  position: relative;
  margin-bottom: 35px;
}

.errorMessage {
  font-size: 20px;
  position: absolute;
  bottom: 155px;
  color: white;

  i {
    font-family: 'fontAwesome' !important;
  }

  div {
    padding: 3px 24px;
    background: rgba(139, 0, 0, 0.5);
  }
}

.inputEmail {
  float: left;
  width: 65%;
  height: 43px;
  border: none;
  margin-bottom: 3px;
  border-radius: 0;
  padding-left: 13px;
  color: $modal-black-color;
  font-weight: 400;
  font-size: 16px;
}

.inputEmail::placeholder {
  color: #bbb;
}

.formSubmitButton {
  width: 32%;
  height: 43px;
  background: #0a87d2;
  border: none;
  border-radius: 0;
  color: white;
  font-weight: 700;
  font-size: 16px;
  cursor: pointer;
}

.footer {
  line-height: 1.2;
  text-align: center;
  text-shadow: 1px 1px 3px $modal-black-color;
  font-size: 15px;
}

.formError {
  float: left;
  clear: both;
}

.formError {
  color: $modal-error-color;
  text-shadow: 1px 1px 3px #eee;
  visibility: hidden;
}

// SECONDARY FORM
.formSecondary {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  position: relative;
  color: $modal-black-color;
}

.successMessageSecondary {
  text-transform: uppercase;
  font-size: 50px;
  font-weight: 900;
  color: $modal-main-color;
  text-align: center;
  line-height: 1.2;
}

.titleSecondary {
  margin: 35px 0 25px 0;
  font-size: 35px;
  font-weight: 900;
  line-height: 1.2;
  text-align: center;
  color: $modal-black-color;
}

.formContainerSecondary {
  flex: 1 1 50%;
  margin-bottom: 25px;
}

.formLabel {
  display: block;
  position: relative;
  margin: 0;
  padding-left: 25px;
  font-size: 20px;
  font-weight: 700;
  line-height: 1.2;
  cursor: pointer;
}

.list {
  margin: 0 0 10px 20px;
  padding-left: 40px;
}

.listItem {
  margin: 5px 0;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.3;
  list-style-type: initial;
}

.formCheckbox {
  position: absolute;
  left: 0;
  top: 3px;
  cursor: pointer;
}

.submitSecondary {
  flex: 0 1 48%;
  padding: 10px 0;
  background: $modal-main-color;
  border-radius: 5px;
  color: white;
  font-weight: 700;
  font-size: 16px;
  cursor: pointer;
}

.declineButton {
  flex: 0 1 48%;
  padding: 10px 0;
  border-radius: 5px;
  border: 2px solid $modal-main-color;
  color: $modal-main-color;
  text-align: center;
  cursor: pointer;
}

.footerSecondary {
  margin-top: 20px;
  color: $modal-black-color;
  text-align: center;

  a {
    color: $modal-black-color;
  }
}

.divider {
  margin: 0 5px;
}

.error {
  position: absolute;
  bottom: -22px;
  left: 7px;
  color: #d42626;
}

// FAIL MODAL
.failureText {
  margin-top: 100px;
  font-size: 30px;
  line-height: 1.2;
  text-align: center;
  color: $modal-black-color;
}

// LOADER
.loader,
.loader:after {
  border-radius: 50%;
  width: 6em;
  height: 6em;
}

.loader {
  position: absolute;
  top: -80px;
  right: 50%;
  font-size: 10px;
  text-indent: -9999em;
  border-top: 1.1em solid rgba(255, 255, 255, 0.3);
  border-right: 1.1em solid rgba(255, 255, 255, 0.3);
  border-bottom: 1.1em solid rgba(255, 255, 255, 0.3);
  border-left: 1.1em solid $modal-main-color;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation: load8 1.1s infinite linear;
  animation: load8 1.1s infinite linear;
}

.loaderSecondary {
  top: 70px;
  right: 45%;
}

@-webkit-keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes load8 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
