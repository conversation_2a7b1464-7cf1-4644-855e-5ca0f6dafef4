import Link from 'next/link'
import React from 'react'

const LondonFixNotice: React.FC<{ style?: React.CSSProperties }> = ({
  style,
}) => {
  return (
    <div
      className="border border-gray-300 bg-gray-50 max-w-3xl font-sans"
      style={{
        ...style,
        border: '1px solid #ccc',
        backgroundColor: '#f9f9f9',
        padding: '20px',
        fontFamily: 'Mulish, sans-serif',
        lineHeight: '1.25rem',
      }}
    >
      <h2
        className="mt-0 text-xl font-semibold text-black mb-2"
        style={{
          fontSize: style?.fontSize || '1.25rem',
          fontFamily: 'Lato, sans-serif',
        }}
      >
        📢 Change to London Fix Price Availability
      </h2>

      <p
        className="text-base text-gray-800 mb-2"
        style={{
          fontSize: style?.fontSize || '0.875rem',
          fontFamily: 'Mulish, sans-serif',
        }}
      >
        As of June 2025, we are no longer able to provide London Fix prices or
        historical London Fix data. This change is due to updated redistribution
        policies implemented by the{' '}
        <strong>London Bullion Market Association (LBMA)</strong>.
      </p>

      <p
        className="text-base text-gray-800 mb-2"
        style={{
          fontSize: style?.fontSize || '0.875rem',
          fontFamily: 'Mulish, sans-serif',
        }}
      >
        We apologize for the inconvenience and appreciate your understanding.
        For historical price charts and intraday market data, please visit:
      </p>

      <ul
        className="list-disc pl-6 mb-2 text-base text-gray-800"
        style={{
          fontSize: style?.fontSize || '0.875rem',
          fontFamily: 'Mulish, sans-serif',
        }}
      >
        <li className="mb-1">
          <Link
            href="https://www.kitco.com/charts/gold"
            target="_blank"
            className="text-blue-600 underline"
            rel="noopener noreferrer"
          >
            Gold Charts
          </Link>
        </li>
        <li className="mb-1">
          <Link
            href="https://www.kitco.com/charts/silver"
            target="_blank"
            className="text-blue-600 underline"
            rel="noopener noreferrer"
          >
            Silver Charts
          </Link>
        </li>
        <li className="mb-1">
          <Link
            href="https://www.kitco.com/charts/platinum"
            target="_blank"
            className="text-blue-600 underline"
            rel="noopener noreferrer"
          >
            Platinum Charts
          </Link>
        </li>
        <li className="mb-1">
          <Link
            href="https://www.kitco.com/charts/palladium"
            target="_blank"
            className="text-blue-600 underline"
            rel="noopener noreferrer"
          >
            Palladium Charts
          </Link>
        </li>
      </ul>

      <hr
        className="my-2 border-gray-300"
        style={{
          fontSize: style?.fontSize || '0.875rem',
          fontFamily: 'Mulish, sans-serif',
        }}
      />

      <p
        className="text-sm text-gray-600"
        style={{
          fontSize: style?.fontSize || '0.875rem',
          fontFamily: 'Mulish, sans-serif',
        }}
      >
        We will soon be providing an alternate daily benchmark price for{' '}
        <strong>gold, silver, platinum, and palladium</strong>, which will be
        published daily on this page.
      </p>
    </div>
  )
}

export default LondonFixNotice
