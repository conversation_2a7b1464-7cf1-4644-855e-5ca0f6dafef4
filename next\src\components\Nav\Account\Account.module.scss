@use './../../../styles/vars' as *;

.navItem {
  height: 100%;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.circle {
  // border: solid 3px white;
  border: 0;
  height: 24px;
  width: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 6px;
  margin-left: 15px;
  background-color: transparent;
  @media screen and (max-width: 768px) {
    margin-left: 0;
  }
}

.dropContainer {
  position: absolute;
  width: 240px;
  padding: 1.5em;
  background-color: #373737;
  z-index: 900;

  @media screen and (max-width: 768px) {
    padding-top: 0;
    background-color: #373737;
  }

  @media screen and (max-width: 1240px) {
    visibility: hidden;
  }
}

ul.accountLinksList {
  margin-left: 0;

  li {
    word-wrap: none;
    color: white;

    & a {
      color: white;
    }
    &:first-of-type {
      border-bottom: solid 1px #575757;
      padding-bottom: 1em;
    }
    &:last-of-type {
      margin-top: 1em;
    }
  }
  @media screen and (max-width: 1240px) {
    visibility: hidden;
  }
}

.menuBg {
  background-color: #373733;
  padding: 1.5em;
  width: 210px;
  margin-top: 5px;
  right: 0;
  position: absolute;
  @media screen and (max-width: 1240px) {
    visibility: hidden;
  }
}

.ugh {
  position: relative;
}
