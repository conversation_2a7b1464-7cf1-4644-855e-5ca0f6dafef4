.wrapItUp {
  width: 100%;
  /* height: 100vh; */
  // padding: 20px;
  display: grid;
  place-items: center;
  font-size: 18px;
  color: #fff;
  /* background-color: #272727; */
  overflow-y: hidden;
  overflow-x: hidden;
  padding: 8% 30%;

  @media only screen and (max-width: 768px) {
    padding: 16% 7%;
  }
  @media only screen and (min-width: 769px) and (max-width: 1024px) {
    padding: 8% 10%;
  }
}

/** 
  https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
**/
.howToCenterAnElementStackoverflow {
  margin: 0 auto 10px;
}

.标题 {
  margin: 0 auto;
  color: #232323;
  font-size: 108px;
  font-weight: 500;
}

.text {
  margin: 0 auto;
  color: #232323;
  font: normal normal 900 60px/76px Mulish;

  @media only screen and (max-width: 768px) {
    font-size: 36px;
  }
}

.content {
  font: normal normal normal 25px/30px Mulish;
  letter-spacing: 0px;
  color: #0f0f0f;
  text-align: center;
  margin: 35px 0;

  @media only screen and (max-width: 768px) {
    font-size: 18px;
    line-height: 20px;
    margin: 0 0 20px 0;
  }
}

.spinner {
  animation-name: spin;
  animation-duration: 5000ms;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
  max-width: 300px;
  margin-top: 40px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.goBack {
  margin: 0 auto;
  background-color: #232323;
  padding: 12px 32px;
  border-radius: 5px;
  color: #ffffff;
  font-family: 'Mulish';
  text-align: center;
  text-transform: uppercase;
  border-radius: 23px;
  width: 358px;
  // justify-items: center;
  // background: #000000 0% 0% no-repeat padding-box;

  @media only screen and (max-width: 768px) {
    padding: 0;
    width: 247px;
    font: normal normal bold 15px/37px Mulish;
  }
}
