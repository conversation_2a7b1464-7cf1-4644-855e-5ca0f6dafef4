.wrapItUp {
  width: 100%;
  height: 100vh;
  padding: 20px;
  display: grid;
  place-items: center;
  font-size: 18px;
  color: #fff;
  background-color: #272727;
  overflow-y: hidden;
  overflow-x: hidden;
}

/** 
  https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
	https://stackoverflow.com/questions/114543/how-to-horizontally-center-an-element
**/
.howToCenterAnElementStackoverflow {
  margin: 0 auto 10px;
}

.标题 {
  color: #f0b310;
  font-size: 32px;
}

.spinner {
  animation-name: spin;
  animation-duration: 5000ms;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
  max-width: 300px;
  margin-top: 40px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
