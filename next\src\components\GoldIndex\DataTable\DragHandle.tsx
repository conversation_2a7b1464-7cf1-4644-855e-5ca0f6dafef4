import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import clsx from 'clsx'
import { ReactNode, useEffect, useState } from 'react'
// import { FaGripVertical } from 'react-icons/fa'

interface DraggableRowProps
  extends Omit<
    React.HTMLAttributes<HTMLTableRowElement>,
    'onDragStart' | 'onDragOver' | 'onDrop'
  > {
  id: string
  children: ReactNode
  className?: string
  style?: React.CSSProperties
  onDragStart?: (e: React.DragEvent<HTMLTableRowElement>) => void
  onDragOver?: (e: React.DragEvent<HTMLTableRowElement>) => void
  onDrop?: (e: React.DragEvent<HTMLTableRowElement>) => void
}

export const DraggableRow = ({
  id,
  children,
  className,
  style: propStyle,
  ...rest
}: DraggableRowProps) => {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id,
    animateLayoutChanges: () => isMounted,
  })

  const style: React.CSSProperties = {
    transform: isMounted ? CSS.Translate.toString(transform) : undefined,
    transition: isMounted ? transition || undefined : undefined,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1 : 'auto',
    cursor: 'move',
    ...propStyle,
  }

  return (
    <tr
      ref={setNodeRef}
      style={style}
      className={clsx(
        className,
        'hover:bg-gray-50 border-b border-slate-200',
        isDragging && 'opacity-50',
      )}
      {...attributes}
      {...listeners}
      {...rest}
    >
      {children}
      {/* Add an empty cell to match the header's extra column */}
      <td className="w-0 px-0 bg-transparent opacity-0 invisible"></td>
    </tr>
  )
}
