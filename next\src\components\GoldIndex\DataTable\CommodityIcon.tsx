import { faCow, faSeedling } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import clsx from 'clsx'
import type { FC } from 'react'

type CommodityIconProps = {
  name: string
  className?: string
}

const CommodityIcon: FC<CommodityIconProps> = ({ name, className = '' }) => {
  const category = getCategory(name)

  if (!category) return null

  return (
    <div className={clsx('flex flex-col items-center', className)}>
      <div className="flex items-center justify-center w-5 h-5">
        {getIconByCategory(category)}
      </div>
      {
        category === 'crypto'
        // &&
        // (<span className="font-mulish text-[11px] font-medium italic leading-none text-gray-600">
        //   NEW
        // </span>)
      }
    </div>
  )
}

function getCategory(name: string): string | null {
  if (!name) return null
  const lowerCaseName = name.toString().toLowerCase().trim()

  if (
    lowerCaseName === 'gold' ||
    lowerCaseName === 'silver' ||
    lowerCaseName === 'platinum' ||
    lowerCaseName === 'palladium' ||
    lowerCaseName === 'rhodium' ||
    lowerCaseName === 'rh'
  ) {
    return 'precious'
  }

  if (
    lowerCaseName === 'copper' ||
    lowerCaseName === 'aluminum' ||
    lowerCaseName === 'lead' ||
    lowerCaseName === 'nickel' ||
    lowerCaseName === 'tin' ||
    lowerCaseName === 'zinc' ||
    lowerCaseName === 'pb' ||
    lowerCaseName === 'uranium' ||
    lowerCaseName === 'ur'
  ) {
    return 'base'
  }

  if (lowerCaseName === 'oil' || lowerCaseName === 'crude oil') {
    return 'energy'
  }

  if (
    lowerCaseName === 'corn' ||
    lowerCaseName === 'cotton' ||
    lowerCaseName === 'soybeans' ||
    lowerCaseName === 'wheat'
  ) {
    return 'agriculture'
  }

  if (
    lowerCaseName === 'cattle' ||
    lowerCaseName === 'feeder cattle' ||
    lowerCaseName === 'lean hogs'
  ) {
    return 'livestock'
  }

  const cryptoCurrencies = [
    'bitcoin',
    'ethereum',
    'litecoin',
    'monero',
    'ripple',
    'dash',
    'zcash',
    'peercoin',
    'namecoin',
    'cardano',
    'polkadot',
    'solana',
    'dogecoin',
    'avalanche',
    'polygon',
    'chainlink',
    'stellar',
    'tron',
    'cosmos',
    'binance',
    'bnb',
    'sui',
    'xrp',
  ]

  if (cryptoCurrencies.includes(lowerCaseName)) {
    return 'crypto'
  }

  return null
}

function getIconByCategory(category: string) {
  const iconClass = 'w-4 h-4 text-current'

  switch (category) {
    case 'precious':
      return (
        <img
          src="/icons/KGX-Ingots.svg"
          alt="Ingots icon"
          width={18}
          height={18}
        />
      )
    case 'base':
      return (
        <img
          src="/icons/KGX-Anvil.svg"
          alt="Anvil icon"
          width={20}
          height={20}
        />
      )
    case 'energy':
      return (
        <img src="/icons/KGX-Oil.svg" alt="Oil icon" width={16} height={16} />
      )
    case 'agriculture':
      return <FontAwesomeIcon icon={faSeedling} className={iconClass} />
    case 'livestock':
      return <FontAwesomeIcon icon={faCow} className={iconClass} />
    case 'crypto':
      return (
        <img
          src="/icons/KGX-Crypto.svg"
          alt="Crypto icon"
          width={13}
          height={13}
        />
      )
    default:
      return null
  }
}

export default CommodityIcon
