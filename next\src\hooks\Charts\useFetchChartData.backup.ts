import type dayjs from 'dayjs'
import { metalsBackup } from '~/src/lib/metals-factory.backup.lib'
import { kitcoBackupQuery } from '~/src/services/database/kitcoQuery'

/**
 * Fetches the necessary data for the chart using backup endpoint.
 *
 * @param {Object} args - The arguments for fetching the data.
 * @param {string} args.symbol - The metal symbol
 * @param {string} args.currency - The currency code
 * @param {dayjs.Dayjs} now - The current time
 *
 * @returns {Object} - Returns the fetched data for both nivoChartData and metalQuote.
 */
function useFetchChartDataBackup(
  args: { symbol: string; currency: string },
  now: dayjs.Dayjs,
) {
  // Create a query for historical data using backup endpoint
  const nivoChartQuery = metalsBackup.nivoChartData({
    variables: {
      ...args,
      timestamp: now.unix(),
    },
  })

  // Fetch historical data from backup server
  return kitcoBackupQuery(nivoChartQuery)
}

export default useFetchChartDataBackup
