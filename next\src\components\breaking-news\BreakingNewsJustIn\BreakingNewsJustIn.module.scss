.link {
  width: 100%;
  height: 60px;
  margin: 0 auto 1em auto;
  border: solid 1px #fc8181;
  border-color: rgba(252, 129, 129, 1);
  background-color: black;
  gap: 1rem;
  display: flex;

  @media (min-width: 640px) {
    height: 80px;
  }

  @media (min-width: 767px) {
    gap: 2rem;
    height: 105px;
  }
}

.svgContainer {
  position: relative;
  display: block;
  width: 33%;
  height: auto;
  min-height: 100%;
  margin-right: 20px;
}

.st0 {
  fill: #da2816;
}
.st1 {
  fill-rule: evenodd;
  clip-rule: evenodd;
  fill: #ffffff;
}
.st2 {
  fill: #ffffff;
}
.st3 {
  fill: none;
  stroke: #da2816;
  stroke-width: 10;
}
