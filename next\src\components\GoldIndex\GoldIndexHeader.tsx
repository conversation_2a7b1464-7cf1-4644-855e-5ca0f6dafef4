// next/src/components/GoldIndex/GoldIndexHeader.tsx

import Image from 'next/image'
import React from 'react'
import DateTime from '~/src/components/DateTime/DateTime'

const GoldIndexHeader: React.FC = () => (
  <>
    {/* flex container: header on left, banner on right */}
    <div className="flex flex-col md:flex-row items-start justify-between w-full gap-4">
      {/* ←── left: your existing header content (minus its own underline) */}
      <div className="inline-flex w-full md:w-auto md:flex-1 md:max-w-[calc(100%-370px)] flex-col items-start justify-start gap-2">
        <h1 className="mt-1.5 font-['Lato'] text-3xl font-bold leading-none text-neutral-900">
          Kitco Global Index (KGX)
          <span className="sr-only">
            : Uncover the Real Value Behind Commodity & Crypto Prices
          </span>
        </h1>
        <h2 className="font-weight-normal font-['Lato'] text-xl leading-tight text-zinc-600">
          Separating Market Values from USD Influence
        </h2>
        <div className="flex gap-1 font-['Mulish'] text-xs font-normal leading-none text-zinc-600">
          <DateTime timeZone={process.env.NEXT_PUBLIC_TIMEZONE || 'America/New_York'} /> NY Time
        </div>
      </div>

      {/* → right: the 350×110 banner */}
      <div
        className="hidden md:flex items-center justify-center relative flex-shrink-0"
        style={{
          width: '350px',
          height: '110px',
          backgroundColor: '#F9FBFD',
          border: '0.7px solid #0A87D2',
          borderRadius: '8px',
          boxSizing: 'border-box',
          padding: '0 12px',
        }}
      >
        {/* Bulb logo in top-left corner */}
        <Image
          src="/icons/Bulb_ico.png"
          alt="Bulb Icon"
          width={20}
          height={20}
          className="absolute top-2 left-2"
        />
        <p
          style={{
            margin: 0,
            fontFamily: 'Mulish, sans-serif',
            fontSize: '13.33px',
            lineHeight: '15px',
            color: '#595959',
            textAlign: 'center',
          }}
        >
          <strong style={{ fontWeight: 700 }}>Now Featuring Top Cryptos</strong>
          <span style={{ fontWeight: 500 }}>
            {
              ' - Track Bitcoin, Ethereum, and more alongside precious and base metals, separating market values from US Dollar influence.'
            }
          </span>
        </p>
      </div>
    </div>

    {/* ─── underline spanning both header & banner ─── */}
    <div className="mt-5 w-full border-b border-slate-200" />
  </>
)

export default GoldIndexHeader