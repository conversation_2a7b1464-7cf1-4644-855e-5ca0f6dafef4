import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import { useRouter } from 'next/router'
import GoldIndexDataTable from '../GoldIndexDataTable'
import type CommodityData from '~/src/types/DataTable/CommodityData'

// Mock next/router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}))

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

const mockRouter = {
  query: {},
  isReady: true,
  push: jest.fn(),
  pathname: '/markets/kitco-gold-index',
}

const mockData: CommodityData[] = [
  {
    commodity: 'Gold',
    lastBid: {
      bid: '2000.50',
      bidVal: 2000.50,
      currency: 'USD',
      originalTime: '2023-01-01T00:00:00Z',
    },
    changeDueToUSD: {
      change: '10.25',
      changeVal: 10.25,
      percentage: '0.51',
      percentageVal: 0.51,
    },
    changeDueToTrade: {
      change: '10.25',
      changeVal: 10.25,
      percentage: '0.51',
      percentageVal: 0.51,
    },
    totalChange: {
      change: '10.25',
      changeVal: 10.25,
      percentage: '0.51',
      percentageVal: 0.51,
    },
  },
  {
    commodity: 'Silver',
    lastBid: {
      bid: '25.50',
      bidVal: 25.50,
      currency: 'USD',
      originalTime: '2023-01-01T00:00:00Z',
    },
    changeDueToUSD: {
      change: '-0.25',
      changeVal: -0.25,
      percentage: '-0.97',
      percentageVal: -0.97,
    },
    changeDueToTrade: {
      change: '-0.25',
      changeVal: -0.25,
      percentage: '-0.97',
      percentageVal: -0.97,
    },
    totalChange: {
      change: '-0.25',
      changeVal: -0.25,
      percentage: '-0.97',
      percentageVal: -0.97,
    },
  },
]

describe('GoldIndexDataTable', () => {
  beforeEach(() => {
    ; (useRouter as jest.Mock).mockReturnValue(mockRouter)
    localStorageMock.getItem.mockReturnValue(null)
    localStorageMock.setItem.mockClear()
  })

  it('renders the data table with commodity data', async () => {
    const { container } = render(<GoldIndexDataTable data={mockData} isLoading={false} />)

    // Check if the table structure is rendered
    expect(container.querySelector('.w-full')).toBeInTheDocument()

    // Wait for the component to fully render and check for commodity data
    // The component should render the commodity names in the table
    await screen.findByText('Gold')
    await screen.findByText('Silver')

    // Check that the table headers are present
    expect(screen.getByText('Commodities')).toBeInTheDocument()

    // Verify that the drag-and-drop functionality is initialized
    expect(container.querySelector('[id^="DndDescribedBy"]')).toBeInTheDocument()
  })

  it('initializes with provided data when no saved order exists', () => {
    localStorageMock.getItem.mockReturnValue(null)

    render(<GoldIndexDataTable data={mockData} isLoading={false} />)

    expect(localStorageMock.getItem).toHaveBeenCalledWith('goldIndexOrder')
  })

  it('loads saved order from localStorage when available', () => {
    const savedOrder = ['Silver', 'Gold']
    localStorageMock.getItem.mockReturnValue(JSON.stringify(savedOrder))

    render(<GoldIndexDataTable data={mockData} isLoading={false} />)

    expect(localStorageMock.getItem).toHaveBeenCalledWith('goldIndexOrder')
  })

  it('handles loading state correctly', () => {
    render(<GoldIndexDataTable data={mockData} isLoading={true} />)

    // The loading state should be passed to the DataTable component
    // This would require more detailed testing of the DataTable component
  })

  it('uses the correct storage key for persistence', () => {
    render(<GoldIndexDataTable data={mockData} isLoading={false} />)

    // Verify that the component attempts to load from the correct storage key
    expect(localStorageMock.getItem).toHaveBeenCalledWith('goldIndexOrder')
  })

  it('integrates with SortableProvider correctly', () => {
    const { container } = render(<GoldIndexDataTable data={mockData} isLoading={false} />)

    // Verify that the SortableProvider is wrapping the component
    // This is indicated by the presence of DnD context elements
    expect(container.querySelector('[id^="DndDescribedBy"]')).toBeInTheDocument()
    expect(container.querySelector('[id^="DndLiveRegion"]')).toBeInTheDocument()
  })
})
