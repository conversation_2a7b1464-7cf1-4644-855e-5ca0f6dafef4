import { dehydrate } from '@tanstack/react-query'
import type { GetServerSideProps, NextPage } from 'next'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { Suspense, useEffect } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import BuySellButton from '~/src/components/BuySellButton/BuySellButton'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import AboutKitcoGoldIndex from '~/src/components/GoldIndex/Content/AboutKitcoGoldIndex'
import USDollarStrength from '~/src/components/GoldIndex/Content/USDollarStrength'
import GoldIndexDataTable from '~/src/components/GoldIndex/DataTable/GoldIndexDataTable'
import GoldIndexBarchartImage from '~/src/components/GoldIndex/GoldIndexBarchartImage'
import GoldIndexHeader from '~/src/components/GoldIndex/GoldIndexHeader'
import GoldIndexSubHeader from '~/src/components/GoldIndex/GoldIndexSubHeader'
import LatestNewsSidebar from '~/src/components/LatestNewsSidebar/LatestNewsSidebar'
import Layout from '~/src/components/Layout/Layout'
import { CommodityProvider } from '~/src/contexts/CommodityContext'
import {
  getEnergyDataQuery,
  processEnergyData,
} from '~/src/hooks/GlobalIndex/useGIEnergyData'
import {
  getMetalsDataQuery,
  processMetalsData,
} from '~/src/hooks/GlobalIndex/useGIMetalsData'
import {
  createTableData,
  useGlobalIndexData,
} from '~/src/hooks/GlobalIndex/useGlobalIndexData'
import { kitcoQueryClient } from '~/src/services/database/kitcoQuery'

const KitcoGoldIndexContent = () => {
  const router = useRouter()
  const data = useGlobalIndexData()
  const isLoading = !data

  // Handle direct navigation to specific commodities via URL hash
  useEffect(() => {
    if (data?.length && router.isReady) {
      const { commodity } = router.query
      if (commodity && typeof commodity === 'string') {
        const selected = data.find(
          (c) => c.commodity.toLowerCase() === commodity.toLowerCase(),
        )
        if (selected) {
          // The CommodityProvider will handle this selection via context
        }
      }
    }
  }, [data, router.isReady, router.query, router])

  return (
    <CommodityProvider initialCommodities={data || []}>
      <Layout title="Kitco Global Index (KGX) | Track Gold, Silver, Oil, Crypto & More vs. USD">
        <ErrBoundary>
          <Suspense fallback={<div>Loading...</div>}>
            <Head>
              <meta
                name="description"
                content={
                  'Track real-time commodity prices with the Kitco Global Index (KGX). Analyze how USD strength impacts Gold, Silver, Oil, Copper, and top cryptocurrencies like Bitcoin, Ethereum, and Solana.'
                }
              />
              <meta
                name="keywords"
                content="Kitco Global Index,KGX,Gold,Silver,Oil,Copper,Bitcoin,Ethereum,Solana,Cryptocurrencies,USD,US Dollar,Commodity prices,Market index,Price tracking"
              />
            </Head>
            <div className="mx-auto grid max-w-full grid-cols-1 gap-8 md:p-2 lg:grid-cols-4">
              <main className="flex flex-col items-center space-y-10 lg:col-span-3">
                <div className="w-full max-w-full p-4 sm:max-w-[750px] md:w-[750px] md:p-0  lg:w-full lg:max-w-full">
                  <ErrBoundary errorTitle="Gold Index Header Error">
                    <GoldIndexHeader />
                  </ErrBoundary>
                  <ErrBoundary errorTitle="Gold Index SubHeader Error">
                    <GoldIndexSubHeader data={data} />
                  </ErrBoundary>
                </div>

                <div className="mt-10 w-full max-w-full p-0 md:w-[750px] md:max-w-[750px] md:rounded-xl md:p-4 md:shadow-2xl lg:w-full lg:max-w-full">
                  <ErrBoundary errorTitle="Gold Index DataTable Error">
                    <GoldIndexDataTable data={data} isLoading={isLoading} />
                  </ErrBoundary>
                </div>

                <AdvertisingSlot
                  id={'banner-1'}
                  className="mx-auto my-[15px] h-[280px] w-[336px] tablet:h-[90px] tablet:w-[728px] no-print"
                />

                <div>
                  <GoldIndexBarchartImage />
                </div>

                <div className="mt-10 inline-flex max-w-[750px] flex-col items-start justify-start gap-4 p-4 text-justify md:p-0">
                  <USDollarStrength />
                </div>

                <div
                  id="information"
                  className="mt-10 inline-flex max-w-[750px] flex-col items-start justify-start gap-4 p-4 text-justify md:p-0"
                >
                  <AboutKitcoGoldIndex />
                </div>
              </main>
              <aside className="flex flex-col items-center justify-start gap-6">
                <AdvertisingSlot
                  id={'right-rail-1'}
                  className="mx-auto hidden h-[250px] w-[300px] desktop:flex no-print"
                />
                <BuySellButton
                  width={240}
                  containerClassName="pt-4 pb-4 flex items-center justify-center xl:pt-0"
                  buttonClassName="text-[14px]"
                />
                <LatestNewsSidebar
                  limit={5}
                  className="flex flex-col space-y-4 p-4 sm:max-w-[750px] md:p-0"
                />
                <AdvertisingSlot
                  id={'right-rail-2'}
                  className="mx-auto hidden h-[600px] w-[300px] desktop:flex sticky top-[100px] no-print"
                />
              </aside>
            </div>

            <div className="mx-auto mt-10 flex max-w-full flex-col gap-8 sm:max-w-[750px] lg:max-w-full">
              <div className="w-full border-b border-slate-200" />
            </div>
          </Suspense>
        </ErrBoundary>
      </Layout>
    </CommodityProvider>
  )
}

export const getServerSideProps: GetServerSideProps = async () => {
  const queryClient = kitcoQueryClient()

  // Prefetch the queries
  await queryClient.prefetchQuery(getMetalsDataQuery(false))
  await queryClient.prefetchQuery(getEnergyDataQuery())

  // Prefetch the SSR data and process it
  const metalsRawData = await queryClient.fetchQuery(getMetalsDataQuery(false))
  const processedMetalsData = processMetalsData(metalsRawData)

  const energyRawData = await queryClient.fetchQuery(getEnergyDataQuery())
  const processedEnergyData = processEnergyData(energyRawData)

  // For server-side rendering, we'll initialize an empty array for crypto data
  // The client will fetch the latest crypto data on the client side
  const processedCryptoData: any[] = []

  const tableData = createTableData(
    processedMetalsData,
    processedEnergyData,
    processedCryptoData,
  )

  return {
    props: {
      dehydratedState: JSON.parse(JSON.stringify(dehydrate(queryClient))),
      ssrData: tableData,
    },
  }
}

const KitcoGoldIndex: NextPage = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <KitcoGoldIndexContent />
    </Suspense>
  )
}

export default KitcoGoldIndex
