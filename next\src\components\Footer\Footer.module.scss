@use './../../styles/vars' as *;

.defaultBG {
  background-color: #232323;
}

.videoPageBG {
  background-color: #0f181d;
}

.footer {
  width: 100%;
  margin-top: 120px;
  padding-top: 3em;
  padding-bottom: 3em;
  color: white;
  border-top: solid 2px #e5b53a;
  @media only screen and (max-width: 768px) {
    margin-top: 80px;
  }
  @media only screen and (max-width: 1024px) {
    margin-top: 80px;
  }
}

.logoSocial {
  @media only screen and (max-width: 767px) {
    display: block;
  }
  @media only screen and (min-width: 768px) {
    display: flex;
  }
  @media only screen and (min-width: 1024px) {
    display: none;
  }
}

.gridContent {
  @include contentWrapper;
  display: flex;
  padding: 0 16px;

  // @media only screen and (min-width: 768px) {
  //   // grid-template-columns: 1fr 1fr;
  // }

  // @media only screen and (min-width: 1024px) {
  //   grid-template-columns: 1fr 1fr 1fr 1fr;
  //   padding: 0;
  // }
}

.displayColumn {
  display: flex;
  flex-direction: column;
  flex-grow: 0;

  a {
    color: white;
    line-height: 1.8em;
    text-decoration: none;
  }

  @media only screen and (max-width: 767px) {
    flex: 1;
  }
}

.storeImages {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  gap: 10px;
  @media only screen and (min-width: 1024px) {
    align-items: flex-start;
    gap: 0;
    flex-direction: column;
  }
}

.columnTitle {
  font-weight: 600;
  color: gray;
  margin-top: 20px;

  @media only screen and (min-width: 768px) {
    margin-top: 0;
  }
}

.socialContainer {
  display: flex;
  margin-top: 30px;
  @media only screen and (min-width: 768px) {
    margin-top: 0;
  }
  @media only screen and (min-width: 1024px) {
    margin-top: 30px;
  }
}

.legalFlexWrapper {
  @include contentWrapper;
  display: flex;
  justify-content: space-between;
  margin-top: 3em;
  padding-left: 16px;
  padding-right: 16px;
  a {
    color: gray;
    text-decoration: none;
    margin-right: 15px;
  }

  @media only screen and (max-width: 1150px) {
    padding: 0 3em;
  }

  @media only screen and (max-width: 896px) {
    margin-top: 40px;
    margin-bottom: 40px;
    padding-left: 16px;
    padding-right: 16px;
  }
}
