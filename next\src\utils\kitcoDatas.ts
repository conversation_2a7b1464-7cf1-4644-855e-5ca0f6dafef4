// TODO: Refactor this to a lib file and to be reused...
import { CURRENCY_ENUM, METAL_SYMBOLS } from '~/src/types/enums'
import type { MetalType, TCrypto } from '~/src/types/index'

export const kitcoCryptos: TCrypto[] = [
  {
    name: 'Bitcoin',
    symbol: 'BTC',
    currency: CURRENCY_ENUM.USD,
    type: 'cryptocurrency',
  },
  {
    name: 'Ethereum',
    symbol: 'ETH',
    currency: CURRENCY_ENUM.USD,
    type: 'cryptocurrency',
  },
  {
    name: 'Sol<PERSON>',
    symbol: 'SOL',
    currency: CURRENCY_ENUM.USD,
    type: 'cryptocurrency',
  },
  {
    name: '<PERSON>ano',
    symbol: 'ADA',
    currency: CURRENCY_ENUM.USD,
    type: 'cryptocurrency',
  },
  {
    name: 'Avalanche',
    symbol: 'AVAX',
    currency: CURRENCY_ENUM.USD,
    type: 'cryptocurrency',
  },
  {
    name: 'Chain<PERSON>',
    symbol: 'LIN<PERSON>',
    currency: CURRENCY_ENUM.USD,
    type: 'cryptocurrency',
  },
  {
    name: 'TRON',
    symbol: 'TRX',
    currency: CURRENCY_ENUM.USD,
    type: 'cryptocurrency',
  },
  {
    name: 'XRP',
    symbol: 'XRP',
    currency: CURRENCY_ENUM.USD,
    type: 'cryptocurrency',
  },
  {
    name: 'BNB',
    symbol: 'BNB',
    currency: CURRENCY_ENUM.USD,
    type: 'cryptocurrency',
  },
  {
    name: 'SUI',
    symbol: 'SUI',
    currency: CURRENCY_ENUM.USD,
    type: 'cryptocurrency',
  },
]

export const kitcoMetals: MetalType[] = [
  {
    name: 'Aluminum',
    symbol: METAL_SYMBOLS.ALUMINUM,
    currency: CURRENCY_ENUM.USD,
    type: 'commodity',
  },
  {
    name: 'Copper',
    symbol: METAL_SYMBOLS.COPPER,
    currency: CURRENCY_ENUM.USD,
    type: 'commodity',
  },
  {
    name: 'Gold',
    symbol: METAL_SYMBOLS.GOLD,
    currency: CURRENCY_ENUM.USD,
    type: 'commodity',
  },
  {
    name: 'Lead',
    symbol: METAL_SYMBOLS.LEAD,
    currency: CURRENCY_ENUM.USD,
    type: 'commodity',
  },
  {
    name: 'Nickel',
    symbol: METAL_SYMBOLS.NICKEL,
    currency: CURRENCY_ENUM.USD,
    type: 'commodity',
  },
  {
    name: 'Platinum',
    symbol: METAL_SYMBOLS.PLATINUM,
    currency: CURRENCY_ENUM.USD,
    type: 'commodity',
  },
  {
    name: 'Palladium',
    symbol: METAL_SYMBOLS.PALLADIUM,
    currency: CURRENCY_ENUM.USD,
    type: 'commodity',
  },
  {
    name: 'Rhodium',
    symbol: METAL_SYMBOLS.RHODIUM,
    currency: CURRENCY_ENUM.USD,
    type: 'commodity',
  },
  {
    name: 'Silver',
    symbol: METAL_SYMBOLS.SILVER,
    currency: CURRENCY_ENUM.USD,
    type: 'commodity',
  },
  {
    name: 'Zinc',
    symbol: METAL_SYMBOLS.ZINC,
    currency: CURRENCY_ENUM.USD,
    type: 'commodity',
  },
]
