import type { FC } from 'react'
import BlockShell from '~/src/components/BlockShell/BlockShell'
import type { LondonFixQuery } from '~/src/generated'
import LondonFixNotice from '../LondonFixNotice/LondonFixNotice'

const LondonFixHome: FC<{ data: LondonFixQuery }> = ({ data }) => {
  return (
    <div className="w-full max-w-none">
      <BlockShell title="London Fix Gold">
        <LondonFixNotice
          style={{
            fontSize: '0.875rem',
            padding: '0.2rem',
            maxWidth: '100%',
            maxHeight: '850px',
            overflow: 'hidden',
            lineHeight: '0.8rem',
            backgroundColor: '#f9f9f9',
            border: '1px solid #e5e5e5',
            width: '100%',
            cursor: 'default',
          }}
        />
      </BlockShell>
    </div>
  )
}

export default LondonFixHome
