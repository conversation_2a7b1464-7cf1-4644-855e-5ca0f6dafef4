import { NextRequest } from 'next/server'

// Using Next.js public environment variables
declare const process: {
  env: {
    NEXT_PUBLIC_KGX_API_KEY: string
    NEXT_PUBLIC_KGX_API_ENDPOINT: string
  }
}

export const config = {
  runtime: 'edge',
}

export default async function handler(request: NextRequest) {
  // Set CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  }

  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        ...corsHeaders,
        'Content-Length': '0',
      },
    })
  }

  // Only allow GET requests
  if (request.method !== 'GET') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
      },
    })
  }

  const apiKey = process.env.NEXT_PUBLIC_KGX_API_KEY
  // Ensure we don't double-append the path if it's already in the endpoint
  let apiEndpoint = process.env.NEXT_PUBLIC_KGX_API_ENDPOINT || ''

  // Remove any trailing slashes and ensure we don't have duplicate path segments
  apiEndpoint = apiEndpoint.replace(/\/+$/, '')

  // If the endpoint doesn't already include /sse/kgx, append it
  if (!apiEndpoint.includes('/sse/kgx')) {
    apiEndpoint = `${apiEndpoint}/sse/kgx`
  }

  console.log('API Endpoint:', apiEndpoint)

  if (!apiKey || !apiEndpoint) {
    console.error('Required KGX environment variables are not set')
    return new Response(
      JSON.stringify({
        error: 'Server configuration error',
        message: 'Missing required environment variables for KGX API',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      },
    )
  }

  const url = `${apiEndpoint}?apiKey=${encodeURIComponent(apiKey)}`

  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error(`API Error: ${response.status} - ${errorText}`)
      throw new Error(`Failed to fetch data: ${response.statusText}`)
    }

    const reader = response.body?.getReader()

    if (!reader) {
      throw new Error('Failed to get readable stream from response')
    }

    const stream = new ReadableStream({
      async start(controller) {
        try {
          while (true) {
            const { done, value } = await reader.read()
            if (done) break
            controller.enqueue(value)
          }
        } catch (error) {
          console.error('Stream error:', error)
          controller.error(error)
        } finally {
          reader.releaseLock()
        }
      },
      cancel() {
        reader.cancel().catch(console.error)
      },
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        Connection: 'keep-alive',
        ...corsHeaders,
      },
    })
  } catch (error) {
    console.error('Proxy error:', error)
    return new Response(
      JSON.stringify({
        error: 'Failed to fetch data',
        message: error instanceof Error ? error.message : 'Unknown error',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      },
    )
  }
}
