@tailwind base;

body {
  font-family:
    'Mulish',
    -apple-system,
    BlinkMacSystemFont,
    'Se<PERSON>e UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 14px;
  color: #373737;
  margin: 0;
  padding: 0;
}

* {
  box-sizing: border-box;
}

p,
h1,
h2,
h3,
h4,
h5,
h6,
ul,
li {
  margin: 0;
  padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'Lato', sans-serif;
}

ul {
  list-style-type: none;
}

button {
  border: solid 1px transparent;
  border-radius: 2px;
  outline: 0;
  cursor: pointer;
  transition: all 200ms ease;
}

button:hover {
  opacity: 0.8;
}

a {
  text-decoration: none;
  color: navy;
  cursor: pointer;
}

[data-reach-listbox-popover] {
  z-index: 200;
}

.vjs-control:focus {
  outline: none;
}

.vjs-poster {
  background-size: cover !important;
}

.videoPlayerGlobal {
  position: absolute;
  width: 100%;
  height: 100%;
  video:focus {
    outline: none;
  }
}
.videoPlayerEmbedded {
  position: absolute;
  width: 100%;
  height: 100%;
  video:focus {
    outline: none;
  }
}

.videoPlayerGlobal button.vjs-big-play-button {
  top: 50% !important;
  left: 50% !important;
  width: 100px !important;
  height: 100px !important;
  transform: translate(-50%, -50%) !important;
  border-radius: 100% !important;
  border-color: transparent !important;
  background-color: white !important;
}

.videoPlayerGlobal span.vjs-icon-placeholder:before {
  color: black;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: scale(200%);
}

.embeddedVideoPlayerContainer {
  position: relative;
  padding-bottom: 56.25%;
  height: 0;
}

.embeddedVideoPlayer {
  position: relative;
  width: 100%;
  height: 100%;
  video:focus {
    outline: none;
  }

  &.vjs-big-play-button {
    top: 50% !important;
    left: 50% !important;
    width: 100px !important;
    height: 100px !important;
    transform: translate(-50%, -50%) !important;
    border-radius: 100% !important;
    border-color: transparent !important;
    background-color: white !important;
  }
  &.vjs-icon-placeholder:before {
    color: black;
  }
}

.player button.vjs-big-play-button {
  top: 50% !important;
  left: 50% !important;
  width: 100px !important;
  height: 100px !important;
  transform: translate(-50%, -50%) !important;
  border-radius: 100% !important;
  border-color: transparent !important;
  background-color: white !important;
}

.moreLink {
  color: #fff !important;
  text-align: center;
  width: 100%;
  background-color: #0a4e8d;
  padding: 6px 10px;
  border-radius: 5px;
  font-size: 0.75rem;
}

.mulish {
  font-family:
    'Mulish' -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif !important;
}

.font-mulish {
  font-family: 'Mulish', sans-serif !important;
}

.font-babasNeue {
  font-family: 'Bebas Neue', sans-serif !important;
}

@tailwind components;
@tailwind utilities;

@layer components {
  .animate-loading {
    @apply animate-pulse rounded-sm bg-white/10;
  }

  /* Enhanced shimmer animation for loading skeletons */
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  .link-hover {
    @apply decoration-1 transition hover:underline hover:decoration-ktc-category/40;
  }
  .summary {
    & a {
      @apply text-ktc-desc-gray;
    }
    @apply line-clamp-3 font-medium leading-5 text-ktc-summary-gray;
  }
  .btn {
    @apply rounded-sm border border-solid outline-none;
  }
  .tab {
    @apply cursor-pointer border-b-0 border-l-0 border-r-0 border-t-0 border-solid border-gray-600 text-lg font-semibold text-gray-600;
  }
  .tabsmall {
    @apply text-xs;
  }
  .tab:active {
    @apply border-b-0 border-l-0 border-r-0 border-t-0 border-transparent shadow-noshadow outline-none;
  }
  .tab:after {
    @apply border-b-0 border-l-0 border-r-0 border-t-0 border-transparent shadow-noshadow outline-none;
  }
  .tab:before {
    @apply border-b-0 border-l-0 border-r-0 border-t-0 border-transparent shadow-noshadow outline-none;
  }
  .tab:focus {
    @apply border-b-0 border-l-0 border-r-0 border-t-0 border-transparent shadow-noshadow outline-none;
  }
  .tab[aria-current]:not([aria-current='false']) {
    @apply text-gray-900;
  }
  .tab:hover {
    @apply text-gray-900;
  }
}

@layer utilities {
  .layout-cols-10 {
    @apply grid grid-cols-layout-10 gap-8;
  }
  .layout-cols-2 {
    @apply grid grid-cols-layout-2 gap-8;
  }
  .layout-cols-1 {
    @apply grid grid-cols-1 gap-8 px-4;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

.slider-control-centerleft {
  button {
    position: absolute;
    left: -37px;
    @media only screen and (max-width: 768px) {
      display: none;
    }
  }
}

.slider-control-centerright {
  button {
    position: absolute;
    right: -37px;
    @media only screen and (max-width: 768px) {
      display: none;
    }
  }
}

[data-theme='advertising'] {
  @import './advertising/bootstrap.min.scss';
  @import './advertising/style.scss';
  @import './advertising/kitco-style.scss';
  @import './advertising/kitco-reach.scss';
}

[data-theme='services'] {
  @import './pages/services/bootstrap.min.scss';
  @import './pages/services/animate.min.scss';
  @import './pages/services/font-awesome.min.scss';
  @import './pages/services/slick.scss';
  @import './pages/services/style.scss';
  @import './pages/services/services-style.scss';
}

.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
@media print {
  .no-print,
  .no-print * {
    display: none !important;
  }
  .banner-print {
    width: 430px;
    height: auto;
  }
  .page-print {
    margin: 0;
    border: initial;
    width: 21cm;
    min-height: 29.7cm;
    padding: 2cm 1cm;
    background: white;
  }
  .scrolling-table-size {
    font-size: 90%;
  }
}

.react-aria-TableBody {
  & tr td {
    a:visited ~ tr {
      background-color: #eff5ff;
    }
  }
}

// Ad related for the AnyClip player - Travis
.ac-widget-placeholder,
.ac-widget-ph {
  flex: 1 1 auto;
}

// Ad related for the mobile AnyClip player - Travis
.ac-player-wrapper.ac-floated-player {
  @media (max-width: 767px) {
    top: 80px !important;
    background: transparent !important;
  }
}

// Ad related for the mobile AnyClip player - Travis
.ac-floated-smaller-device.ac-floated-player {
  @media (max-width: 767px) {
    top: 80px !important;
    background: transparent !important;
  }
}

.custom-zoom [data-rmiz-modal-overlay='visible'] {
  background-color: rgba(0, 0, 0, 1) !important;
}

.tradingview-widget-copyright {
  text-align-last: right;
}

// Aspect Ratios
.aspect-4x3 {
  aspect-ratio: 4 / 3;
}

.aspect-8x5 {
  aspect-ratio: 8 / 5;
}

.aspect-16x9 {
  aspect-ratio: 16 / 9;
}

.aspect-1x1 {
  aspect-ratio: 1 / 1;
}
