import type { Column } from '@tanstack/react-table'
import type { CSSProperties } from 'react'

/**
 * Get the common pinning styles for the columns
 *
 * @param column
 */
export const getCommonPinningStyles = <T>(
  column: Column<T>,
  isHeader: boolean = false,
): CSSProperties => {
  const isPinned = column.getIsPinned()
  const isLastLeftPinnedColumn =
    isPinned === 'left' && column.getIsLastColumn('left')

  // Base styles that apply to all pinned columns
  const baseStyles: CSSProperties = {
    left: isPinned === 'left' ? `${column.getStart('left')}px` : undefined,
    right: isPinned === 'right' ? `${column.getAfter('right')}px` : undefined,
    position: isPinned ? 'sticky' : 'static',
    width: column.getSize(),
    zIndex: isPinned ? 2 : 0,
  }

  // Only apply special styles to the last left-pinned data cells (not headers)
  if (isLastLeftPinnedColumn && !isHeader) {
    return {
      ...baseStyles,
      position: 'sticky',
      // Use a special background that works with hover
      // This creates a semi-transparent white background that allows hover to show through
      // while still hiding content behind it
      background: 'rgba(255, 255, 255, 0.85)',
      // Add multiple box-shadows for a stronger fade effect
      boxShadow: `
        15px 0 15px -10px rgba(255, 255, 255, 0.9) inset,
        5px 0 5px rgba(255, 255, 255, 0.7) inset,
        25px 0 25px -20px rgba(255, 255, 255, 1) inset
      `,
      // Add a subtle backdrop blur to help hide content
      backdropFilter: 'blur(1px)',
      // Use clip-path to ensure the background doesn't affect borders
      clipPath: 'inset(1px 0 1px 0)', // This clips 1px from top and bottom to avoid affecting borders
      // Add a mix-blend-mode to better integrate with hover
      mixBlendMode: 'normal',
    }
  }

  return baseStyles
}
