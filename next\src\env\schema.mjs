import { z } from 'zod'

// SSR Env var schema
export const serverSchema = z.object({
  CORALTALK_JWT_SECRET: z.string(),
  DISCOURSE_API_KEY: z.string(),
  DISCOURSE_API_USERNAME: z.string(),
  DISCOURSE_SSO_SECRET: z.string(),
  MAILGUN_API_KEY: z.string(),
  MAILGUN_DOMAIN: z.string(),
  RECAPTCHA_SECRET_KEY: z.string(),
})

/**
 * Specify your client-side environment variables schema here.
 * This way you can ensure the app isn't built with invalid env vars.
 * To expose them to the client, prefix them with `NEXT_PUBLIC_`.
 */
export const clientSchema = z.object({
  NEXT_PUBLIC_ACMS_BUCKET: z.string(),
  NEXT_PUBLIC_AUDIO_BUCKET: z.string(),
  NEXT_PUBLIC_BARCHART_API_KEY: z.string(),
  NEXT_PUBLIC_BUCKET: z.string(),
  NEXT_PUBLIC_CAPTCHA_SITE_KEY: z.string(),
  NEXT_PUBLIC_CORALTALK_ENABLED: z.string(),
  NEXT_PUBLIC_CORALTALK_URL: z.string(),
  NEXT_PUBLIC_DISCOURSE_URL: z.string(),
  NEXT_PUBLIC_DRUPAL_URI: z.string(),
  NEXT_PUBLIC_ENABLE_DEV_MAPS: z.string(),
  NEXT_PUBLIC_FIREBASE_API_KEY: z.string(),
  NEXT_PUBLIC_FIREBASE_APP_ID: z.string(),
  NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: z.string(),
  NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID: z.string(),
  NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: z.string(),
  NEXT_PUBLIC_FIREBASE_PROJECT_ID: z.string(),
  NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: z.string(),
  NEXT_PUBLIC_GRAPH_GATEWAY_URL: z.string(),
  NEXT_PUBLIC_GRAPH_GATEWAY_BACKUP_URL: z.string(),
  NEXT_PUBLIC_GRAPH_NEWS_URL: z.string(),
  NEXT_PUBLIC_IMAGES_CDN_API: z.string(),
  NEXT_PUBLIC_KEY_ENCRYPT: z.string(),
  NEXT_PUBLIC_LOGIN_MENU: z.string(),
  NEXT_PUBLIC_MARKETS_OPEN_HOUR: z.string(),
  NEXT_PUBLIC_MARKETS_CLOSE_HOUR: z.string(),
  NEXT_PUBLIC_MARKETS_OPEN_DAY: z.string(),
  NEXT_PUBLIC_MARKETS_CLOSE_DAY: z.string(),
  NEXT_PUBLIC_RECAPTCHA_SITE_KEY: z.string(),
  NEXT_PUBLIC_TIMEZONE: z.string(),
  NEXT_PUBLIC_URL: z.string(),
  NEXT_PUBLIC_URL_CLOUDFLARE: z.string(),
  NEXT_PUBLIC_VCMS_BUCKET: z.string(),
  NEXT_PUBLIC_VIDEO_BUCKET: z.string(),
})

/**
 * You can't destruct `process.env` as a regular object, so you have to do
 * it manually here. This is because Next.js evaluates this at build time,
 * and only used environment variables are included in the build.
 * @type {{ [k in keyof z.infer<typeof clientSchema>]: z.infer<typeof clientSchema>[k] | undefined }}
 */
export const clientEnv = {
  NEXT_PUBLIC_ACMS_BUCKET: process.env.NEXT_PUBLIC_ACMS_BUCKET,
  NEXT_PUBLIC_AUDIO_BUCKET: process.env.NEXT_PUBLIC_AUDIO_BUCKET,
  NEXT_PUBLIC_BUCKET: process.env.NEXT_PUBLIC_BUCKET,
  NEXT_PUBLIC_BARCHART_API_KEY: process.env.NEXT_PUBLIC_BARCHART_API_KEY,
  NEXT_PUBLIC_CAPTCHA_SITE_KEY: process.env.NEXT_PUBLIC_CAPTCHA_SITE_KEY,
  NEXT_PUBLIC_CORALTALK_ENABLED: process.env.NEXT_PUBLIC_CORALTALK_ENABLED,
  NEXT_PUBLIC_CORALTALK_URL: process.env.NEXT_PUBLIC_CORALTALK_URL,
  NEXT_PUBLIC_DISCOURSE_URL: process.env.NEXT_PUBLIC_DISCOURSE_URL,
  NEXT_PUBLIC_DRUPAL_URI: process.env.NEXT_PUBLIC_DRUPAL_URI,
  NEXT_PUBLIC_ENABLE_DEV_MAPS: process.env.NEXT_PUBLIC_ENABLE_DEV_MAPS,
  NEXT_PUBLIC_FIREBASE_API_KEY: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  NEXT_PUBLIC_FIREBASE_APP_ID: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN:
    process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID:
    process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
  NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID:
    process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  NEXT_PUBLIC_FIREBASE_PROJECT_ID: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET:
    process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  NEXT_PUBLIC_GRAPH_GATEWAY_URL: process.env.NEXT_PUBLIC_GRAPH_GATEWAY_URL,
  NEXT_PUBLIC_GRAPH_GATEWAY_BACKUP_URL:
    process.env.NEXT_PUBLIC_GRAPH_GATEWAY_BACKUP_URL,
  NEXT_PUBLIC_GRAPH_NEWS_URL: process.env.NEXT_PUBLIC_GRAPH_NEWS_URL,
  NEXT_PUBLIC_IMAGES_CDN_API: process.env.NEXT_PUBLIC_IMAGES_CDN_API,
  NEXT_PUBLIC_KEY_ENCRYPT: process.env.NEXT_PUBLIC_KEY_ENCRYPT,
  NEXT_PUBLIC_LOGIN_MENU: process.env.NEXT_PUBLIC_LOGIN_MENU,
  NEXT_PUBLIC_MARKETS_OPEN_HOUR: process.env.NEXT_PUBLIC_MARKETS_OPEN_HOUR,
  NEXT_PUBLIC_MARKETS_CLOSE_HOUR: process.env.NEXT_PUBLIC_MARKETS_CLOSE_HOUR,
  NEXT_PUBLIC_MARKETS_OPEN_DAY: process.env.NEXT_PUBLIC_MARKETS_OPEN_DAY,
  NEXT_PUBLIC_MARKETS_CLOSE_DAY: process.env.NEXT_PUBLIC_MARKETS_CLOSE_DAY,
  NEXT_PUBLIC_RECAPTCHA_SITE_KEY: process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY,
  NEXT_PUBLIC_TIMEZONE: process.env.NEXT_PUBLIC_TIMEZONE,
  NEXT_PUBLIC_URL: process.env.NEXT_PUBLIC_URL,
  NEXT_PUBLIC_URL_CLOUDFLARE: process.env.NEXT_PUBLIC_URL_CLOUDFLARE,
  NEXT_PUBLIC_VCMS_BUCKET: process.env.NEXT_PUBLIC_VCMS_BUCKET,
  NEXT_PUBLIC_VIDEO_BUCKET: process.env.NEXT_PUBLIC_VIDEO_BUCKET,
}
