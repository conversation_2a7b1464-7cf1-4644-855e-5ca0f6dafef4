import type { UseQueryOptions } from '@tanstack/react-query'
import type QueryArgs from '~/src/types/QueryArgs'
import { graphs } from '../services/database/fetcher'

/**
 * Generic backup factory creator
 * This utility creates backup versions of factory functions that use the backup GraphQL endpoint
 */
export function createBackupFactory<T extends Record<string, any>>(
  originalFactory: T,
  fetcherMethod:
    | 'pricesFetch'
    | 'searchPricesFetch'
    | 'videosPricesFetch'
    | 'contentFetch',
): T {
  const backupFactory = {} as Record<string, any>

  // Get the backup fetcher method
  const backupFetcherMethod =
    fetcherMethod === 'contentFetch'
      ? 'contentFetch' // Content uses the same endpoint, no backup needed
      : (`backup${fetcherMethod.charAt(0).toUpperCase()}${fetcherMethod.slice(1)}` as keyof typeof graphs)

  // Create backup versions of each factory function
  for (const [key, originalFunction] of Object.entries(originalFactory)) {
    if (typeof originalFunction === 'function') {
      backupFactory[key] = (
        args: QueryArgs<any, any>,
      ): UseQueryOptions<any> => {
        const originalOptions = originalFunction(args)

        // If it's a content fetch, return the original since there's no backup for content
        if (fetcherMethod === 'contentFetch') {
          return originalOptions
        }

        // For prices/search/videos, use the backup fetcher
        return {
          ...originalOptions,
          queryKey: [`backup_${key}`, args.variables],
          queryFn: async () => {
            const backupFetcher =
              graphs[backupFetcherMethod as keyof typeof graphs]
            if (typeof backupFetcher === 'function') {
              return await backupFetcher(
                originalOptions.queryFn.toString().match(/gql`([^`]+)`/)?.[1] ||
                  '',
                args.variables,
              )
            }
            throw new Error(
              `Backup fetcher ${String(backupFetcherMethod)} not found`,
            )
          },
        }
      }
    }
  }

  return backupFactory as T
}

/**
 * Helper to create backup versions of price-related factories
 */
export function createBackupPricesFactory<T extends Record<string, any>>(
  originalFactory: T,
): T {
  return createBackupFactory(originalFactory, 'pricesFetch')
}

/**
 * Helper to create backup versions of search-related factories
 */
export function createBackupSearchFactory<T extends Record<string, any>>(
  originalFactory: T,
): T {
  return createBackupFactory(originalFactory, 'searchPricesFetch')
}

/**
 * Helper to create backup versions of video-related factories
 */
export function createBackupVideosFactory<T extends Record<string, any>>(
  originalFactory: T,
): T {
  return createBackupFactory(originalFactory, 'videosPricesFetch')
}
