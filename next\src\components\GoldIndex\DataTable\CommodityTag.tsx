import clsx from 'clsx'
import type { FC } from 'react'

function tagStyle(type: string, text = true) {
  switch (type) {
    case 'Precious Metals':
      return text ? 'text-[#E4B539]' : 'border-[#E4B539]'
    case 'Base Metals':
      return text ? 'text-[#5E5E5E]' : 'border-[#5E5E5E]'
    case 'Energy':
      return text ? 'text-[#1C60AC]' : 'border-[#1C60AC]'
    case 'Agriculture':
      return text ? 'text-copper-400' : 'border-copper-400'
    case 'Livestock':
      return text ? 'text-sapphire-400' : 'border-sapphire-400'
    default:
      return text ? 'text-grey-400' : 'border-grey-400'
  }
}

function getType(name: string) {
  if (!name) return null

  // Convert the input name to lowercase
  const lowerCaseName = name.toString().toLowerCase().trim()

  // Compare lowerCaseName with lowercase strings
  if (
    lowerCaseName === 'gold' ||
    lowerCaseName === 'silver' ||
    lowerCaseName === 'platinum' ||
    lowerCaseName === 'palladium' ||
    lowerCaseName === 'rhodium' ||
    lowerCaseName === 'rh'
  ) {
    return 'Precious Metals'
  }

  if (
    lowerCaseName === 'copper' ||
    lowerCaseName === 'aluminum' ||
    lowerCaseName === 'lead' ||
    lowerCaseName === 'nickel' ||
    lowerCaseName === 'uranium' ||
    lowerCaseName === 'tin' ||
    lowerCaseName === 'zinc' ||
    lowerCaseName === 'pb' ||
    lowerCaseName === 'ur'
  ) {
    return 'Base Metals'
  }

  if (lowerCaseName === 'oil' || lowerCaseName === 'crude oil') {
    return 'Energy'
  }

  if (
    lowerCaseName === 'corn' ||
    lowerCaseName === 'cotton' ||
    lowerCaseName === 'soybeans' ||
    lowerCaseName === 'wheat'
  ) {
    return 'Agriculture'
  }

  if (
    lowerCaseName === 'cattle' ||
    lowerCaseName === 'feeder cattle' ||
    lowerCaseName === 'lean hogs'
  ) {
    return 'Livestock'
  }

  // Return null if no matches
  return null
}

interface CommodityTagProps {
  name: string
}

const commodityTag: FC<CommodityTagProps> = ({ name }: CommodityTagProps) => {
  const type = getType(name)
  return (
    <div
      className={clsx(
        'flex flex-col items-start justify-center gap-2.5 rounded border px-1.5 py-0.5',
        tagStyle(type, false),
      )}
    >
      <div
        className={clsx(
          "no-wra whitespace-nowrap font-['Lato'] text-xs font-bold leading-3 tracking-tight",
          tagStyle(type),
        )}
      >
        {type}
      </div>
    </div>
  )
}

export default commodityTag
