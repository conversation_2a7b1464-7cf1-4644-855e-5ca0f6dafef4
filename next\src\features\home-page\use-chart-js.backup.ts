import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import weekday from 'dayjs/plugin/weekday'
import { useEffect, useMemo, useState } from 'react'
import generateFormedData from '~/src/hooks/Charts/generatedFormedData'
import useFetchChartDataBackup from '~/src/hooks/Charts/useFetchChartData.backup'
import type { ScalesUnion } from '~/src/hooks/Charts/useTimeScaleConfig'
import { useTimeScaleConfig } from '~/src/hooks/Charts/useTimeScaleConfig'
import type MarketStatus from '~/src/types/Market/MarketStatus'
import getMarketStatus from '~/src/utils/Market/getMarketStatus'
import { current } from '~/src/utils/timestamps'

// Extend dayjs with necessary plugins
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(weekday)

/**
 * Custom hook for handling chart logic using backup endpoint
 * @param {Object} args - The arguments for the hook
 * @param {string} args.symbol - The metal symbol
 * @param {string} args.currency - The currency code
 * @returns {Object} - Formed chart data, now data, scale, range setter, weekend data
 */
export function useChartJsBackup(args: { symbol: string; currency: string }) {
  // The current time (initialize)
  const [now, setNow] = useState(
    dayjs.unix(current()).tz(process.env.NEXT_PUBLIC_TIMEZONE),
  )

  // The market status
  const [marketStatus, setMarketStatus] = useState<MarketStatus>(
    getMarketStatus(now),
  )

  // State for timescale
  const [scale, setScales] = useState<ScalesUnion>('10m')

  // State for range of timestamps
  const [range, setRangeState] = useState<number[]>([])

  // Use the timescale configuration
  const { setRange } = useTimeScaleConfig(
    setRangeState,
    setScales,
    marketStatus.chartDayStart,
    marketStatus.chartDayEnd,
  )

  // Fetch the chart data from the backup API
  const { data, refetch } = useFetchChartDataBackup(args, now)

  // Listen for updates in the fetched data and react to it
  useEffect(() => {
    if (data) {
      const currentTime = dayjs
        .unix(current())
        .tz(process.env.NEXT_PUBLIC_TIMEZONE)
      setNow(currentTime)

      // Update the market status
      const updatedMarketStatus = getMarketStatus(currentTime)
      setMarketStatus(updatedMarketStatus)

      // Update range when new data is fetched
      setRange(scale)
    }
  }, [data, scale])

  // Generate chart data using memoization
  const chartData = useMemo(() => {
    return generateFormedData(range, data)
  }, [data, range])

  return {
    chartData,
    scale,
    setRange,
    dataWeekend: {
      date: `${marketStatus.marketCloseTime.format('dddd MMM D, YYYY h:mm A')} NY Time`,
      isShow: !marketStatus.marketOpen,
    },
    refetch, // Return refetch to trigger manual updates if necessary
  }
}
