import getSymbolFromCurrency from 'currency-symbol-map'

interface FormatPriceParams {
  value: number
  category?: string
  min?: number
  max?: number
  currency?: string
  locales?: string
}

/**
 * Helper to determine decimals based on category and value
 */
function getDecimals(
  category: string,
  value: number,
): { min: number; max: number } {
  switch (category) {
    case 'BASE METALS':
    case 'CRYPTOCURRENCIES':
      if (Math.abs(value) > 9.99) {
        return { min: 2, max: 2 }
      } else {
        return { min: 4, max: 4 }
      }
    case 'PRECIOUS METALS':
    case 'ENERGY':
      return { min: 2, max: 2 }
    default:
      return { min: 2, max: 2 }
  }
}

/**
 * Format the number based on the category
 *
 * @param {object} params - The parameters for formatting
 * @param {number} params.value - The value to format
 * @param {string} [params.category='default'] - The category of the commodity
 * @param {number} [params.min=0] - The minimum fraction digits
 * @param {number} [params.max=2] - The maximum fraction digits
 * @param {string} [params.currency] - The currency to use
 * @param {string} [params.locales='en-US'] - The locales to use
 * @returns {string} The formatted price
 */
export function formatPrice({
  value,
  category = 'default',
  min,
  max,
  currency = null,
  locales = 'en-US',
}: FormatPriceParams): string {
  // Determine decimals based on category and value, unless min/max are explicitly provided
  let decimals = getDecimals(category, value)
  const minimumFractionDigits = typeof min === 'number' ? min : decimals.min
  const maximumFractionDigits = typeof max === 'number' ? max : decimals.max

  let options: Intl.NumberFormatOptions = {
    minimumFractionDigits,
    maximumFractionDigits,
  }

  value = Math.abs(value)
  const formattedValue = value.toLocaleString(locales, options)

  if (currency) {
    const currencySymbol = getSymbolFromCurrency(currency)
    return `${currencySymbol}${formattedValue}`
  }

  return formattedValue
}
