// if (process.env.NODE_ENV === 'development') {
//   require('preact/debug')
// }
import {
  HydrationBoundary,
  QueryClientProvider,
  QueryErrorResetBoundary,
} from '@tanstack/react-query'
// tree-shaken out for prod
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { useRouter } from 'next/router'
import { StrictMode, useEffect, useRef } from 'react'
import { AdvertisingProvider } from 'react-advertising'
import 'react-medium-image-zoom/dist/styles.css'
import { Provider } from 'react-redux'
import type useSWR from 'swr'
import { SWRConfig } from 'swr'
import { adConfig as config } from '~/src/features/advertising/config'
import plugins from '~/src/features/advertising/plugins'
import PageVisibilityProvider from '~/src/hooks/Global/PageVisibilityContext'
import { kitcoQueryClient } from '~/src/services/database/kitcoQuery'
import '~/src/styles/barchart.css'
import '~/src/styles/global.scss'
import { SWRDataCtx } from '~/src/utils/swr-gql'
import { ErrBoundary } from '../components/ErrBoundary/ErrBoundary'
import { CommodityProvider } from '../contexts/CommodityContext'
import { store } from '../features/store/store'
import { useScrollRestoration } from '../utils/use-scroll-restoration'

export default function WrapAppInErrorBoundary(props: any) {
  const router = useRouter()
  const url = router.pathname
  const onError = (error) => {
    console.error('Advertising error:', error)
  }

  return (
    <StrictMode>
      <Provider store={store}>
        {/* <AdvertisingProvider
          config={{ ...config, url }}
          plugins={plugins}
          onError={onError}
        >
          <QueryErrorResetBoundary>
            <ErrBoundary>
              <App {...props} />
            </ErrBoundary>
          </QueryErrorResetBoundary>
        </AdvertisingProvider> */}
        <CommodityProvider initialCommodities={[]}>
          <AdvertisingProvider
            config={{ ...config, url }}
            plugins={plugins}
            onError={onError}
          >
            <QueryErrorResetBoundary>
              <ErrBoundary>
                <App {...props} />
              </ErrBoundary>
            </QueryErrorResetBoundary>
          </AdvertisingProvider>
        </CommodityProvider>
      </Provider>
    </StrictMode>
  )
}

const App = ({ Component, pageProps }) => {
  const router = useRouter()
  useScrollRestoration(router)
  const fallback = pageProps.fallback || <div>Loading...</div>

  const queryClient = kitcoQueryClient()

  return (
    <PageVisibilityProvider>
      <QueryClientProvider client={queryClient}>
        <HydrationBoundary state={pageProps.dehydratedState}>
          <ReactQueryDevtools initialIsOpen={false} />
          <SWRDataCtx ssrData={pageProps.ssrData}>
            <SWRConfig value={{ fallback, use: [silentRevalidate] }}>
              <Component {...pageProps} />
            </SWRConfig>
          </SWRDataCtx>
        </HydrationBoundary>
      </QueryClientProvider>
    </PageVisibilityProvider>
  )
}

// This is a SWR middleware for keeping the data even if key changes.
// this also creates a new Timestamp as a magical side effect lol react rendering
function silentRevalidate(useSWRNext: typeof useSWR) {
  return (key, fetcher, config) => {
    // Use a ref to store previous returned data.
    const laggyDataRef = useRef()

    // Actual SWR hook.
    const swr = useSWRNext(key, fetcher, config)

    useEffect(() => {
      // Update ref if data is not undefined.
      if (swr.data !== undefined) {
        laggyDataRef.current = swr.data
      }
    }, [swr.data])

    // Fallback to previous data if the current data is undefined.
    const dataOrLaggyData =
      swr.data === undefined ? laggyDataRef.current : swr.data

    // Is it showing previous data?
    const isRevalidating =
      swr.data === undefined && laggyDataRef.current !== undefined

    // Also add a `isLagging` field to SWR.
    return Object.assign({}, swr, {
      data: dataOrLaggyData,
      isRevalidating,
    })
  }
}
