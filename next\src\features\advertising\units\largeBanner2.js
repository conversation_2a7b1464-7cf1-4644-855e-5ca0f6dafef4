import { demandConfig, sizeDefaults } from './demandConfig'

export const largeBanner2 = {
  id: 'large-banner-2',
  path: '/21841313772,22554256/kitco/large_banner_two',
  sizes: sizeDefaults.largeBanner,
  sizeMappingName: 'largeBanner',
  refreshTime: 30,
  targeting: {
    refresh: 'true',
    refresh_time: '30',
  },
  prebid: [
    {
      mediaTypes: {
        banner: {
          sizeConfig: [
            {
              minViewPort: [0, 0],
              sizes: [
                [336, 280],
                [300, 250],
              ],
            },
            {
              minViewPort: [768, 0],
              sizes: [[728, 90]],
            },
            {
              minViewPort: [1024, 0],
              sizes: [
                [970, 250],
                [728, 90],
              ],
            },
          ],
        },
      },
      bids: [
        {
          bidder: 'ix',
          params: {
            siteId: '560681',
          },
        },
        {
          bidder: 'onetag',
          params: {
            pubId: demandConfig.onetag.pubId,
          },
        },
        {
          bidder: 'oms',
          params: {
            publisherId: demandConfig.brightcom.publisherId,
          },
        },
        {
          bidder: 'eplanning',
          params: {
            ci: demandConfig.eplanning.ci,
          },
        },
        {
          bidder: 'anyclip',
          params: {
            publisherId: '86',
            supplyTagId: 'd10c6ee9-cdba-4a30-9b4a-7fbdc402da3a',
          },
        },
        {
          bidder: 'rumble',
          params: {
            publisherId: 38329,
            siteId: 123,
            zoneId: 511, // 300x250
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'rumble',
          params: {
            publisherId: 38329,
            siteId: 123,
            zoneId: 525, // 728x90
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
      ],
    },
  ],
}
