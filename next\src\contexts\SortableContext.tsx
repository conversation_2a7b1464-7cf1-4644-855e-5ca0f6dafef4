import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import type { ReactNode } from 'react'
import { createContext, useCallback, useEffect, useState } from 'react'

interface SortableContextType {
  items: string[]
  moveRow: (dragIndex: number, hoverIndex: number) => void
  isDragging: boolean
}

export const SortableTableContext = createContext<SortableContextType>({
  items: [],
  moveRow: () => {},
  isDragging: false,
})

interface SortableProviderProps {
  children: ReactNode
  initialItems: string[]
  onDragEnd?: (event: DragEndEvent) => void
}

export const SortableProvider: React.FC<SortableProviderProps> = ({
  children,
  initialItems,
  onDragEnd,
}) => {
  const [items, setItems] = useState<string[]>([])
  const [isDragging, setIsDragging] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // Set client flag on mount
  useEffect(() => {
    setIsClient(true)

    // Load saved order from localStorage
    if (typeof window === 'undefined') return

    try {
      const savedOrder = localStorage.getItem('commodityOrder')
      if (savedOrder) {
        const parsed = JSON.parse(savedOrder)
        // Only set if we have the same number of items
        if (parsed.length === initialItems.length) {
          setItems(parsed)
        }
      } else {
        // Initialize with default order if no saved order exists
        setItems(initialItems)
      }
    } catch (e) {
      console.error('Failed to load saved order', e)
      setItems(initialItems)
    }
  }, [initialItems])

  // Update items when initialItems changes and we're on the client
  useEffect(() => {
    if (isClient && items.length === 0 && initialItems.length > 0) {
      setItems(initialItems)
    }
  }, [initialItems, isClient, items.length])

  const moveRow = useCallback((dragIndex: number, hoverIndex: number) => {
    if (dragIndex === hoverIndex) return

    setItems((prevItems) => {
      const newItems = [...prevItems]
      const [removed] = newItems.splice(dragIndex, 1)
      newItems.splice(hoverIndex, 0, removed)

      // Save to localStorage
      if (typeof window !== 'undefined') {
        try {
          localStorage.setItem('commodityOrder', JSON.stringify(newItems))
        } catch (e) {
          console.error('Failed to save order', e)
        }
      }

      return newItems
    })
  }, [])

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  const handleDragStart = useCallback(() => {
    setIsDragging(true)
  }, [])

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event

      if (active && over && active.id !== over.id) {
        const oldIndex = items.findIndex((item) => item === active.id)
        const newIndex = items.findIndex((item) => item === over.id)

        if (oldIndex !== -1 && newIndex !== -1) {
          moveRow(oldIndex, newIndex)
        }
      }

      // Call the parent's onDragEnd if provided
      if (onDragEnd) {
        onDragEnd(event)
      }

      setIsDragging(false)
    },
    [items, moveRow, onDragEnd],
  )

  return (
    <SortableTableContext.Provider value={{ items, moveRow, isDragging }}>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={items} strategy={verticalListSortingStrategy}>
          {children}
        </SortableContext>
      </DndContext>
    </SortableTableContext.Provider>
  )
}
