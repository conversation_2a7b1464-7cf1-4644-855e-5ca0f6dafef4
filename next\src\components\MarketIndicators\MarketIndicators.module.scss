@use './../../styles/vars' as *;

.wrapper {
  width: 100%;
}

.title {
  padding: 2px 0;
  color: white;
  font-size: 16px;
  text-align: center;
  background-color: #373737;
}

.subtitle {
  color: #000;
  text-align: center;
  padding: 5px 0;
  font-size: 12px;
  // line-height: 14px;
  border-bottom: 1px solid #ccc;
}

.contentsContainer {
  position: relative;
  padding: 10px;
}

.indexContainer {
  padding: 10px 4px;
  background-color: white;
  border-bottom: solid 1px $dark-grey;

  h5 {
    display: flex;
  }

  &:last-of-type {
    border-bottom: 0;
  }

  &:hover {
    background-color: $light-grey;
  }
}

.idxAltBg {
  background-color: #e2e1e1 !important;
}

// this color will be need to be dynamic red or green
.currentChangeFlex {
  display: flex;
  justify-content: space-between;
  color: red;
}

.updateButton {
  margin: 10px 10%;
  width: 80%;
  padding: 0.2em 0;
  text-align: center;
  color: white;
  font-size: 14px;
  font-weight: 600;
  border: solid 1px darkcyan;
  border-radius: 2px;
  background-color: darkcyan;
}

.delayText {
  font-size: 8px;
  color: #373737;
  text-align: center;
  margin-top: 8px;
}

ul.spotPriceGrid {
  & li {
    @media screen and (max-width: 768px) {
      grid-template-columns: 1fr 28% 24%;
    }
  }
  @media screen and (max-width: 768px) {
    width: 100%;
  }
}

.priceName {
  text-align: left;
  font-size: 14px;
  line-height: 18px;
  font-weight: 700;
}

.convertPrice {
  font-size: 14px;
  line-height: 18px;
  font-weight: 400;
  text-align: right;
  font-weight: bold;
}

.up {
  color: #009981;
}

.down {
  color: #fc374a;
}

.unchanged {
  color: #666666;
}

.arrowUpTsp {
  background-image: url('../../../public/sprites_homepage.png');
  width: 25px;
  height: 10px;
  background-position: -492px -695px;
}

.arrowDownTsp {
  background-image: url('../../../public/sprites_homepage.png');
  width: 25px;
  height: 10px;
  background-position: -516px -695px;
}

.arrowUnchanged {
  background-image: url('../../../public/sprites_homepage.png');
  width: 25px;
  height: 10px;
  background-position: -716px -627px;
}

.titleUSD {
  font-size: 14pt;
  font-weight: bold;
  font-style: italic;
  color: #cc0000;
}
