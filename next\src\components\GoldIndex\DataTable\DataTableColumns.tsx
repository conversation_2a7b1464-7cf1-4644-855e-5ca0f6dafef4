import type { ColumnDef } from '@tanstack/react-table'
import clsx from 'clsx'
import { FaInfoCircle } from 'react-icons/fa'
import DateTime from '~/src/components/DateTime/DateTime'
// import CommodityTag from '~/src/components/GoldIndex/DataTable/CommodityTag'
import PriceChange from '~/src/components/Price/PriceChange'
import Tooltip from '~/src/components/Tooltip/Tooltip'
import type CommodityData from '~/src/types/DataTable/CommodityData'
// import CommodityIcon from './CommodityIcon'

const columns: ColumnDef<CommodityData>[] = [
  {
    accessorKey: 'commodity',
    id: 'commodity',
    header: 'Commodities',
    cell: (info) => {
      const value = info.getValue().toString()
      return (
        <div className="sticky flex items-center h-full ">
          <div className="flex items-center pl-0">
            {/*<div className="w-3 flex justify-left mr-4 shrink-0">*/}
            {/*  <CommodityIcon name={value} />*/}
            {/*</div>*/}
            <div className="font-['Verdana'] text-sm font-normal leading-none text-neutral-900">
              {value}
            </div>
          </div>
          {/* <CommodityTag name={value} /> */}
        </div>
      )
    },
    enablePinning: true,
    size: 150,
  },
  {
    accessorKey: 'lastBid.bidVal',
    id: 'lastBid',
    header: 'Last (Bid)',
    cell: (info) => {
      const value = info.row.original.lastBid
      const bidValue =
        typeof value?.bid === 'string' ? parseFloat(value.bid) : value?.bid

      const formattedBid = new Intl.NumberFormat('en-US', {
        // style: 'currency',
        // currency: value?.currency || 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(bidValue || 0)

      return (
        <div
          className={clsx(
            'flex flex-col items-left justify-left gap-1.5 self-stretch py-4 h-[60px]',
          )}
        >
          <div className="whitespace-nowrap font-['Mulish'] text-sm font-bold leading-none text-neutral-700">
            {formattedBid}
          </div>
          <div className="flex gap-1 font-['Mulish'] text-xs font-normal leading-none text-zinc-600">
            <DateTime
              timeZone="UTC"
              timeFormat="HH:mm"
              date={value?.originalTime}
            />{' '}
            (ET)
          </div>
        </div>
      )
    },
    size: 150,
  },
  {
    accessorKey: 'changeDueToUSD.changeVal',
    id: 'changeDueToUSD',
    header: (context) => {
      // Safely access table data with fallback
      const rows = context?.table?.getRowModel?.()?.rows || []
      const firstRowData = rows.length > 0 ? rows[0]?.original : null
      const usdChangeVal = firstRowData?.changeDueToUSD?.changeVal ?? 0

      return (
        <div className="flex items-left justify-start gap-1">
          <span>
            {usdChangeVal >= 0
              ? 'Change due to Weakening of USD'
              : 'Decrease due to Strengthening of USD'}
          </span>
          <Tooltip
            text="We assume the closing times of the USD index to be the
          same as their respective metals. This is part of the reason you will
          see a variation of the change in the USD Index across different metals.
          Another reason is because the USD Index calculation happens only when
          the metal price is updated and only changes with each change in the
          metal price. That way the indicated strength or weakness in the USD
          is always correct for the time indicated of the last metal quote."
            spacing={10}
          >
            <FaInfoCircle />
          </Tooltip>
        </div>
      )
    },
    cell: (info) => {
      const value = info.row.original.changeDueToUSD
      return <PriceChange value={value} symbol="" />
    },
    size: 200,
  },
  {
    accessorKey: 'changeDueToTrade.changeVal',
    id: 'changeDueToTrade',
    header: () => (
      <>
        Change due to
        <br />
        normal trading
      </>
    ),
    cell: (info) => {
      const value = info.row.original.changeDueToTrade
      return <PriceChange value={value} symbol="" />
    },
    size: 200,
  },
  {
    accessorKey: 'totalChange.changeVal',
    id: 'totalChange',
    header: 'Total change',
    cell: (info) => {
      const value = info.row.original.totalChange
      return <PriceChange value={value} symbol="" />
    },
    size: 200,
  },
]

export default columns