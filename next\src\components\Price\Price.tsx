import clsx from 'clsx'
import type { FC } from 'react'
import { BiCaretDown, BiCaretUp } from 'react-icons/bi'
// import isZero from '~/src/utils/Math/isZero'

/**
 * Price component props
 */
interface PriceProps {
  price: string
  priceVal: number
  symbol?: string
}

/**
 * Component for displaying price
 * @param {string} price - Formatted price as string
 * @param priceVal
 * @param symbol
 * @constructor
 */
const Price: FC<PriceProps> = ({
  price,
  priceVal,
  symbol = '$',
}: PriceProps) => {
  const priceIsZero = priceVal === 0

  return (
    <div
      className={clsx(
        "flex whitespace-nowrap font-['Mulish'] text-sm font-normal leading-none",
        priceIsZero
          ? 'text-neutral-900'
          : priceVal > 0
            ? 'text-green-600'
            : 'text-red-600',
      )}
    >
      {priceIsZero ? (
        <div className="w-3.5 h-3.5" />
      ) : priceVal > 0 ? (
        <BiCaretUp />
      ) : (
        <BiCaretDown />
      )}
      {symbol}
      {price}
    </div>
  )
}

export default Price
