.linkContainer {
  margin: 1em;
  display: flex;
}

.viewLink {
  color: #fff;
  text-align: center;
  width: 100%;
  background-color: #0a4e8d;
  padding: 6px 10px;
  border-radius: 5px;
  font-size: 0.75rem;
}

.container {
  border-width: 1px;
  padding-bottom: 1rem;
}

.activeScale {
  color: #434651;
  background-color: rgb(128 128 128 / 14%);
}

.scale {
  background-color: #80808038;
  box-shadow: 0px 2px #8888888c;
  font-weight: bold;
}
