<div class="mining-billboard">
  <div class="section-one">
    <a href="%%CLICK_URL_UNESC%%[%ImageLink%]" target="_blank">
      <img src="[%Image%]" />
    </a>
  </div>
  <div class="section-two">
    <a href="%%CLICK_URL_UNESC%%[%LogoLink%]" target="_blank">
      <img src="[%Logo%]" />
    </a>
    <div class="stock-section" id="stock-section">
      <p class="stock-data-name" id="stock-symbol-data">[%StockSymbol%]</p>
      <p class="stock-name" id="stock-symbol">[%DisplayStockName%]</p>
      <p class="stock-timestamp" id="stock-timestamp"></p>
    </div>
  </div>
  <div class="section-three">
    <a
      class="line-item"
      href="%%CLICK_URL_UNESC%%[%LineItemLink1%]"
      id="l1"
      target="_blank"
      >[%LineItem1%]</a
    >
    <a
      class="line-item"
      href="%%CLICK_URL_UNESC%%[%LineItemLink2%]"
      id="l2"
      target="_blank"
      >[%LineItem2%]</a
    >
    <a
      class="line-item"
      href="%%CLICK_URL_UNESC%%[%LineItemLink3%]"
      id="l3"
      target="_blank"
      >[%LineItem3%]</a
    >
    <a
      class="line-item"
      href="%%CLICK_URL_UNESC%%[%LineItemLink4%]"
      id="l4"
      target="_blank"
      >[%LineItem4%]</a
    >
    <a
      class="line-item"
      href="%%CLICK_URL_UNESC%%[%LineItemLink5%]"
      id="l5"
      target="_blank"
      >[%LineItem5%]</a
    >
  </div>
  <div class="section-four" id="section-four">
    <div class="price-container">
      <div class="stock-price-container">
        <span class="stock-price" id="stock-price"></span>
        <span class="stock-arrow-up" id="stock-arrow-up"
          ><svg
            fill="none"
            height="28"
            viewBox="0 0 28 28"
            width="28"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M25.5749 7.72333C25.4565 7.43826 25.23 7.21172 24.9449 7.09333C24.8046 7.03355 24.654 7.00184 24.5016 7H18.6682C18.3588 7 18.0621 7.12292 17.8433 7.34171C17.6245 7.5605 17.5016 7.85725 17.5016 8.16667C17.5016 8.47609 17.6245 8.77283 17.8433 8.99162C18.0621 9.21042 18.3588 9.33333 18.6682 9.33333H21.6899L15.1682 15.855L11.3299 12.005C11.2214 11.8956 11.0924 11.8089 10.9502 11.7496C10.8081 11.6904 10.6556 11.6599 10.5016 11.6599C10.3475 11.6599 10.1951 11.6904 10.0529 11.7496C9.91071 11.8089 9.78168 11.8956 9.67322 12.005L2.67322 19.005C2.56387 19.1135 2.47708 19.2425 2.41785 19.3847C2.35862 19.5268 2.32812 19.6793 2.32812 19.8333C2.32813 19.9873 2.35862 20.1398 2.41785 20.282C2.47708 20.4242 2.56387 20.5532 2.67322 20.6617C2.78168 20.771 2.91071 20.8578 3.05288 20.917C3.19505 20.9763 3.34754 21.0068 3.50156 21.0068C3.65557 21.0068 3.80806 20.9763 3.95023 20.917C4.0924 20.8578 4.22143 20.771 4.32989 20.6617L10.5016 14.4783L14.3399 18.3283C14.4483 18.4377 14.5774 18.5245 14.7195 18.5837C14.8617 18.6429 15.0142 18.6734 15.1682 18.6734C15.3222 18.6734 15.4747 18.6429 15.6169 18.5837C15.7591 18.5245 15.8881 18.4377 15.9966 18.3283L23.3349 10.9783V14C23.3349 14.3094 23.4578 14.6062 23.6766 14.825C23.8954 15.0437 24.1921 15.1667 24.5016 15.1667C24.811 15.1667 25.1077 15.0437 25.3265 14.825C25.5453 14.6062 25.6682 14.3094 25.6682 14V8.16667C25.6664 8.01421 25.6347 7.86359 25.5749 7.72333Z"
              fill="#18A751"
            /></svg
        ></span>
        <span class="stock-arrow-down" id="stock-arrow-down">
          <svg
            fill="none"
            height="28"
            viewBox="0 0 28 28"
            width="28"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M24.4996 12.8342C24.1901 12.8342 23.8934 12.9571 23.6746 13.1759C23.4558 13.3947 23.3329 13.6914 23.3329 14.0009V17.0225L15.9946 9.67253C15.8861 9.56318 15.7571 9.47639 15.6149 9.41716C15.4727 9.35793 15.3202 9.32744 15.1662 9.32744C15.0122 9.32744 14.8597 9.35793 14.7176 9.41716C14.5754 9.47639 14.4464 9.56318 14.3379 9.67253L10.4996 13.5225L4.3279 7.3392C4.10821 7.11951 3.81025 6.99609 3.49957 6.99609C3.18888 6.99609 2.89092 7.11951 2.67123 7.3392C2.45154 7.55889 2.32812 7.85685 2.32812 8.16753C2.32813 8.47822 2.45154 8.77618 2.67123 8.99587L9.67123 15.9959C9.77969 16.1052 9.90872 16.192 10.0509 16.2512C10.1931 16.3105 10.3456 16.341 10.4996 16.341C10.6536 16.341 10.8061 16.3105 10.9482 16.2512C11.0904 16.192 11.2194 16.1052 11.3279 15.9959L15.1662 12.1459L21.6879 18.6675H18.6662C18.3568 18.6675 18.0601 18.7904 17.8413 19.0092C17.6225 19.228 17.4996 19.5248 17.4996 19.8342C17.4996 20.1436 17.6225 20.4404 17.8413 20.6592C18.0601 20.878 18.3568 21.0009 18.6662 21.0009H24.4996C24.652 20.999 24.8026 20.9673 24.9429 20.9075C25.228 20.7891 25.4545 20.5626 25.5729 20.2775C25.6327 20.1373 25.6644 19.9867 25.6662 19.8342V14.0009C25.6662 13.6914 25.5433 13.3947 25.3245 13.1759C25.1057 12.9571 24.809 12.8342 24.4996 12.8342Z"
              fill="#A70202"
            />
          </svg>
        </span>
      </div>
      <div class="stock-change-container">
        <span id="net-change"></span>
        <span id="percent-change"></span>
      </div>
    </div>
    <a href="%%CLICK_URL_UNESC%%[%StockDataLink%]">
      <div class="hlv-container">
        <p id="high">H:</p>
        <p id="low">L:</p>
        <p id="volume">Vol:</p>
      </div>
    </a>
  </div>
</div>
<script>
  var stockTimestamp = document.getElementById('stock-timestamp')
  var stockSymbol = document.getElementById('stock-symbol-data').innerHTML
  var price = document.getElementById('stock-price')
  var nChange = document.getElementById('net-change')
  var pChange = document.getElementById('percent-change')
  var upArrow = document.getElementById('stock-arrow-up')
  var downArrow = document.getElementById('stock-arrow-down')
  var vol = document.getElementById('volume')
  var high = document.getElementById('high')
  var low = document.getElementById('low')
  var line1 = document.getElementById('l1')
  var line2 = document.getElementById('l2')
  var line3 = document.getElementById('l3')
  var line4 = document.getElementById('l4')
  var line5 = document.getElementById('l5')
  var sectionFour = document.getElementById('section-four')

  if (!line1.innerHTML) {
    line1.style.display = 'none'
  }
  if (!line2.innerHTML) {
    line2.style.display = 'none'
  }
  if (!line3.innerHTML) {
    line3.style.display = 'none'
  }
  if (!line4.innerHTML) {
    line4.style.display = 'none'
  }
  if (!line5.innerHTML) {
    line5.style.display = 'none'
  }

  fetch('https://kdb-gw.prod.kitco.com/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query:
        'query { GetStockV3 { ID Symbol Name ChangePercentage Change Price High Low Volume Timestamp } }',
    }),
  })
    .then((res) => res.json())
    .then(function (res) {
      if (res.data.GetStockV3 == null) {
        sectionFour.style.display = 'none'
      }
      var stock = res.data.GetStockV3.find((x) => x.Symbol === stockSymbol)
      if (!stock) {
        console.info('No stock data for that stock')
        sectionFour.style.display = 'none'
      }
      var arrow = stock.Change
      if (arrow > 0) {
        upArrow.style.display = 'inline'
      } else {
        downArrow.style.display = 'inline'
      }
      var options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        timeZone: process.env.NEXT_PUBLIC_TIMEZONE,
        hour: 'numeric',
        minute: 'numeric',
        timeZoneName: 'short',
      }
      var date = new Date(Date.parse(stock.Timestamp)).toLocaleDateString(
        'EN-US',
        options,
      )
      var netChange = stock.Change
      var percentChangeData = stock.ChangePercentage

      if (Math.sign(netChange) === 1) {
        nChange.style.color = '#18A751'
      } else if (Math.sign(netChange) === -1) {
        nChange.style.color = '#A70202'
      }

      if (Math.sign(percentChangeData) === 1) {
        pChange.style.color = '#18A751'
      } else if (Math.sign(percentChangeData) === -1) {
        pChange.style.color = '#A70202'
      }

      stockTimestamp.innerHTML = date
      price.innerHTML = '$' + stock.Price.toFixed(2)
      nChange.innerHTML = Number(netChange.toFixed(3))
      pChange.innerHTML = '(' + percentChangeData + '%)'
      vol.innerHTML =
        'Vol: ' + (Math.round(stock.Volume * 100) / 100).toLocaleString()
      high.innerHTML = 'H: ' + Math.round(stock.High * 100) / 100
      low.innerHTML = 'L: ' + Math.round(stock.Low * 100) / 100
    })
</script>
