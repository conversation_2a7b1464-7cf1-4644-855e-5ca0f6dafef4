.chart {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 280px;
  width: 100%;
}

.chart * {
  box-sizing: inherit;
}

.chartFull {
  height: 480px;
}

.bcharts-tooltip {
  background-color: #222;
  color: #ccc;
  cursor: default;
  font-size: 12px;
  pointer-events: none;
  white-space: nowrap;
  box-shadow: 1px 1px 3px darkgray;
  top: -10px !important;
  left: 0px !important;
}

.bcharts-tooltip table tr td {
  padding: 0 5px !important;
  border-bottom: none;
}

.bcharts-tooltip .field-value {
  text-align: right;
}

.bcharts-card {
  z-index: 20;
  background: rgba(0, 0, 0, 0);
  line-height: 1.6;
  font-size: 11px;
  padding: 3px;
  vertical-align: center;
}

.bcharts-card-first {
  margin-top: 0px;
}

.bcharts-card .plot:first-of-type {
  display: inline;
}

.bcharts-card .value-title {
  color: #373737;
  cursor: pointer;
}

.bcharts-card .field-name {
  color: #777;
}

.bcharts-card .field-value {
  font-weight: 700;
  color: #2eb9ff;
}

.bcharts-card .curve-color {
  padding-right: 2px;
}

.bcharts-card .move-pane {
  padding: 3px;
  fill: #ffd1dc;
  cursor: pointer;
}

.bcharts-card .delete-plot {
  padding: 3px;
  fill: #999;
  cursor: pointer;
}

.bcharts-card .delete-plot:hover {
  fill: #f00;
}

.bubble-box {
  background-color: #272e32;
  box-shadow:
    0px 0px 0px 1px #3a4850,
    0px 2px 3px rgba(#000, 0.6);
  border-radius: 1px 1px 0 1px;
  padding: 8px 10px;
  line-height: 18px;
}

.bubble-box-date {
  color: rgba(200, 201, 201, 0.8);
}

.bubble-box-last {
  font-size: 18px;
  font-weight: 700;
  color: #373737;
  margin-bottom: 2px;
  margin-top: 1px;
}

.bubble-box-values {
  padding-left: 15px;
}

.bubble-box-volume {
  color: rgba(200, 201, 201, 0.8);
}

.bubble-box-label {
  padding-right: 5px;
  color: rgba(200, 201, 201, 0.8);
}

.bubble-box-value {
  padding-left: 5px;
  color: #373737;
  font-weight: 400;
}

.bcharts-logo {
  opacity: 0.15;
}

.bcharts-logo .minor {
  opacity: 0.55;
  fill: #ccc;
}

.bcharts-logo .major {
  fill: #ccc;
}

.go-to-latest {
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.9);
  outline: none;
  border: none;
  padding: 0;
  width: 24px;
  height: 24px;
  fill: #eee;
}
