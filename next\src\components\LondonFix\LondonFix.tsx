import dayjs from 'dayjs'
import type { FC } from 'react'
import SkeletonTable from '~/src/components/SkeletonTable/SkeletonTable'
import type { LondonFixByYearQuery } from '~/src/generated'
import priceFormatter from '~/src/utils/priceFormatter'

/**
 * LondonFix component props
 *
 * @interface LondonFixProps
 * @property {LondonFixByYearQuery} londonData - The London Fix data
 * @property {string} timeCreateFile - The time the file was created
 */
interface LondonFixProps {
  londonData: LondonFixByYearQuery
  timeCreateFile: string
}

/**
 * LondonFix component for rendering London Fix data
 * It renders the data in a table format
 *
 * @param {LondonFixProps} props - The component props
 * @returns {JSX.Element} The rendered component
 * @constructor
 */
const LondonFix: FC<LondonFixProps> = ({
  londonData,
  timeCreateFile,
}: LondonFixProps) => {
  // Pick the latest result only
  const latestResult = londonData?.GetLondonFixByYearV3?.results?.[0]

  return (
    <div className="scrollbar-hide scrolling-table-size overflow-x-auto">
      <div className="mt-10 min-w-[19cm] md:mt-16">
        <h3 className="mb-8 text-xl">London Fix</h3>
        <div className="grid grid-cols-layout-5 gap-4 border-t border-dashed border-gray-500 pt-2">
          <span>&nbsp;</span>
          <span className="text-center">GOLD</span>
          <span className="text-center">SILVER</span>
          <span className="text-center">PLATINUM</span>
          <span className="text-center">PALLADIUM</span>
        </div>
        <div className="grid grid-cols-layout-5 gap-4 border-b border-dashed border-gray-500 pb-2">
          <span className="text-center">Date</span>
          <div className="grid grid-cols-2">
            <span className="text-center">AM</span>
            <span className="text-center">PM</span>
          </div>
          <span className="text-center">-</span>
          <div className="grid grid-cols-2">
            <span className="text-center">AM</span>
            <span className="text-center">PM</span>
          </div>
          <div className="grid grid-cols-2">
            <span className="text-center">AM</span>
            <span className="text-center">PM</span>
          </div>
        </div>
        {latestResult ? (
          <div className="grid grid-cols-layout-5 gap-4 py-1">
            <span className="text-center">
              {dayjs.unix(latestResult?.timestamp).format('MMM DD, YYYY')}
            </span>
            <div className="grid grid-cols-2 gap-4">
              <span className="text-center">N/A</span>
              <span className="text-center">N/A</span>
            </div>
            <span className="text-center">N/A</span>
            <div className="grid grid-cols-2 gap-4">
              <span className="text-center">
                {priceFormatter(latestResult?.platinumAM)}
              </span>
              <span className="text-center">
                {priceFormatter(latestResult?.platinumPM)}
              </span>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <span className="text-center">
                {priceFormatter(latestResult?.palladiumAM)}
              </span>
              <span className="text-center">
                {priceFormatter(latestResult?.palladiumPM)}
              </span>
            </div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-layout-5 gap-4 py-1">
              <SkeletonTable />
              <div className="grid grid-cols-2 gap-4">
                <SkeletonTable />
                <SkeletonTable />
              </div>
              <SkeletonTable />
              <div className="grid grid-cols-2 gap-4">
                <SkeletonTable />
                <SkeletonTable />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <SkeletonTable />
                <SkeletonTable />
              </div>
            </div>
          </>
        )}
        <div className="border-t border-dashed border-gray-500 pt-10">
          <span>File created at {timeCreateFile}</span>
        </div>
      </div>
    </div>
  )
}

export default LondonFix
