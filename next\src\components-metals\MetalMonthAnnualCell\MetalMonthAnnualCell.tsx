import type { FC } from 'react'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import MetalMonthHomePage from '~/src/components/MetalMonthHomePage/MetalMonthHomePage'
import MetalMonthShell from '~/src/components/MetalMonthHomePage/MetalMonthShell'
import useMetalNowData from '~/src/hooks/MetalQuotes/useMetalNowData'
import { metalsBackup } from '~/src/lib/metals-factory.backup.lib'
import { kitcoBackupQuery } from '~/src/services/database/kitcoQuery'
import * as timestamps from '~/src/utils/timestamps'

/**
 * MetalMonthAnnualCell component
 * This component now uses the backup GraphQL endpoint
 *
 * @returns {JSX.Element} - The rendered component
 */
const MetalMonthAnnualCell: FC = () => {
  // Get the "now" data
  const { data } = useMetalNowData()

  // Get the historical data using the backup endpoint
  const { data: historicalData } = kitcoBackupQuery(
    metalsBackup.metalMonthAnnual({
      variables: {
        symbol: 'AU',
        currency: 'USD', // This should be always USD
        timestamp: timestamps.current(),
      },
      options: {
        refetchOnWindowFocus: true,
      },
    }),
  )

  return (
    <ErrBoundary enableReset>
      <MetalMonthShell>
        <MetalMonthHomePage data={data} historicalData={historicalData} />
      </MetalMonthShell>
    </ErrBoundary>
  )
}

export default MetalMonthAnnualCell
