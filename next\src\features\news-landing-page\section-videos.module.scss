.list {
  position: relative;
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scroll-padding: 48px;
  -webkit-overflow-scrolling: touch;

  &::-webkit-scrollbar {
    display: none;
  }
}

.listItem {
  scroll-snap-stop: always;
}

.btnNav {
  position: absolute;
  top: calc(33% + 16px);

  @media screen and (min-width: 440px) and (max-width: 767px) {
    top: calc(30% + 10px);
  }
}
