# Backup GraphQL Endpoint Setup

This document explains how to use the backup GraphQL endpoint that was set up to provide fallback functionality when the primary GraphQL endpoint is unavailable.

## Environment Variables

Add the following environment variable to your `.env` file:

```env
NEXT_PUBLIC_GRAPH_GATEWAY_BACKUP_URL=https://your-backup-endpoint.com/graphql
```

This should be set alongside your existing primary endpoint:
```env
NEXT_PUBLIC_GRAPH_GATEWAY_URL=https://your-primary-endpoint.com/graphql
```

## What's Been Modified

### 1. Environment Configuration
- Added `NEXT_PUBLIC_GRAPH_GATEWAY_BACKUP_URL` to the environment schema
- Updated both client and server schema validation

### 2. GraphQL Client Infrastructure
- Added `backupPricesClient` in `fetcher.ts` that connects to the backup endpoint
- Added backup fetcher functions:
  - `backupPricesFetch()`
  - `backupSearchPricesFetch()`
  - `backupVideosPricesFetch()`

### 3. Query Infrastructure
- Added `kitcoBackupQuery()` function that works identically to `kitcoQuery()` but uses the backup endpoint
- Added `kitcoBackupQueryClient()` for creating backup query clients

### 4. Factory Functions
- Created `cryptos-factory.backup.lib.ts` with backup versions of all crypto-related queries
- Created `backup-factory.lib.ts` with utilities for creating backup versions of other factories

## Usage Examples

### Option 1: Using Backup Components

You can use the dedicated backup components:

```tsx
import { CryptosTableBackup } from '~/src/components-cryptos/CryptosTable/CryptosTableBackup'

// This will always use the backup endpoint
function MyPage() {
  return (
    <div>
      <CryptosTableBackup />
    </div>
  )
}
```

### Option 2: Using Configurable Components

You can use components that can switch between primary and backup endpoints:

```tsx
import { CryptosTableWithBackup } from '~/src/components-cryptos/CryptosTable/CryptosTableWithBackup'

function MyPage() {
  const [useBackup, setUseBackup] = useState(false)
  
  return (
    <div>
      <button onClick={() => setUseBackup(!useBackup)}>
        Switch to {useBackup ? 'Primary' : 'Backup'} Endpoint
      </button>
      <CryptosTableWithBackup useBackup={useBackup} />
    </div>
  )
}
```

### Option 3: Custom Hook with Backup

Create your own hook that can use either endpoint:

```tsx
import { cryptos } from '~/src/lib/cryptos-factory.lib'
import { cryptosBackup } from '~/src/lib/cryptos-factory.backup.lib'
import kitcoQuery, { kitcoBackupQuery } from '~/src/services/database/kitcoQuery'

function useCustomDataWithBackup(useBackup = false) {
  const queryFunction = useBackup ? kitcoBackupQuery : kitcoQuery
  const factory = useBackup ? cryptosBackup : cryptos
  
  return queryFunction(
    factory.cryptosTable({
      variables: { currency: 'USD', symbols: 'BTC,ETH' },
      options: { enabled: true }
    })
  )
}
```

## Creating Backup Versions for Other Factories

To create backup versions of other factory functions (like metals, news, etc.), you can use the backup factory utility:

```tsx
import { createBackupPricesFactory } from '~/src/lib/backup-factory.lib'
import { metals } from '~/src/lib/metals-factory.lib'

// Create a backup version of the metals factory
export const metalsBackup = createBackupPricesFactory(metals)
```

## Error Handling and Fallback Strategy

You can implement automatic fallback logic:

```tsx
import { cryptos } from '~/src/lib/cryptos-factory.lib'
import { cryptosBackup } from '~/src/lib/cryptos-factory.backup.lib'
import kitcoQuery, { kitcoBackupQuery } from '~/src/services/database/kitcoQuery'

function useDataWithAutomaticFallback() {
  // Try primary endpoint first
  const primaryQuery = kitcoQuery(
    cryptos.cryptosTable({
      variables: { currency: 'USD', symbols: 'BTC,ETH' },
      options: { enabled: true }
    })
  )
  
  // Use backup if primary fails
  const backupQuery = kitcoBackupQuery(
    cryptosBackup.cryptosTable({
      variables: { currency: 'USD', symbols: 'BTC,ETH' },
      options: { enabled: primaryQuery.isError }
    })
  )
  
  return primaryQuery.data || backupQuery.data
}
```

## Files Created/Modified

### New Files:
- `next/src/lib/backup-factory.lib.ts` - Utility for creating backup factories
- `next/src/lib/cryptos-factory.backup.lib.ts` - Backup version of cryptos factory
- `next/src/components-cryptos/CryptosTable/CryptosTableBackup.tsx` - Backup-only component
- `next/src/components-cryptos/CryptosTable/CryptosTableWithBackup.tsx` - Configurable component

### Modified Files:
- `next/src/env/schema.mjs` - Added backup URL to environment schema
- `next/src/services/database/fetcher.ts` - Added backup client and functions
- `next/src/services/database/kitcoQuery.ts` - Added backup query functions

## Benefits

1. **Redundancy**: If the primary GraphQL endpoint goes down, you can quickly switch to the backup
2. **Flexibility**: Components can be configured to use either endpoint
3. **Gradual Migration**: You can test the backup endpoint without affecting production
4. **Extensibility**: Easy to create backup versions of other factories using the utility functions

## Testing

To test the backup endpoint:

1. Set your `NEXT_PUBLIC_GRAPH_GATEWAY_BACKUP_URL` to a working GraphQL endpoint
2. Use one of the backup components or set `useBackup={true}` on a configurable component
3. Verify that data is being fetched from the backup endpoint

## Next Steps

1. Set up your backup GraphQL endpoint infrastructure
2. Add the backup URL to your environment configuration
3. Choose which components should use the backup endpoint
4. Test the backup functionality
5. Consider implementing automatic fallback logic for production use 