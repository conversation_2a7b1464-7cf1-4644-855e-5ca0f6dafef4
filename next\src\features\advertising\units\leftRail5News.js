import { demandConfig, sizeDefaults } from './demandConfig'

export const leftRail5News = {
  id: 'left-rail-5-news',
  path: '/21841313772,22554256/kitco/left_rail_two',
  sizes: sizeDefaults.skyscraper,
  sizeMappingName: 'leftRailNews',
  refreshTime: 30,
  targeting: {
    refresh: 'true',
    refresh_time: '30',
  },
  prebid: [
    {
      mediaTypes: {
        banner: {
          sizeConfig: [
            { minViewPort: [0, 0], sizes: [] },
            { minViewPort: [768, 0], sizes: [[160, 600]] },
            { minViewPort: [1270, 0], sizes: [[160, 600]] },
          ],
        },
      },
      bids: [
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '3091224',
          },
        },
        {
          bidder: 'openx',
          params: {
            unit: '*********',
            delDomain: demandConfig.openx.delDomain,
          },
        },
        {
          bidder: 'rubicon',
          params: {
            accountId: demandConfig.rubicon.accountId,
            siteId: demandConfig.rubicon.siteId,
            zoneId: '1792630',
          },
        },
        {
          bidder: 'sovrn',
          params: {
            tagid: '756860',
          },
        },
        {
          bidder: 'ix',
          params: {
            siteId: '560682',
          },
        },
        {
          bidder: 'sharethrough',
          params: {
            pkey: 'KlmfOBCyXCJ8Zps5QHRUVwDD',
          },
        },
        {
          bidder: 'medianet',
          params: {
            cid: demandConfig.medianet.cid,
            crid: '*********',
          },
        },
        {
          bidder: 'sonobi',
          params: {
            placement_id: 'c86e096fa91e147f3cf3',
          },
        },
        {
          bidder: 'onetag',
          params: {
            pubId: demandConfig.onetag.pubId,
          },
        },
        {
          bidder: 'oms',
          params: {
            publisherId: demandConfig.brightcom.publisherId,
          },
        },
        {
          bidder: 'eplanning',
          params: {
            ci: demandConfig.eplanning.ci,
          },
        },
        {
          bidder: 'nativo',
          params: {
            placementId: 1467944,
          },
        },
      ],
    },
  ],
}
