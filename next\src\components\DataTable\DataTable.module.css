/* Category tabs */
.categoryTabs {
  display: flex;
  gap: 24px;
  padding: 16px 0;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 0;
}

.categoryTab {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  padding-bottom: 8px;
  position: relative;
  text-transform: uppercase;
  transition: color 0.2s ease;
}

.categoryTab.active {
  color: #1f2937;
  font-weight: 600;
}

.categoryTab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #2563eb;
}

/* Table headers */
.tableHeader {
  --header-size: auto;
  width: var(--header-size);
  padding: 12px 16px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.tableHeader:first-child {
  border-top-left-radius: 8px;
}

.tableHeader:last-child {
  border-top-right-radius: 8px;
}

.tableHeader[data-size] {
  width: var(--header-size);
}

.tableHeaderWithSize {
  width: var(--header-size);
}
