import { demandConfig, sizeDefaults } from './demandConfig'

export const inContent1 = {
  id: 'in-content-1',
  path: '/21841313772,22554256/kitco/in_content_1',
  sizes: sizeDefaults.inContent,
  sizeMappingName: 'inContent',
  refreshTime: 30,
  targeting: {
    refresh: 'true',
    refresh_time: '30',
  },
  prebid: [
    {
      mediaTypes: {
        banner: {
          sizeConfig: [
            {
              minViewPort: [0, 0],
              sizes: [
                [300, 250],
                [336, 280],
              ],
            },
            {
              minViewPort: [768, 0],
              sizes: [
                [300, 250],
                [336, 280],
              ],
            },
            { minViewPort: [1270, 0], sizes: [] },
          ],
        },
      },
      bids: [
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '3091226',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'openx',
          params: {
            unit: '*********',
            delDomain: demandConfig.openx.delDomain,
          },
        },
        {
          bidder: 'rubicon',
          params: {
            accountId: demandConfig.rubicon.accountId,
            siteId: demandConfig.rubicon.siteId,
            zoneId: '1802116',
          },
        },
        {
          bidder: 'sovrn',
          params: {
            tagid: '1199489', // 300x250
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'ix',
          params: {
            siteId: '560685',
          },
        },
        {
          bidder: 'sharethrough',
          params: {
            pkey: 'p7dvw4qXVgdFmobZh0iVrAIg', //300x250
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'medianet',
          params: {
            cid: demandConfig.medianet.cid,
            crid: '511140884',
          },
        },
        {
          bidder: 'sonobi',
          params: {
            placement_id: '781ee483f50940c6080a', //300x250
          },
        },
        {
          bidder: 'onetag',
          params: {
            pubId: demandConfig.onetag.pubId,
          },
        },
        {
          bidder: 'oms',
          params: {
            publisherId: demandConfig.brightcom.publisherId,
          },
        },
        {
          bidder: 'eplanning',
          params: {
            ci: demandConfig.eplanning.ci,
          },
        },
        {
          bidder: 'anyclip',
          params: {
            publisherId: '86',
            supplyTagId: 'd10c6ee9-cdba-4a30-9b4a-7fbdc402da3a',
          },
        },
        {
          bidder: 'rumble',
          params: {
            publisherId: 38329,
            siteId: 123,
            zoneId: 511, // 300x250
          },
        },
      ],
    },
  ],
}
