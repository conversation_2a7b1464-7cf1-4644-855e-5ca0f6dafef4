import { getSortedRowModel, type SortingState } from '@tanstack/table-core'
import { useRouter } from 'next/router'
import React, {
  FC,
  startTransition,
  useCallback,
  useMemo,
  useState,
  useTransition,
} from 'react'
import DataTable from '~/src/components/DataTable/DataTable'
import DataTableColumns from '~/src/components/GoldIndex/DataTable/DataTableColumns'
import {
  SortableProvider,
  useSortableTable,
} from '~/src/contexts/SortableContext'
import commodityCategories from '~/src/data/GoldIndex/CommodityCategories'
import type CommodityData from '~/src/types/DataTable/CommodityData'

interface GoldIndexDataTableProps {
  data: CommodityData[]
  isLoading?: boolean
}

// Inner component that uses the sortable context
const GoldIndexDataTableInner: FC<GoldIndexDataTableProps> = ({
  data: initialData,
  isLoading,
}: GoldIndexDataTableProps) => {
  const { items } = useSortableTable<CommodityData>()
  const router = useRouter()
  const [isPending] = useTransition()

  // Get initial filter from URL query parameter
  const getInitialFilter = useCallback(() => {
    const { filter, commodity } = router.query

    // First check for direct filter parameter
    if (filter && typeof filter === 'string') {
      // Convert filter parameter to match our category keys
      const filterMap: Record<string, string> = {
        cryptocurrencies: 'CRYPTOCURRENCIES',
        'precious-metals': 'PRECIOUS METALS',
        'base-metals': 'BASE METALS',
        energy: 'ENERGY',
      }
      return filterMap[filter.toLowerCase()] || filter.toUpperCase()
    }

    // Then check for commodity parameter and determine its category
    if (commodity && typeof commodity === 'string') {
      // Find which category this commodity belongs to
      for (const [categoryName, commodities] of Object.entries(
        commodityCategories,
      )) {
        if (commodities.includes(commodity)) {
          return categoryName
        }
      }
    }

    return 'ALL'
  }, [router.query])

  const [activeFilter, setActiveFilter] = useState<string>('ALL')

  // Update filter when URL changes
  React.useEffect(() => {
    if (router.isReady) {
      const newFilter = getInitialFilter()
      startTransition(() => {
        setActiveFilter(newFilter)
      })
    }
  }, [
    router.query.filter,
    router.query.commodity,
    router.isReady,
    getInitialFilter,
  ])

  // handleFilterChange removed since filters are no longer visible
  // const handleFilterChange = (filter: string) => {
  //   startTransition(() => {
  //     setActiveFilter(filter)
  //   })
  // }

  const [sorting, setSorting] = React.useState<SortingState>([])

  const extraConfig = {
    initialState: {
      columnPinning: {
        left: ['commodity'],
      },
      pagination: {
        pageIndex: 0,
        pageSize: 25,
      },
    },
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  }

  const filteredData = useMemo(() => {
    let result = [...items]

    if (activeFilter !== 'ALL') {
      const categoryCommodities = commodityCategories[activeFilter]
      if (categoryCommodities) {
        result = result.filter((item) =>
          categoryCommodities.includes(item.commodity),
        )
      }
    }

    return result
  }, [items, activeFilter])

  return (
    <DataTable<CommodityData>
      columns={DataTableColumns}
      data={filteredData}
      extraConfig={extraConfig}
      isLoading={isLoading || isPending}
      paginationEnabled={false}
      groupByCategory={true}
      categoryOrder={[
        'PRECIOUS METALS',
        'CRYPTOCURRENCIES',
        'BASE METALS',
        'ENERGY',
      ]}
      commodityCategories={commodityCategories}
    />
  )
}

// Main component that wraps with SortableProvider
const GoldIndexDataTable: FC<GoldIndexDataTableProps> = ({
  data,
  isLoading,
}: GoldIndexDataTableProps) => {
  return (
    <SortableProvider
      initialItems={data}
      getItemId={(item: CommodityData) => item.commodity}
      storageKey="goldIndexOrder"
    >
      <GoldIndexDataTableInner data={data} isLoading={isLoading} />
    </SortableProvider>
  )
}

export default GoldIndexDataTable
