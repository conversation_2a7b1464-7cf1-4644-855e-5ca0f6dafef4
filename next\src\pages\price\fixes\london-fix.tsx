import type { GetServerSideProps } from 'next'
import { useRouter } from 'next/router'
import type { FC } from 'react'
import { useEffect } from 'react'

export const getServerSideProps: GetServerSideProps = async ({ res }) => {
  // Server-side redirect to home page
  if (res) {
    res.writeHead(302, { Location: '/' })
    res.end()
  }

  return {
    props: {},
  }
}

const LondonFix: FC = () => {
  const router = useRouter()

  // Client-side redirect as a fallback
  useEffect(() => {
    router.replace('/')
  }, [router])

  // Return null since we're redirecting
  return null
}
export default LondonFix
