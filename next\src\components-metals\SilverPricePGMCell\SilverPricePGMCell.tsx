import SilverPricePGMBlock from '~/src/components-metals/SilverPricePGMBlock/SilverPricePGMBlock'
import { metalsBackup } from '~/src/lib/metals-factory.backup.lib'
import { kitcoBackupQuery } from '~/src/services/database/kitcoQuery'
import * as timestamps from '~/src/utils/timestamps'

/**
 * SilverPricePGMCell component
 * This component now uses the backup GraphQL endpoint
 */
export default function SilverPricePGMCell() {
  const { data } = kitcoBackupQuery(
    metalsBackup.silverPGM({
      variables: {
        currency: 'USD',
        timestamp: timestamps.current(),
      },
    }),
  )

  return <SilverPricePGMBlock data={data} />
}
