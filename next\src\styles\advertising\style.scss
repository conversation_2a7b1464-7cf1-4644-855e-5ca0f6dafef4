/* http://meyerweb.com/eric/tools/css/reset/
   v2.0 | 20110126
   License: none (public domain)
*/
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

img {
  display: inline;
}

.caps {
  text-transform: uppercase;
}

.bg-light {
  background-color: #f0f0f0;
}

.anchor-offset {
  display: block;
  margin-top: -100px;
  padding-top: 100px;
}

.max-width {
  max-width: 1400px;
}

.vw-25 {
  min-width: 25vw;
}

.vw-50 {
  min-width: 50vw;
}

.vw-75 {
  min-width: 75vw;
}

.vw-100 {
  min-width: 100vw;
}

.vh-25 {
  min-height: 25vh;
}

.vh-50 {
  min-height: 50vh;
}

.vh-75 {
  min-height: 75vh;
}

.vh-100 {
  min-height: 100vh;
}

.p-10 {
  padding: 100px !important;
}

.pt-10 {
  padding-top: 100px !important;
}

.pr-10 {
  padding-right: 100px !important;
}

.pb-10 {
  padding-bottom: 100px !important;
}

.pl-10 {
  padding-left: 100px !important;
}

.px-10 {
  padding-left: 100px !important;
  padding-right: 100px !important;
}

.py-10 {
  padding-top: 100px !important;
  padding-bottom: 100px !important;
}

@media screen and (max-width: 1199px) {
  .anchor-offset {
    display: block;
    margin-top: -80px;
    padding-top: 80px;
  }
  .p-10 {
    padding: 80px !important;
  }
  .pt-10 {
    padding-top: 80px !important;
  }
  .pr-10 {
    padding-right: 80px !important;
  }
  .pb-10 {
    padding-bottom: 80px !important;
  }
  .pl-10 {
    padding-left: 80px !important;
  }
  .px-10 {
    padding-left: 80px !important;
    padding-right: 80px !important;
  }
  .py-10 {
    padding-top: 80px !important;
    padding-bottom: 80px !important;
  }
}

@media screen and (max-width: 767px) {
  .anchor-offset {
    display: block;
    margin-top: -60px;
    padding-top: 60px;
  }
  .p-10 {
    padding: 60px !important;
  }
  .pt-10 {
    padding-top: 60px !important;
  }
  .pr-10 {
    padding-right: 60px !important;
  }
  .pb-10 {
    padding-bottom: 60px !important;
  }
  .pl-10 {
    padding-left: 60px !important;
  }
  .px-10 {
    padding-left: 60px !important;
    padding-right: 60px !important;
  }
  .py-10 {
    padding-top: 60px !important;
    padding-bottom: 60px !important;
  }
}

.anim--scroll {
  animation: scrollDown 0.8s ease-in-out infinite alternate forwards;
}

@keyframes scrollDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(10px);
  }
}

.h1 {
  margin-bottom: 30px;
  color: black;
  font-family: 'Raleway', 'Helvetica Neue', Arial, sans-serif;
  font-size: 3em;
  font-weight: bold;
  letter-spacing: 4px;
  line-height: 1.3em;
  text-transform: uppercase;
}

@media screen and (max-width: 991px) {
  h1,
  .h1 {
    font-size: 2.667em;
  }
}

h2,
.h2 {
  margin-bottom: 30px;
  color: black;
  font-family: 'Raleway', 'Helvetica Neue', Arial, sans-serif;
  font-size: 3.2em;
  font-weight: 300;
  letter-spacing: 4px;
  line-height: 1.4em;
  text-transform: uppercase;
}

h3,
.h3 {
  margin-bottom: 30px;
  color: black;
  font-family: 'Raleway', 'Helvetica Neue', Arial, sans-serif;
  font-size: 2em;
  font-weight: 700;
  letter-spacing: 3px;
  line-height: 1.4em;
  text-transform: uppercase;
}

h4,
.h4 {
  margin-bottom: 10px;
  color: black;
  font-family: 'Raleway', 'Helvetica Neue', Arial, sans-serif;
  font-size: 1.2em;
  font-weight: 700;
  letter-spacing: 4px;
  line-height: 1.4em;
  text-transform: uppercase;
}

h5,
.h5,
h6,
.h6 {
  font-family: 'Raleway', 'Helvetica Neue', Arial, sans-serif;
}

p {
  margin-bottom: 30px;
  color: #15316a;
}

p:last-child {
  margin-bottom: 0;
}

small {
  font-size: 0.8em;
}

b,
strong {
  font-weight: 700;
}

.site-title {
  margin-bottom: 0px;
  color: white;
  font-weight: bold;
}

@media screen and (max-width: 479px) {
  .site-title {
    font-size: 35px;
    letter-spacing: 0.7px;
  }

  .hero a.button {
    margin-top: 100px;
  }
}

h1.site-tagline {
  color: white;
  font-size: 2.667em;
  font-weight: 300;
  letter-spacing: 3px;
  line-height: 1.3em;
  text-transform: uppercase;
}

@media screen and (max-width: 991px) {
  h1.site-tagline {
    font-size: 1.333em;
  }

  .hero__contact {
    width: 80%;
    margin: 10px 0;
  }
}

.img-responsive {
  max-width: 100%;
  height: auto;
  display: block;
}

.text-center .img-responsive {
  margin: 0 auto;
}

.site-cta {
  font-size: 20px;
  font-weight: bold;
  margin-top: 60px;
  padding-left: 80px !important;
  padding-right: 80px !important;
}

.site-cta:before {
  background-color: white !important;
}

.site-cta:active,
.site-cta:focus,
.site-cta:hover {
  color: black !important;
}

@media screen and (max-width: 767px) {
  .site-cta {
    margin-top: 40px;
  }
}

a {
  color: inherit;
}

a:not(.scroll-down):not(.scroll-up) {
  position: relative;
  font-weight: 700;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:not(.scroll-down):not(.scroll-up):after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: currentColor;
  opacity: 0.5;
}

a:not(.scroll-down):not(.scroll-up):active,
a:not(.scroll-down):not(.scroll-up):focus,
a:not(.scroll-down):not(.scroll-up):hover {
  color: #1f57aa;
  text-decoration: none;
}

a[href^='tel'] {
  color: #1f57aa;
  font-size: 2em;
  font-weight: 700;
}

a[href^='tel']:after {
  display: none;
}

.scroll-down {
  position: absolute;
  bottom: 40px;
  left: 50%;
  width: 50px;
  height: 30px;
  margin-left: -25px;
  background: transparent url('../../../public/reach/images/arrow-down.png')
    center no-repeat;
}

@media screen and (max-width: 991px) {
  .scroll-down {
    width: 30px;
    height: 15px;
    margin-left: -10px;
    background-size: 100%;
    bottom: 105px;
  }
}
@media screen and (max-width: 479px) {
  .scroll-down {
    bottom: 130px;
  }
}

.scroll-up {
  position: absolute;
  top: -65px;
  left: 50%;
  width: 50px;
  height: 30px;
  margin-left: -25px;
  background: transparent url('../../../public/reach/images/arrow-up.png')
    center no-repeat;
}

button:not(.card-btn):not(#mobile-menu-button),
.button,
a.button {
  position: relative;
  display: inline-block;
  min-width: 260px;
  padding: 18px 25px;
  background: #1f57aa;
  border: 0px solid transparent;
  color: white;
  font-weight: 700;
  letter-spacing: 1px;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  transition: 0.3s ease all;
  cursor: pointer;
  z-index: 1;
}

button:not(.card-btn):not(#mobile-menu-button)--light:before,
.button--light:before,
a.button--light:before {
  background-color: white !important;
}

button:not(.card-btn):not(#mobile-menu-button)--light:active,
button:not(.card-btn):not(#mobile-menu-button)--light:focus,
button:not(.card-btn):not(#mobile-menu-button)--light:hover,
.button--light:active,
.button--light:focus,
.button--light:hover,
a.button--light:active,
a.button--light:focus,
a.button--light:hover {
  color: black;
}

button:not(.card-btn):not(#mobile-menu-button) + button,
button:not(.card-btn):not(#mobile-menu-button) + .button,
button:not(.card-btn):not(#mobile-menu-button) + a.button,
.button + button,
.button + .button,
.button + a.button,
a.button + button,
a.button + .button,
a.button + a.button {
  margin-left: 40px;
}

button:not(.card-btn):not(#mobile-menu-button):before,
.button:before,
a.button:before {
  content: '';
  position: absolute;
  top: 0;
  right: 50%;
  bottom: 0;
  left: 50%;
  background-color: black;
  opacity: 0;
  transition: 0.5s all ease;
  z-index: -2;
}

button:not(.card-btn):not(#mobile-menu-button):after,
.button:after,
a.button:after {
  display: none;
}

button:not(.card-btn):not(#mobile-menu-button):active,
button:not(.card-btn):not(#mobile-menu-button):focus,
button:not(.card-btn):not(#mobile-menu-button):hover,
.button:active,
.button:focus,
.button:hover,
a.button:active,
a.button:focus,
a.button:hover {
  color: white;
  text-decoration: none;
}

button:not(.card-btn):not(#mobile-menu-button):active:before,
button:not(.card-btn):not(#mobile-menu-button):focus:before,
button:not(.card-btn):not(#mobile-menu-button):hover:before,
.button:active:before,
.button:focus:before,
.button:hover:before,
a.button:active:before,
a.button:focus:before,
a.button:hover:before {
  left: 0;
  right: 0;
  opacity: 1;
  transition: 0.5s all ease;
}

@media screen and (max-width: 767px) {
  button:not(.card-btn):not(#mobile-menu-button),
  .button,
  a.button {
    display: block;
  }
  button:not(.card-btn):not(#mobile-menu-button) + button,
  button:not(.card-btn):not(#mobile-menu-button) + .button,
  button:not(.card-btn):not(#mobile-menu-button) + a.button,
  .button + button,
  .button + .button,
  .button + a.button,
  a.button + button,
  a.button + .button,
  a.button + a.button {
    margin-top: 20px;
    margin-left: 0;
  }
}

.form__field {
  margin-bottom: 25px;
}

input[type='text'],
input[type='email'] {
  width: 100%;
  padding: 15px 20px;
  border: 0 solid transparent;
  border-bottom: 5px solid #dcdcdc;
  color: #15316a;
  letter-spacing: 2px;
}

textarea {
  width: 100%;
  padding: 15px 20px;
  border: 0 solid transparent;
  border-bottom: 5px solid #dcdcdc;
  color: #15316a;
  letter-spacing: 2px;
  resize: none;
}

ol {
  margin-bottom: 30px;
  list-style: decimal;
  list-style-position: inside;
}

ol li {
  margin-bottom: 10px;
}

ul.style--default {
  margin-left: 20px;
  list-style: disc;
}

ul.style--default li {
  margin-bottom: 10px;
}

.main-menu {
  margin: 0;
  padding: 0;
  list-style: none;
}

@media screen and (max-width: 991px) {
  .main-menu {
    position: fixed;
    top: 0;
    left: -300px;
    width: 300px;
    height: 100vh;
    margin: 0;
    padding: 0;
    background-color: white;
    box-shadow: 0 0 150px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
    opacity: 0;
    overflow: hidden;
    clear: both;
    z-index: 1000;
  }
}

.menu__item {
  display: inline-block;
  margin: 0 15px;
  font-weight: 300;
}

@media screen and (min-width: 1400px) {
  .menu__item {
    margin: 0 25px;
  }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
  .menu__item {
    margin: 0 5px;
  }
}

.menu__item:first-child {
  margin-left: 0;
}

.menu__item:last-child {
  margin-right: 0;
}

.menu__item > a {
  color: white;
  font-weight: bold !important;
  text-transform: uppercase;
  text-decoration: none;
}

.menu__item > a:after {
  display: none;
}

.menu__item:last-child a {
  border: 1px solid #fff;
  padding: 5px 20px;
  font-size: 0.75em;
  font-weight: normal !important;
}

.menu__item:last-child a:hover {
  border: 1px solid #1f57aa;
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
  .menu__item > a {
    font-size: 0.933em;
  }
}

@media screen and (max-width: 991px) {
  .menu__item {
    border-bottom: 1px solid #f0f0f0;
    display: block;
    margin: 0;
    padding: 0;
  }
  .menu__item a,
  .menu__item .language-switcher__current-lang {
    color: black;
    display: block;
    padding: 20px;
    font-size: 1.125em;
  }
  .menu__item .language-switcher__current-lang:after {
    position: absolute;
    top: 50%;
    right: 20px;
    background-image: url('../../../public/reach/images/arrow-down.png');
    transform: translateY(-50%);
  }
  .menu__item .language-switcher__dropdown__item > a {
    display: block;
    padding: 20px;
    font-size: 1.125em;
    border-bottom: 1px solid #333333;
  }
}

#mobile-menu-button {
  background: transparent;
  border: 0px solid transparent;
  width: 30px;
  height: 30px;
  background: transparent url('../../../public/reach/images/menu-burger.png')
    center no-repeat;
  background-size: 100%;
}

@media screen and (min-width: 992px) {
  #mobile-menu-button {
    display: none;
  }
}

body.menu-open {
  height: 100%;
  overflow: hidden;
}

body.menu-open .main-menu {
  left: 0;
  opacity: 1;
}

body.menu-open #mobile-menu-button {
  background-image: url('../../../public/reach/images/menu-close.png');
}

.section {
  position: relative;
  padding: 100px 0;
}

@media screen and (max-width: 1199px) {
  .section {
    padding: 80px 0;
  }
}

@media screen and (max-width: 767px) {
  .section {
    padding: 60px 0;
  }
  .section#contact {
    padding: 60px 0 100px 0;
  }
}

.section:not(.section--dark):not(.section--fair):not(.section--light):not(
    .section--img
  )
  + .section:not(.section--dark):not(.section--light):not(.section--img) {
  padding-top: 0;
}

.section--footer {
  padding: 40px 0 60px;
}

.section--dark {
  background-color: black;
}

.section--dark * {
  color: white;
}

.section--fair {
  background: #ececec;
}

.section--light {
  background-color: #f0f0f0;
}

.section--img {
  background-size: cover;
  background-repeat: no-repeat;
}

.section--img--center {
  background-position: center;
}

.section--img--left {
  background-position: left center;
}

.section--img--right {
  background-position: right center;
}

.overflowed-block {
  width: 115%;
}

@media (max-width: 1400px) {
  .overflowed-block {
    width: 100%;
  }
}

ul {
  list-style: outside;
  margin-bottom: 10px;
  margin-left: 20px;
}

ul > li {
  line-height: 1.7em;
}

@media (max-width: 992px) {
  #about-kitco .container {
    text-align: center;
  }
  #about-kitco h3 {
    margin-left: 0;
    text-align: center;
    width: 100%;
  }

  #about-kitco ul,
  #about-kitco .note {
    width: 90%;
    margin: 0 auto;
    text-align: left;
  }

  ul > li {
    text-align: left;
  }

  #kitco-media-block {
    padding: 25px 15px 25px 15px;
    font-size: 0.8em;
  }

  .bg-img-block {
    display: none;
  }
}

.arrow-down {
  width: 0;
  height: 0;
  border-left: 45px solid transparent;
  border-right: 45px solid transparent;
  border-top: 30px solid #ececec;
}

#contact > div {
  max-width: 700px;
  margin: 0 auto;
}
#contact h2,
#contact h3 {
  color: #000;
}

#contact h3 {
  font-weight: 700;
  line-height: 1.1em;
}

#contact .theme-colored {
  color: #1f57aa;
}

#contact-form {
  margin-top: 50px;
}

#contact-form input::placeholder,
#contact-form textarea::placeholder {
  color: #15316a;
  font-weight: 900;
}

.phone-block {
  margin-top: 30px;
}

@media (min-width: 992px) {
  #phone-num {
    display: block;
  }

  .phone-block {
    text-align: left;
    margin-top: 0;
    border-left: 5px solid #dcdcdc;
    padding-left: 25px;
  }
}

.hero {
  position: relative;
  height: 100vh;
  background: black;
  background-size: cover;
}

.hero:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: black;
  opacity: 0.1;
  z-index: 0;
}

@media screen and (max-width: 767px) {
  .hero {
    padding-top: 70px;
  }
}

.hero__partners p {
  margin-bottom: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.333em;
  font-weight: 700;
}

.hero__partners ul > li {
  display: inline-block;
}

.hero__partners ul > li + li {
  margin-left: 40px;
}

.hero__partners ul > li img {
  width: auto;
  height: 100%;
}

@media screen and (min-width: 768px) {
  .hero__partners {
    position: absolute;
    top: 90px;
    right: 30px;
    text-align: right;
  }
  .hero__partners ul > li {
    height: 40px;
  }
}

@media screen and (min-width: 992px) {
  .hero__partners {
    top: auto;
    bottom: 40px;
  }
  .hero__partners ul > li {
    height: auto;
  }
}

@media screen and (max-width: 767px) {
  .hero__partners {
    margin: 20px 0;
    padding: 0 20px;
  }
  .hero__partners p {
    margin-bottom: 10px;
    font-size: 1.2em;
  }
  .hero__partners ul > li {
    height: 40px;
  }
  .hero__partners ul > li + li {
    margin-left: 20px;
  }
}

@media screen and (max-width: 991px) {
  .hero__partners p {
    font-size: 12px;
    margin-bottom: 0;
  }
}

@media screen and (min-width: 768px) {
  .hero__contact {
    position: absolute;
    top: 90px;
    left: 30px;
    text-align: left;
  }
}

@media screen and (min-width: 992px) {
  .hero__contact {
    left: auto;
    top: 120px;
    right: 20px;
    text-align: right;
  }
}

@media screen and (max-width: 767px) {
  .hero__contact {
    margin: 10px 0;
    padding: 0 20px;
    text-align: left;
  }
  .hero__contact > a {
    font-size: 1.2em;
  }
}

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 30px;
  background-color: transparent;
  transition:
    background 0.3s ease,
    padding 0.3s ease;
  z-index: 100;
}

.navbar--sticky {
  padding: 15px 30px;
  background-color: black;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5);
}

@media screen and (max-width: 1199px) {
  .navbar {
    padding: 20px;
  }
  .navbar--sticky {
    padding: 15px 20px;
  }
}

.navbar__brand img {
  display: block;
  width: auto;
  height: 72px;
}

@media screen and (max-width: 1199px) {
  .navbar__brand img {
    height: 40px;
  }
}

@media screen and (max-width: 1199px) {
  .navbar__menu {
    margin-top: 10px;
  }
}

#footer {
  position: relative;
}

#footer .legal {
  margin-bottom: 10px;
}

#footer .copy {
  font-size: 0.867em;
  font-weight: 700;
  color: #818181;
}

#footer .copy,
#footer .copy-year {
  color: #818181;
  font-weight: 300;
  font-style: italic;
}

.social-links {
  margin-bottom: 20px;
  margin-left: 0;
}

.social-links .social-links__item {
  display: inline-block;
  font-size: 2em;
}

.social-links .social-links__item + .social-links__item {
  margin-left: 40px;
}

.social-links .social-links__item a:after {
  display: none;
}

.social-links--small .social-links__item {
  font-size: 1.2em;
}

.social-links--small .social-links__item + .social-links__item {
  margin-left: 20px;
}

.social-links--small .social-links__item a {
  color: white;
}

.social-links--small .social-links__item a:active,
.social-links--small .social-links__item a:focus,
.social-links--small .social-links__item a:hover {
  color: #1f57aa;
}

@media (min-width: 1600px) {
  .container {
    max-width: 1300px;
  }
}

* {
  box-sizing: border-box;
}

html {
  height: 100%;
  min-height: 100%;
}

body {
  min-height: 100%;
  color: #15316a;
  font-family: 'Muli', 'Helvetica Neue', Arial, sans-serif;
  font-size: 93.8%;
  font-weight: 300;
  line-height: 1.6em;
}

#wrapper {
  position: relative;
}
