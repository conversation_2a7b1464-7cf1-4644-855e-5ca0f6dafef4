@use './../../styles/vars' as *;

.wrapper {
  width: 100%;
}

.title {
  padding: 2px 0;
  color: white;
  font-size: 16px;
  text-align: center;
  background-color: #373737;
}

.contentsContainer {
  position: relative;
  padding: 10px;
}

.indexContainer {
  padding: 10px 4px;
  background-color: white;
  border-bottom: solid 1px $dark-grey;

  h5 {
    display: flex;
  }

  &:last-of-type {
    border-bottom: 0;
  }

  &:hover {
    background-color: $light-grey;
  }
}

.idxAltBg {
  background-color: #f5f5f5;
}

// this color will be need to be dynamic red or green
.currentChangeFlex {
  display: flex;
  justify-content: space-between;
  color: red;
}

.updateButton {
  margin: 10px 10%;
  width: 80%;
  padding: 0.2em 0;
  text-align: center;
  color: white;
  font-size: 14px;
  font-weight: 600;
  border: solid 1px darkcyan;
  border-radius: 2px;
  background-color: darkcyan;
}

.delayText {
  font-size: 8px;
  color: #373737;
  text-align: center;
  margin-top: 8px;
}
