import clsx from 'clsx'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/router'
import FeedbackForm from '../FeedbackForm/FeedbackForm'
import Icon from '../Icon/Icon'
import Logo from '../Logo/Logo'

import styles from './Footer.module.scss'

const SocialContainer = () => {
  const iconProps = {
    color: 'white',
    margin: '0 10px 0 0',
    size: '24px',
  }
  return (
    <div className={styles.socialContainer}>
      <a
        href="https://www.facebook.com/KitcoNews"
        target="_blank"
        rel="noreferrer"
      >
        <Icon icon="facebook" fill={true} strokeWidth="0" {...iconProps} />
      </a>
      <a
        href="https://twitter.com/kitconewsnow"
        target="_blank"
        rel="noreferrer"
      >
        <Icon icon="twitterx" fill={true} strokeWidth="0" {...iconProps} />
      </a>
      <a
        href="https://www.linkedin.com/company/kitco-metals-inc./"
        target="_blank"
        rel="noreferrer"
      >
        <Icon icon="linkedin" fill={true} strokeWidth="0" {...iconProps} />
      </a>
      <a
        href="https://www.instagram.com/kitconews/"
        target="_blank"
        rel="noreferrer"
      >
        <Icon icon="instagram" fill={false} {...iconProps} />
      </a>
      <a
        href="https://www.youtube.com/user/KitcoNews"
        target="_blank"
        rel="noreferrer"
      >
        <Icon
          icon="youtube"
          color="white"
          fill={false}
          strokeWidth="2px"
          size="24px"
        />
      </a>
      <a
        href="https://www.tiktok.com/@kitconews"
        target="_blank"
        rel="noreferrer"
        className="ml-2"
      >
        <Icon
          icon="tiktok"
          color="white"
          fill={false}
          strokeWidth="2px"
          size="24px"
        />
      </a>
    </div>
  )
}

const linkDataKitco = [
  { title: 'All Metal Quotes', href: '/price/precious-metals' },
  { title: 'Cryptocurrencies', href: '/charts' },
  { title: 'Markets', href: '/markets' },
  { title: 'News', href: '/news' },
  { title: 'Commentaries', href: '/opinion' },
  { title: 'Mining', href: '/news/category/mining' },
]

const Footer = ({ noMarginTop = false }) => {
  const r = useRouter()

  const handlePrivacyClick = () => {
    try {
      if (typeof window !== 'undefined') {
        // Type assertion for googlefc global object
        const windowWithGooglefc = window as any
        if (
          windowWithGooglefc.googlefc?.callbackQueue &&
          windowWithGooglefc.googlefc?.showRevocationMessage
        ) {
          windowWithGooglefc.googlefc.callbackQueue.push(
            windowWithGooglefc.googlefc.showRevocationMessage,
          )
        }
      }
    } catch (error) {
      // Silently fail in development - this is expected behavior
      console.log('Privacy choices not available (expected in development)')
    }
  }

  return (
    <footer
      className={clsx(
        styles.footer,
        noMarginTop ? '!mt-0' : '',
        r.pathname.includes('/news/video')
          ? styles.videoPageBG
          : styles.defaultBG,
      )}
    >
      <div
        className={`${styles.logoSocial} justify-between px-4 md:mb-6 md:block`}
      >
        <Logo />
        <SocialContainer />
      </div>
      <div
        className={clsx(styles.gridContent, 'flex flex-wrap justify-between')}
      >
        <div className="hidden lg:block">
          <Logo />
          <SocialContainer />
        </div>
        <div className={styles.displayColumn}>
          <h3 className={styles.columnTitle}>KITCO</h3>
          {linkDataKitco.map((link, index) => (
            <div key={index}>
              <Link href={link.href}>{link.title}</Link>
            </div>
          ))}
        </div>

        <div className={clsx(styles.displayColumn, 'items-center')}>
          <img
            src="/mobile_02Feb24.png"
            alt="Kitco on AppStore"
            className="w-[250px]"
          />
          <div className="flex justify-between gap-3 px-2 pt-2 lg:pt-[35px]">
            <img
              src="/KitcoGoldLiveAppQRCode.svg"
              alt="Kitco QR Code"
              className="hidden h-[85px] w-[85px] lg:block"
            />
            <p className={clsx(styles.storeImages)}>
              <a
                href="https://play.google.com/store/apps/details?id=com.kitco.goldlive&hl=en&gl=US"
                title="Download Kitco on GooglePlay"
                target="_blank"
                rel="noreferrer"
              >
                <img
                  src="/jewelers/icons/ggplay_store.svg"
                  alt="Download Kitco on GooglePlay"
                  className="h-[35px] w-[118px]"
                />
              </a>
              <a
                href="https://apps.apple.com/ca/app/gold-live-gold-price-silver/id1444886741"
                title="Download Kitco on AppStore"
                target="_blank"
                rel="noreferrer"
              >
                <img
                  src="/jewelers/icons/app_store.svg"
                  alt="Kitco on AppStore"
                  className="h-[35px] w-[118px] md:pr-[13px]"
                />
              </a>
            </p>
          </div>
        </div>
        <FeedbackForm />
      </div>
      <div className={styles.legalFlexWrapper}>
        <div className="flex flex-wrap items-center">
          <Link href="/terms-of-use">Terms of Use</Link>
          <a href="/legal/terms-and-conditions">Terms &amp; Conditions</a>
          <a href="/legal/privacy-policy">Privacy Policy</a>
          <p className="mr-[15px] text-[#808080] hidden md:block">|</p>
          <button
            className="text-[#808080] hover:opacity-100 flex items-center w-full md:w-fit"
            onClick={handlePrivacyClick}
            onKeyDown={handlePrivacyClick}
            type="button"
          >
            <p className="mr-1 text-left">Your Privacy Choices</p>
            <p className="hidden md:block">
              <Image
                src="/privacyoptions-grayscale.svg"
                alt="privacy opt out icon"
                width={40}
                height={20}
              ></Image>
            </p>
          </button>
        </div>
        <p className={styles.incText}>
          &#169; {new Date().getFullYear()} Kitco Metals Inc.
        </p>
      </div>
    </footer>
  )
}

export default Footer
