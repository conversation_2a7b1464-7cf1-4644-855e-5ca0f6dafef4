@import '../../styles/vars';

li.item {
  margin-left: 0;
  border-bottom: solid 1px $light-grey;
  padding: 0.7em 1em;
  &:last-of-type {
    border: 0;
  }
  .itemRow {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    column-gap: 0.5em;
    color: #373737;

    @media only screen and (max-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

.titles {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  column-gap: 0.5em;

  p {
    text-transform: uppercase;
    color: #979797;
  }

  @media only screen and (max-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }
}

.colorRed {
  color: red;
}

.colorGreen {
  color: rgb(9, 194, 9);
}

.bold {
  font-weight: 500;
}

.altBg {
  background-color: #f5f5f5;
}

.alignRight {
  text-align: right;
}

// loaders
.loading {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  text-align: right;
  column-gap: 0.5em;
  color: #373737;

  @media only screen and (max-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }
}
