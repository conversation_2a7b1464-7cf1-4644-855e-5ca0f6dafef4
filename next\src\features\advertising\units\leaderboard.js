import { demandConfig, sizeDefaults } from './demandConfig'

export const leaderboard = {
  id: 'leaderboard',
  path: '/21841313772,22554256/kitco/leaderboard',
  sizes: sizeDefaults.leaderboard,
  sizeMappingName: 'leaderboard',
  refreshTime: 30,
  targeting: {
    refresh: 'true',
    refresh_time: '30',
  },
  prebid: [
    {
      mediaTypes: {
        banner: {
          sizeConfig: [
            {
              minViewPort: [0, 0],
              sizes: [
                [320, 100],
                [300, 100],
                [320, 50],
              ],
            },
            {
              minViewPort: [768, 0],
              sizes: [
                [320, 100],
                [300, 100],
                [320, 50],
                [728, 90],
              ],
            },
            {
              minViewPort: [1024, 0],
              sizes: [
                [970, 250],
                [728, 90],
              ],
            },
            // { minViewPort: [1270, 0], sizes: [[728, 90]] },
          ],
        },
      },
      bids: [
        {
          bidder: 'openx',
          params: {
            unit: '*********',
            delDomain: demandConfig.openx.delDomain,
          },
        },
        {
          bidder: 'rubicon',
          params: {
            accountId: demandConfig.rubicon.accountId,
            siteId: demandConfig.rubicon.siteId,
            zoneId: '1792626',
          },
        },
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '3091221',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '3091222',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '3091223',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'sovrn',
          params: {
            tagid: '810798',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'sovrn',
          params: {
            tagid: '1199484',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'sovrn',
          params: {
            tagid: '756855',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'ix',
          params: {
            siteId: '560681',
          },
        },
        {
          bidder: 'sharethrough',
          params: {
            pkey: 'NDaFMZYwecmbKvtcfAzHZZ1c',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'sharethrough',
          params: {
            pkey: 'LDyx8GYaNz9xKe8P3tJ9D4XK',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'sharethrough',
          params: {
            pkey: 'oMhlPk2h78uNh9BQHYgv7cXj',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'medianet',
          params: {
            cid: demandConfig.medianet.cid,
            crid: '702918643',
          },
        },
        {
          bidder: 'sonobi',
          params: {
            placement_id: '300f93d73f43e7ceaaee',
          },
        },
        {
          bidder: 'onetag',
          params: {
            pubId: demandConfig.onetag.pubId,
          },
        },
        {
          bidder: 'oms',
          params: {
            publisherId: demandConfig.brightcom.publisherId,
          },
        },
        {
          bidder: 'eplanning',
          params: {
            ci: demandConfig.eplanning.ci,
          },
        },
        {
          bidder: 'nativo',
          params: {
            placementId: 1467942,
          },
        },
        {
          bidder: 'anyclip',
          params: {
            publisherId: '86',
            supplyTagId: 'd10c6ee9-cdba-4a30-9b4a-7fbdc402da3a',
          },
        },
        {
          bidder: 'rumble',
          params: {
            publisherId: 38329,
            siteId: 123,
            zoneId: 526, // 300x100
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'rumble',
          params: {
            publisherId: 38329,
            siteId: 123,
            zoneId: 525, // 728x90
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
      ],
    },
  ],
}
