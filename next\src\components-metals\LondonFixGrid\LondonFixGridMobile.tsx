import type { FC } from 'react'
// import BlockHeader from '~/src/components/BlockHeader/BlockHeader'
// import LondonFixNotice from '../LondonFixNotice/LondonFixNotice'
import styles from './LondonFixGridMobile.module.scss'

const LondonFixGridMobile: FC = () => {
  return (
    <div className={styles.wrapper + ' w-full max-w-none'}>
      {/* <BlockHeader title={'London Fix Price'} /> */}

      {/* <LondonFixNotice
        style={{
          fontSize: '0.875rem',
          padding: '0.2rem',
          lineHeight: '0.8rem',
          maxHeight: '650px',
          overflow: 'hidden',
          backgroundColor: '#f9f9f9',
          border: '0px solid #e5e5e5',
          borderBottom: 'none',
          width: '100%',
        }}
      /> */}
    </div>
  )
}

export default LondonFixGridMobile
