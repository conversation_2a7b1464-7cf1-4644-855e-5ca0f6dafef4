@import '../../styles/vars';

.wrap {
  position: relative;
}

.market {
  text-align: center;
  padding: 5px 0;
}

.bold {
  font-weight: bold;
}

.upper {
  text-transform: uppercase;
}

.green {
  color: #008000;
}

.red {
  color: #ff0000;
}

.table {
  font-size: 0.75rem;
}

.tableRow {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  column-gap: 0.25em;
  padding: 5px 10px;
}

.row {
  display: flex;
  justify-content: space-between;
  padding: 5px 10px;
}

.odd {
  background-color: $dark-grey;
}

.mid {
  // text-align: center;
}

.footer {
  display: flex;
  justify-content: space-between;
  padding: 10px;
}

.footerBtn {
  border-radius: 4px;
  box-sizing: border-box;
  display: inline-block;
  line-height: 24px;
  padding: 0 8px;
  flex-shrink: 0;
  background: #f0f3fa;
  line-height: 24px;
  overflow: hidden;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: bold;
  font-size: 12px;
  width: 70px;
  color: #1d61ae;

  &:hover {
    background: #1d61ae;
    color: #fff;
  }

  &:active {
    background: #144985;
  }
}

.up {
  color: #18a751;
}

.down {
  color: #a70202;
}
