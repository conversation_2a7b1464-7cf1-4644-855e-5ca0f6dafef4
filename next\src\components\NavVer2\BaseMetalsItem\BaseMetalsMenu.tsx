import type { SectionItems } from '~/src/types'
import SectionList from '../SectionList/SectionList'
import * as Navigation from './../Composables'

export const BaseMetalsItems: SectionItems[] = [
  {
    name: 'Copper',
    href: '/price/base-metals/[name]',
    as: '/price/base-metals/copper',
  },

  {
    name: 'Nickel',
    href: '/price/base-metals/[name]',
    as: '/price/base-metals/nickel',
  },

  {
    name: 'Aluminum',
    href: '/price/base-metals/[name]',
    as: '/price/base-metals/aluminum',
  },

  {
    name: 'Zinc',
    href: '/price/base-metals/[name]',
    as: '/price/base-metals/zinc',
  },

  {
    name: 'Lead',
    href: '/price/base-metals/[name]',
    as: '/price/base-metals/lead',
  },

  {
    name: 'Uranium',
    href: '/price/base-metals/[name]',
    as: '/price/base-metals/uranium',
  },
]

export const forYou: SectionItems[] = []

const BaseMetalsMenu = () => {
  return (
    <Navigation.SubMenuGrid>
      <Navigation.SubMenuColumn>
        <SectionList title="Charts & Prices" items={BaseMetalsItems} />
      </Navigation.SubMenuColumn>
    </Navigation.SubMenuGrid>
  )
}

export default BaseMetalsMenu
