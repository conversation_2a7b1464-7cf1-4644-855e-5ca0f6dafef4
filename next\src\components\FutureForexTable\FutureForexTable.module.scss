@import '../../styles/vars';

.heading {
  margin-bottom: 1em;
  font-size: 1.1em;

  span {
    margin-left: 1em;
    font-size: 0.9em;
    font-weight: 400;
    color: #979797;
  }
}

.name {
  font-size: 0.8em;
}

li.item {
  padding: 0.7em 1em;
  border-bottom: solid 1px $light-grey;

  &:last-of-type {
    border: 0;
  }
  @media only screen and (max-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }

  a,
  .skeleton {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    column-gap: 0.5em;
    color: #373737;

    @media only screen and (max-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

.itemMoreLink {
  display: block;
  margin-left: 0;
  border-bottom: solid 1px $light-grey;
  padding: 0.7em 1em;
}

.titles {
  margin-left: 0;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  column-gap: 0.5em;

  p {
    text-transform: uppercase;
    color: #979797;
  }
}

.colorRed {
  color: red;
}

.colorGreen {
  color: rgb(9, 194, 9);
}

.bold {
  font-weight: 500;
}

.altBg {
  background-color: #f5f5f5;
}
