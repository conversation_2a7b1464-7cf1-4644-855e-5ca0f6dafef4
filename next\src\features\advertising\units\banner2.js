import { demandConfig, sizeDefaults } from './demandConfig'

export const banner2 = {
  id: 'banner-2',
  path: '/21841313772,22554256/kitco/mid_banner_two',
  sizes: sizeDefaults.banner,
  sizeMappingName: 'banner',
  refreshTime: 30,
  targeting: {
    refresh: 'true',
    refresh_time: '30',
  },
  prebid: [
    {
      mediaTypes: {
        banner: {
          sizeConfig: [
            {
              minViewPort: [0, 0],
              sizes: [
                [300, 250],
                [336, 280],
              ],
            },
            { minViewPort: [768, 0], sizes: [[728, 90]] },
            { minViewPort: [1270, 0], sizes: [[728, 90]] },
          ],
        },
      },
      bids: [
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '3091232',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '3091233',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'openx',
          params: {
            unit: '*********',
            delDomain: demandConfig.openx.delDomain,
          },
        },
        {
          bidder: 'rubicon',
          params: {
            accountId: demandConfig.rubicon.accountId,
            siteId: demandConfig.rubicon.siteId,
            zoneId: '1802118',
          },
        },
        {
          bidder: 'sovrn',
          params: {
            tagid: '1199490', // 300x250
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'sovrn',
          params: {
            tagid: '810795', // 728x90
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'ix',
          params: {
            siteId: '560687',
          },
        },
        {
          bidder: 'sharethrough',
          params: {
            pkey: 'p7dvw4qXVgdFmobZh0iVrAIg',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'sharethrough',
          params: {
            pkey: 'oMhlPk2h78uNh9BQHYgv7cXj',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'medianet',
          params: {
            cid: demandConfig.medianet.cid,
            crid: '522696568',
          },
        },
        {
          bidder: 'sonobi',
          params: {
            placement_id: '38be6c4038f21edb4279',
          },
        },
        {
          bidder: 'onetag',
          params: {
            pubId: demandConfig.onetag.pubId,
          },
        },
        {
          bidder: 'oms',
          params: {
            publisherId: demandConfig.brightcom.publisherId,
          },
        },
        {
          bidder: 'eplanning',
          params: {
            ci: demandConfig.eplanning.ci,
          },
        },
        {
          bidder: 'nativo',
          params: {
            placementId: 1467940,
          },
        },
        {
          bidder: 'anyclip',
          params: {
            publisherId: '86',
            supplyTagId: 'd10c6ee9-cdba-4a30-9b4a-7fbdc402da3a',
          },
        },
        {
          bidder: 'rumble',
          params: {
            publisherId: 38329,
            siteId: 123,
            zoneId: 511, // 300x250
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['none'] },
          ],
        },
        {
          bidder: 'rumble',
          params: {
            publisherId: 38329,
            siteId: 123,
            zoneId: 525, // 728x90
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['banner'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
      ],
    },
  ],
}
