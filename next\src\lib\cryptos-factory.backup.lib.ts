import type { UseQueryOptions } from '@tanstack/react-query'
import { gql } from 'graphql-request'
import type {
  CryptoQuoteQuery,
  CryptoQuoteQueryVariables,
  CryptosBtcEthLtcXmrXrpQuery,
  CryptosBtcEthLtcXmrXrpQueryVariables,
  CryptosTableQuery,
  CryptosTableQueryVariables,
} from '~/src/generated'
import { graphs } from '../services/database/fetcher'
import type QueryArgs from '../types/QueryArgs'

/**
 * Backup version of the cryptos factory that uses the backup GraphQL endpoint
 * This can be used as a fallback when the primary endpoint is unavailable
 */
export const cryptosBackup = {
  cryptoQuote: (
    args: QueryArgs<CryptoQuoteQueryVariables, CryptoQuoteQuery>,
  ): UseQueryOptions<CryptoQuoteQuery> => {
    return {
      ...args.options,
      queryKey: ['cryptoQuote_backup', args.variables],
      queryFn: async () =>
        await graphs.backupPricesFetch(
          gql`
            query CryptoQuote(
              $symbol: String!
              $currency: String!
              $timestamp: Int
            ) {
              GetCryptoQuoteV3(
                symbol: $symbol
                currency: $currency
                timestamp: $timestamp
              ) {
                symbol
                currency
                results {
                  high
                  low
                  open
                  close
                  change
                  changePercentage
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  cryptosBtcEthLtcXmrXrp: (
    args: QueryArgs<
      CryptosBtcEthLtcXmrXrpQueryVariables,
      CryptosBtcEthLtcXmrXrpQuery
    >,
  ): UseQueryOptions<CryptosBtcEthLtcXmrXrpQuery> => ({
    ...args.options,
    queryKey: ['cryptosBtcEthLtcXmrXrp_backup', args.variables],
    queryFn: async () =>
      await graphs.backupPricesFetch(
        gql`
          fragment CryptoRes on Crypto {
            ID
            symbol
            results {
              ID
              high
              low
              open
              close
              change
              changePercentage
            }
          }
          query CryptosBtcEthLtcXmrXrp($currency: String!, $timestamp: Int!) {
            BTC: GetCryptoQuote(
              symbol: "BTC"
              currency: $currency
              timestamp: $timestamp
            ) {
              ...CryptoRes
            }
            ETH: GetCryptoQuote(
              symbol: "ETH"
              currency: $currency
              timestamp: $timestamp
            ) {
              ...CryptoRes
            }
            LTC: GetCryptoQuote(
              symbol: "LTC"
              currency: $currency
              timestamp: $timestamp
            ) {
              ...CryptoRes
            }
            XMR: GetCryptoQuote(
              symbol: "XMR"
              currency: $currency
              timestamp: $timestamp
            ) {
              ...CryptoRes
            }
            XRP: GetCryptoQuote(
              symbol: "XRP"
              currency: $currency
              timestamp: $timestamp
            ) {
              ...CryptoRes
            }
          }
        `,
        args.variables,
      ),
  }),

  cryptosTable: (
    args: QueryArgs<CryptosTableQueryVariables, CryptosTableQuery>,
  ): UseQueryOptions<CryptosTableQuery> => ({
    ...args.options,
    queryKey: ['cryptosTable_backup', args.variables],
    queryFn: async () =>
      await graphs.backupPricesFetch(
        gql`
          query CryptosTable($symbols: String!, $currency: String!) {
            GetCryptoComparePriceFullV3(
              symbols: $symbols
              currency: $currency
            ) {
              price
              imageUrl
              mktCap
              volumeDay
              changePctHourCalculated
              changePct24HourCalculated
              changePct7DayCalculated
              fromSymbol
              totalVolume24h
              totalVolume24hTo
              change24Hour
              changePct24HourCalculated
              highDay
              lowDay
            }
          }
        `,
        args.variables,
      ),
  }),
}
