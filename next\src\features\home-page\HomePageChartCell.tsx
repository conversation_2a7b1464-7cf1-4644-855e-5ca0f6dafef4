import { CurrencySelect } from '~/src/components/CurrencySelect'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import HomePageChartPrices from '~/src/components/HomePageChartPrices/HomePageChartPrices'
import {
  MetalSelect,
  useMetalReadOnlyAtom,
} from '~/src/components/MetalSelect/MetalSelect'
import ArticleMoreButton from '~/src/components/article-more-button/article-more-button.component'
import { useCurrency } from '~/src/hooks/Currency/useCurrency'
import useMetalNowDataBackup from '~/src/hooks/MetalQuotes/useMetalNowData.backup'
import styles from './HomePageChartCell.module.scss'
import { CanvasChart } from './home-chart'
import { useChartJsBackup } from './use-chart-js.backup'

/**
 * HomePageChartCell component
 * This component now uses the backup GraphQL endpoint
 */
const HomePageChartCell = () => {
  const metal = useMetalReadOnlyAtom()
  const currency = useCurrency()

  // Get the "now" data using backup endpoint
  const { data } = useMetalNowDataBackup(currency.symbol, metal?.symbol)

  // Get the chart data using backup endpoint
  const { chartData, scale, dataWeekend } = useChartJsBackup({
    currency: currency.symbol,
    symbol: metal?.symbol ?? 'AU',
  })

  return (
    <ErrBoundary enableReset>
      <div className={styles.container}>
        <div className="flex gap-1">
          <div className="flex-1">
            <MetalSelect hideSymbols={true} alignment="right" />
          </div>
          <div className="flex-1">
            <CurrencySelect
              classNamesListbox="h-full"
              classNamesItemListbox="h-full"
              classNamesIconListbox="pl-2 lg:border-0 border-l border-solid border-gray-300 h-[22px] flex items-center"
            />
          </div>
        </div>
        <div className="mx-2 my-2 px-2 py-1">
          <HomePageChartPrices data={data} />
        </div>
        <div className="py-8 relative">
          <CanvasChart data={chartData} scale={scale} />
          {dataWeekend.isShow && (
            <p className="absolute text-center left-0 right-0 mt-[8px]">
              {dataWeekend.date}
            </p>
          )}
        </div>

        {/*
        <div
          className={clsx(
            'flex justify-between items-center mx-1',
            styles.scale,
          )}
        >
          {buttonScales.map((x) => (
            <button
              key={x.id}
              type="button"
              onClick={() => setRange(x.scale)}
              className={clsx(
                'w-full py-1 text-xs text-bold',
                'bg-transparent ',
                scale === x.scale && styles.activeScale,
              )}
            >
              {x.label}
            </button>
          ))}
        </div>*/}

        <div className="px-1 py-2">
          <ArticleMoreButton
            title="View Charts & More"
            href={`/charts/${metal?.name?.toLowerCase()}`}
          />
        </div>
      </div>
    </ErrBoundary>
  )
}
export default HomePageChartCell
