import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import clsx from 'clsx'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { type FC, useCallback, useEffect, useState } from 'react'
import { FaSpinner } from 'react-icons/fa'
import { MdKeyboardDoubleArrowRight } from 'react-icons/md'
import { ErrBoundary } from '~/src/components/ErrBoundary/ErrBoundary'
import ProgressBar from '~/src/components/ProgressBar/ProgressBar'
import Tooltip from '~/src/components/Tooltip/Tooltip'
import { useCommodity } from '~/src/contexts/CommodityContext'
import useKGXData from '~/src/hooks/KGX/useKGXData'
import type { KGXCommodityData } from '~/src/types/DataTable/CommodityData'
import styles from './KGX.module.css'

interface KGXProps {
  className?: string
  disableDragDrop?: boolean // Add prop to disable drag and drop
}

// SortableItem component with drag handle support
const SortableItem = ({
  id,
  item,
  isEditing,
}: {
  id: string
  item: KGXCommodityData
  isEditing: boolean
}) => {
  const { text, value, color, isSpecialLink } = item.getWidgetMessage()
  const isCommodity =
    item.type === 'commodity' || item.type === 'cryptoCommodity'
  const textColor = isSpecialLink
    ? 'text-blue-600'
    : isCommodity
      ? color
      : 'text-black'

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id,
    disabled: !isEditing,
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={clsx(
        'flex h-16 flex-col sm:flex-row items-center justify-center gap-0.5 sm:gap-1 text-center leading-tight text-xs sm:text-sm md:text-base px-2 font-bold',
        textColor,
        'relative group',
        isEditing && 'bg-white shadow-sm rounded-md',
        styles.draggableItem,
        isDragging && styles.dragging,
      )}
      onClick={(e) => {
        if (isEditing) {
          e.stopPropagation()
        }
      }}
    >
      {isEditing && (
        <button
          {...attributes}
          {...listeners}
          className={styles.dragHandle}
          aria-label="Drag to reorder"
          onClick={(e) => e.stopPropagation()}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={styles.dragHandleIcon}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M4 8h16M4 16h16"
            />
          </svg>
        </button>
      )}
      <span className="text-center leading-tight font-bold">{text}</span>
      <span className="text-center leading-tight font-bold">{value}</span>
    </div>
  )
}

/**
 * KGX Component
 *
 * This component is used to display the KGX widget that shows the commodities
 * and their respective changes due to the US Dollar and predominant buyers or sellers.
 *
 * @param className - Additional CSS classes
 * @param disableDragDrop - If true, disables the drag and drop functionality
 */
const KGX: FC<KGXProps> = ({
  className,
  disableDragDrop = false,
}: KGXProps) => {
  const intervalTime: number = 10000 // 10 seconds

  // Get all the logic
  const router = useRouter()
  const {
    commodities,
    index,
    isLoading,
    isTransitioning,
    pause,
    pauseScrolling,
    reorderCommodities,
    resetOrder,
  } = useKGXData(intervalTime)

  const { setSelectedCommodity } = useCommodity()
  const [isEditing, setIsEditing] = useState(false)

  const handleItemClick = useCallback(
    (e: React.MouseEvent, item: KGXCommodityData | null) => {
      e.preventDefault()
      e.stopPropagation()

      if (isEditing) return // Don't navigate when in edit mode

      // If item is null (loading state), navigate to KGX page with "All" filter
      if (!item) {
        router.push('/markets/kitco-gold-index')
        return
      }

      // Handle special rotation items
      if (item.type === 'cryptoLink') {
        // Rotation 5: KGX now covers the top blue-chip cryptos
        router.push('/markets/kitco-gold-index?filter=cryptocurrencies')
      } else if (
        item.commodity === 'Bitcoin' ||
        item.commodity === 'Ethereum'
      ) {
        // Rotations 6 & 7: Bitcoin and Ethereum - treat as crypto commodities
        const selectedCommodity = commodities.find(
          (c) => c.commodity === item.commodity,
        )
        if (selectedCommodity) {
          setSelectedCommodity(selectedCommodity)
          router.push(
            `/markets/kitco-gold-index?commodity=${encodeURIComponent(item.commodity)}`,
          )
        } else {
          router.push('/markets/kitco-gold-index')
        }
      } else if (item.type === 'learnMore') {
        // Rotation 8: Learn more about the Kitco Global Index
        router.push('/markets/kitco-gold-index#information')
      } else if (item.type === 'cryptoCommodity' || item.type === 'commodity') {
        // Check if it's one of the precious metals that should go to "All" filter
        const preciousMetals = ['Gold', 'Silver', 'Platinum', 'Palladium']
        if (preciousMetals.includes(item.commodity)) {
          // For Gold, Silver, Platinum, Palladium - navigate to All filter
          router.push('/markets/kitco-gold-index')
        } else {
          // For other crypto commodities and regular commodities, update the selected commodity
          const selectedCommodity = commodities.find(
            (c) =>
              c.commodity === item.commodity &&
              (c.type === 'commodity' || c.type === 'cryptoCommodity'),
          )
          if (selectedCommodity) {
            setSelectedCommodity(selectedCommodity)
            router.push(
              `/markets/kitco-gold-index?commodity=${encodeURIComponent(item.commodity)}`,
            )
          } else {
            // Fallback to default behavior if commodity not found
            router.push('/markets/kitco-gold-index')
          }
        }
      } else {
        // Default behavior for other items
        router.push('/markets/kitco-gold-index')
      }
    },
    [router, commodities, setSelectedCommodity, isEditing],
  )

  useEffect(() => {
    pauseScrolling(isLoading)
  }, [isLoading, pauseScrolling])

  // Set up sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3, // Require the mouse to move by 3 pixels before activating
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  // Handle drag end
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event

      if (over && active.id !== over.id) {
        const oldIndex = commodities.findIndex(
          (item) => item.commodity === active.id,
        )
        const newIndex = commodities.findIndex(
          (item) => item.commodity === over.id,
        )

        if (oldIndex !== -1 && newIndex !== -1) {
          reorderCommodities(oldIndex, newIndex)
        }
      }
    },
    [commodities, reorderCommodities],
  )

  // Toggle edit mode
  const toggleEditMode = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault()
      e.stopPropagation()
      if (disableDragDrop) return // Don't allow edit mode if drag and drop is disabled
      setIsEditing((prev) => !prev)
    },
    [disableDragDrop],
  )

  // Reset to default order
  const handleResetOrder = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault()
      e.stopPropagation()
      resetOrder()
    },
    [resetOrder],
  )

  // Safely get current item with fallback to first item if index is out of bounds
  const currentItem =
    commodities.length > 0 ? commodities[index % commodities.length] : null
  const isLoadingOrNoItems = isLoading || !currentItem

  return (
    <ErrBoundary errorTitle="Kitco Global Index Widget">
      <div className={className ?? 'mx-auto mb-2.5 mt-3.5 w-full'}>
        <Tooltip
          content={
            <div>
              <p>Did the US Dollar fluctuation impact the price?</p>
              <p>Click here to find out more information.</p>
            </div>
          }
          timeout={1000}
        >
          <Link
            href="/markets/kitco-gold-index"
            className="flex w-full flex-col rounded text-black hover:bg-neutral-50 hover:shadow"
          >
            <div
              id="kgx_content"
              className={clsx(
                'relative flex h-16 justify-between overflow-hidden text-sm font-bold leading-9',
                styles.clickable,
                isEditing && 'cursor-default',
              )}
              onMouseOut={() => pauseScrolling(false)}
              onMouseOver={() => pauseScrolling(true)}
              onFocus={() => pauseScrolling(true)}
              onBlur={() => pauseScrolling(false)}
              onClick={(e) => {
                if (isEditing) return
                handleItemClick(e, currentItem)
              }}
            >
              <div
                id="kgx_text_title"
                className="ml-3.5 flex h-full basis-1/5 flex-col items-start justify-center text-left leading-none md:ml-9"
              >
                <span className="text-base font-bold md:text-lg">KGX</span>
                <span className="text-xs">Kitco Global Index</span>
              </div>
              <div className={styles.contentContainer}>
                <div className="relative">
                  {!disableDragDrop && isEditing && (
                    <div className="absolute -top-8 right-0 flex space-x-2">
                      <button
                        onClick={handleResetOrder}
                        className="rounded bg-gray-200 px-2 py-1 text-xs text-gray-700 hover:bg-gray-300"
                        title="Reset to default order"
                      >
                        Reset
                      </button>
                      <button
                        onClick={toggleEditMode}
                        className="rounded bg-blue-500 px-2 py-1 text-xs text-white hover:bg-blue-600"
                      >
                        Done
                      </button>
                    </div>
                  )}
                  <div
                    id="kgx_text_content"
                    className={clsx(
                      styles.contentSlide,
                      styles[`slide${index % 20}`],
                      'flex basis-3/5 flex-col justify-center text-center md:text-lg',
                      isTransitioning ? 'transition-all' : 'transition-none',
                      isEditing && 'cursor-grab active:cursor-grabbing',
                    )}
                  >
                    {isLoading ? (
                      <div className="flex h-16 items-center justify-center">
                        <FaSpinner className="animate-spin text-xl text-gray-500" />
                      </div>
                    ) : !disableDragDrop && isEditing ? (
                      <DndContext
                        sensors={sensors}
                        collisionDetection={closestCenter}
                        onDragEnd={handleDragEnd}
                      >
                        <SortableContext
                          items={commodities.map((item) => item.commodity)}
                          strategy={verticalListSortingStrategy}
                        >
                          {commodities.map((item) => (
                            <SortableItem
                              key={item.commodity}
                              id={item.commodity}
                              item={item}
                              isEditing={isEditing}
                            />
                          ))}
                        </SortableContext>
                      </DndContext>
                    ) : (
                      <div className="flex h-16 items-center justify-center">
                        {isLoadingOrNoItems ? (
                          <FaSpinner className="animate-spin text-xl text-gray-500" />
                        ) : (
                          <div
                            className={clsx(
                              'flex h-16 flex-col sm:flex-row items-center justify-center gap-0.5 sm:gap-1 text-center leading-tight text-xs sm:text-sm md:text-base px-2 font-bold',
                              currentItem &&
                                (currentItem.type === 'cryptoCommodity' ||
                                  currentItem.type === 'commodity')
                                ? currentItem.getWidgetMessage()?.color ||
                                    'text-black'
                                : 'text-black',
                            )}
                          >
                            <span className="text-center leading-tight font-bold">
                              {currentItem?.getWidgetMessage()?.text || ''}
                            </span>
                            <span className="text-center leading-tight font-bold">
                              {currentItem?.getWidgetMessage()?.value || ''}
                            </span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="mr-3.5 flex h-full basis-1/5 items-center justify-end md:mr-9">
                <MdKeyboardDoubleArrowRight className="text-xl font-bold" />
              </div>
            </div>
            <ProgressBar
              intervalTime={isTransitioning ? intervalTime : intervalTime / 2}
              index={index}
              paused={pause}
              startFrom={isTransitioning ? 0 : 0.5}
            />
          </Link>
        </Tooltip>
      </div>
    </ErrBoundary>
  )
}

export default KGX
