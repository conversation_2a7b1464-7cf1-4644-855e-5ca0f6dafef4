import {
  type DefinedUseQueryResult,
  QueryClient,
  type QueryKey,
  type UseQueryOptions,
  type UseQueryResult,
  keepPreviousData,
  useQuery,
} from '@tanstack/react-query'
import {
  isBrowser,
  usePageVisibility,
} from '~/src/hooks/Global/PageVisibilityContext'

/**
 * This file contains a custom query function that wraps the useQuery hook
 * from react-query.
 *
 * Also has a wrapper function to create a new query client.
 *
 * It handles the enabled status based on page visibility and SSR.
 * Adds custom query options with defined initial data.
 *
 * This way if we need to update or add new options, we can do it in one place.
 */

/**
 * Default query options
 */
export function defaultQueryOptions<
  TQueryFnData,
  TError,
  TData,
  TQueryKey extends QueryKey,
>(): Partial<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>> {
  return {
    //enabled: true, // Assume the query is enabled by default
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: true, // Refetch the data when the window regains focus
    retry: 3, // Retry 3 times before showing an error
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 5000),
    staleTime: 29000, // Data will be considered fresh for 29 seconds
  }
}

/**
 * A wrapper function to create a new query client
 */
export function kitcoQueryClient(): QueryClient {
  return new QueryClient({
    defaultOptions: {
      queries: defaultQueryOptions(),
    },
  })
}

type DefinedInitialDataOptions<
  TQueryFnData,
  TError,
  TData,
  TQueryKey extends QueryKey,
> = UseQueryOptions<TQueryFnData, TError, TData, TQueryKey> & {
  initialData: TData
}

type UndefinedInitialDataOptions<
  TQueryFnData,
  TError,
  TData,
  TQueryKey extends QueryKey,
> = UseQueryOptions<TQueryFnData, TError, TData, TQueryKey> & {
  initialData?: undefined
}

type DefaultError = unknown // Or the default error type you use

/**
 * Custom hook to handle enabled status based on page visibility.
 *
 * @returns - The final enabled status.
 * @param enabled
 */
export function enabledStatus(enabled: boolean): boolean {
  // Return false if the query is explicitly disabled (no need to check visibility)
  if (!enabled) {
    return false
  }

  // If we're not in a browser environment, return the enabled status
  if (!isBrowser()) {
    return enabled
  }

  // We're in a browser environment, get the page visibility status
  const pageVisibility = usePageVisibility()

  // If the page is not visible, disable the query
  return pageVisibility ? enabled : false
}

/**
 * Custom query function to wrap the useQuery hook from react-query.
 * Handles the enabled status based on page visibility and SSR.
 *
 * @param options - Custom query options.
 * @param queryClient
 */
export function kitcoQuery<
  TQueryFnData = unknown,
  TError = DefaultError,
  TData = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
>(
  options: DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError>

/**
 * Custom query function to wrap the useQuery hook from react-query.
 * Handles the enabled status based on page visibility and SSR.
 *
 * @param options - Custom query options.
 * @param queryClient
 */
export function kitcoQuery<
  TQueryFnData = unknown,
  TError = DefaultError,
  TData = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
>(
  options: UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,
  queryClient?: QueryClient,
): UseQueryResult<TData, TError>

/**
 * Custom query function to wrap the useQuery hook from react-query.
 * Handles the enabled status based on page visibility and SSR.
 *
 * @param options - Custom query options.
 * @param queryClient
 */
export function kitcoQuery<
  TQueryFnData = unknown,
  TError = DefaultError,
  TData = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
>(
  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,
  queryClient?: QueryClient,
): UseQueryResult<TData, TError>

export function kitcoQuery<
  TQueryFnData,
  TError,
  TData,
  TQueryKey extends QueryKey,
>(
  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> {
  // Destructure the enabled status from the options
  const { enabled = true, ...queryOptions } = options

  // Ensure enabled is a boolean
  const isEnabled = enabledStatus(
    typeof enabled === 'boolean' ? enabled : !!enabled,
  )

  // Merge the default options with the custom options
  const mergedOptions: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey> =
    {
      ...defaultQueryOptions(),
      ...queryOptions,
      enabled: isEnabled,
    }

  // Return the useQuery hook with the merged options
  return useQuery<TQueryFnData, TError, TData, TQueryKey>(
    mergedOptions,
    queryClient,
  )
}

/**
 * Backup query client - uses the backup GraphQL endpoint
 */
export function kitcoBackupQueryClient(): QueryClient {
  return new QueryClient({
    defaultOptions: {
      queries: defaultQueryOptions(),
    },
  })
}

/**
 * Backup query function to wrap the useQuery hook from react-query.
 * This version uses the backup GraphQL endpoint.
 * Handles the enabled status based on page visibility and SSR.
 *
 * @param options - Custom query options.
 * @param queryClient
 */
export function kitcoBackupQuery<
  TQueryFnData = unknown,
  TError = DefaultError,
  TData = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
>(
  options: DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError>

/**
 * Backup query function to wrap the useQuery hook from react-query.
 * This version uses the backup GraphQL endpoint.
 * Handles the enabled status based on page visibility and SSR.
 *
 * @param options - Custom query options.
 * @param queryClient
 */
export function kitcoBackupQuery<
  TQueryFnData = unknown,
  TError = DefaultError,
  TData = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
>(
  options: UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,
  queryClient?: QueryClient,
): UseQueryResult<TData, TError>

/**
 * Backup query function to wrap the useQuery hook from react-query.
 * This version uses the backup GraphQL endpoint.
 * Handles the enabled status based on page visibility and SSR.
 *
 * @param options - Custom query options.
 * @param queryClient
 */
export function kitcoBackupQuery<
  TQueryFnData = unknown,
  TError = DefaultError,
  TData = TQueryFnData,
  TQueryKey extends QueryKey = QueryKey,
>(
  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,
  queryClient?: QueryClient,
): UseQueryResult<TData, TError>

export function kitcoBackupQuery<
  TQueryFnData,
  TError,
  TData,
  TQueryKey extends QueryKey,
>(
  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> {
  // Destructure the enabled status from the options
  const { enabled = true, ...queryOptions } = options

  // Ensure enabled is a boolean
  const isEnabled = enabledStatus(
    typeof enabled === 'boolean' ? enabled : !!enabled,
  )

  // Merge the default options with the custom options
  const mergedOptions: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey> =
    {
      ...defaultQueryOptions(),
      ...queryOptions,
      enabled: isEnabled,
    }

  // Return the useQuery hook with the merged options
  return useQuery<TQueryFnData, TError, TData, TQueryKey>(
    mergedOptions,
    queryClient,
  )
}

export default kitcoQuery
