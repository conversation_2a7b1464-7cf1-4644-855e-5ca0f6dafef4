import { metalsBackup } from '~/src/lib/metals-factory.backup.lib'
import { kitcoBackupQuery } from '~/src/services/database/kitcoQuery'
import * as timestamps from '~/src/utils/timestamps'

/**
 * Get the "now" data query for the metal price using backup endpoint
 *
 * @returns {MetalQuoteQuery} The "now" data query
 * @param {string} currency The currency to fetch the data for
 * @param {string} symbol The symbol to fetch the data for
 */
const getMetalNowDataQuery = (currency = 'USD', symbol = 'AU') => {
  // Create the query arguments
  return metalsBackup.metalQuote({
    variables: {
      currency: currency ?? 'USD',
      symbol: symbol ?? 'AU',
      timestamp: timestamps.current(),
    },
    options: {
      refetchOnWindowFocus: true,
    },
  })
}

/**
 * Fetches the "now" data for the metal price using backup endpoint
 *
 * @returns {MetalQuoteQuery} The "now" data
 *
 * @param {string} currency The currency to fetch the data for
 * @param {string} symbol The symbol to fetch the data for
 */
const useMetalNowDataBackup = (currency = 'USD', symbol = 'AU') => {
  // Get the "now" data using backup endpoint
  return kitcoBackupQuery(getMetalNowDataQuery(currency, symbol))
}

export default useMetalNowDataBackup
