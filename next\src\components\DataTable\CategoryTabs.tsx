import React from 'react'
import styles from './DataTable.module.css'

interface CategoryTabsProps {
  categories: string[]
  activeCategory: string
  onCategoryChange: (category: string) => void
}

const CategoryTabs: React.FC<CategoryTabsProps> = ({
  categories,
  activeCategory,
  onCategoryChange,
}) => {
  return (
    <div className={styles.categoryTabs}>
      {categories.map((category) => (
        <div
          key={category}
          className={`${styles.categoryTab} ${
            activeCategory === category ? styles.active : ''
          }`}
          onClick={() => onCategoryChange(category)}
        >
          {category}
        </div>
      ))}
    </div>
  )
}

export default CategoryTabs
