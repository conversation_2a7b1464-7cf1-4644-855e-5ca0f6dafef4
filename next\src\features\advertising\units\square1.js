import { demandConfig, sizeDefaults } from './demandConfig'

export const square1 = {
  id: 'square-1',
  path: '/21841313772,22554256/kitco/square_one',
  sizes: sizeDefaults.square,
  sizeMappingName: 'square',
  refreshTime: 30,
  targeting: {
    refresh: 'true',
    refresh_time: '30',
  },
  prebid: [
    {
      mediaTypes: {
        banner: {
          sizeConfig: [
            { minViewPort: [0, 0], sizes: [] },
            { minViewPort: [768, 0], sizes: [] },
            {
              minViewPort: [1270, 0],
              sizes: [
                [125, 125],
                [180, 150],
              ],
            },
          ],
        },
      },
      bids: [
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '5709460',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'pubmatic',
          params: {
            publisherId: demandConfig.pubmatic.publisherId,
            adSlot: '3324602',
          },
          sizeConfig: [
            { minViewPort: [0, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [768, 0], relevantMediaTypes: ['none'] },
            { minViewPort: [1270, 0], relevantMediaTypes: ['banner'] },
          ],
        },
        {
          bidder: 'rubicon',
          params: {
            accountId: demandConfig.rubicon.accountId,
            siteId: demandConfig.rubicon.siteId,
            zoneId: '1802122',
          },
        },
        {
          bidder: 'ix',
          params: {
            siteId: '560694',
          },
        },
        {
          bidder: 'medianet',
          params: {
            cid: demandConfig.medianet.cid,
            crid: '*********',
          },
        },
        {
          bidder: 'onetag',
          params: {
            pubId: demandConfig.onetag.pubId,
          },
        },
      ],
    },
  ],
}
