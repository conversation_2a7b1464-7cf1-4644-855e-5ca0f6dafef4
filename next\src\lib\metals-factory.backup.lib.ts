import type { UseQueryOptions } from '@tanstack/react-query'
import { gql } from 'graphql-request'
import type {
  GetNivoDataQuery,
  GetNivoDataQueryVariables,
  MetalMonthAnnualQuery,
  MetalMonthAnnualQueryVariables,
  MetalQuoteQuery,
  MetalQuoteQueryVariables,
  SilverPgmQuery,
  SilverPgmQueryVariables,
} from '~/src/generated'
import { graphs } from '../services/database/fetcher'
import type QueryArgs from '../types/QueryArgs'
import { excludeTimestampFromCacheKey } from '../utils/exclude-timestamp-from-cache-key.util'
import { refetchInterval } from '../utils/timestamps'
import { metalFragment, metalQuoteFragment } from './metals-fragments.graphql'

/**
 * Backup version of the metals factory that uses the backup GraphQL endpoint
 * This can be used as a fallback when the primary endpoint is unavailable
 */
export const metalsBackup = {
  metalQuote: (
    args: QueryArgs<MetalQuoteQueryVariables, MetalQuoteQuery>,
  ): UseQueryOptions<MetalQuoteQuery> => {
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['metalQuote_backup', args?.variables],
      queryFn: async () =>
        await graphs.backupPricesFetch(
          gql`
            ${metalFragment}
            ${metalQuoteFragment}
            query MetalQuote(
              $symbol: String!
              $currency: String!
              $timestamp: Int
            ) {
              GetMetalQuoteV3(
                symbol: $symbol
                currency: $currency
                timestamp: $timestamp
              ) {
                ...MetalFragment
              }
            }
          `,
          args.variables,
        ),
    }
  },

  nivoChartData: (
    args: QueryArgs<GetNivoDataQueryVariables, GetNivoDataQuery>,
  ): UseQueryOptions<GetNivoDataQuery> => {
    return {
      ...args.options,
      refetchInterval,
      retry: 3,
      retryDelay: 1000,
      queryKey: ['nivoChartData_backup', args.variables],
      queryFn: async () =>
        await graphs.backupPricesFetch(
          gql`
            ${metalQuoteFragment}
            query GetNivoData(
              $currency: String!
              $timestamp: Int!
              $symbol: String!
            ) {
              now: GetMetalQuoteV3(
                symbol: $symbol
                timestamp: $timestamp
                currency: $currency
              ) {
                ID
                symbol
                currency
                name
                results {
                  ...MetalQuoteFragment
                }
              }

              history: GetMetalChartDataV3(
                chartId: TODAY
                symbol: $symbol
                currency: $currency
              ) {
                symbol
                currency
                results {
                  ...MetalQuoteFragment
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },

  silverPGM: (
    args: QueryArgs<SilverPgmQueryVariables, SilverPgmQuery>,
  ): UseQueryOptions<SilverPgmQuery> => {
    const cacheKey = excludeTimestampFromCacheKey(args.variables)
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['silverPGM_backup', cacheKey],
      queryFn: async () =>
        await graphs.backupPricesFetch(
          gql`
            ${metalFragment}
            ${metalQuoteFragment}
            query SilverPGM($currency: String!, $timestamp: Int) {
              silver: GetMetalQuoteV3(
                symbol: "AG"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              platinum: GetMetalQuoteV3(
                symbol: "PT"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              palladium: GetMetalQuoteV3(
                symbol: "PD"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }

              rhodium: GetMetalQuoteV3(
                symbol: "RH"
                timestamp: $timestamp
                currency: $currency
              ) {
                ...MetalFragment
              }
            }
          `,
          args.variables,
        ),
    }
  },

  metalMonthAnnual: (
    args: QueryArgs<MetalMonthAnnualQueryVariables, MetalMonthAnnualQuery>,
  ): UseQueryOptions<MetalMonthAnnualQuery> => {
    return {
      ...args.options,
      refetchInterval,
      queryKey: ['metalMonthAnnual_backup', args?.variables],
      queryFn: async () =>
        await graphs.backupPricesFetch(
          gql`
            query MetalMonthAnnual(
              $symbol: String!
              $currency: String!
              $timestamp: Int!
            ) {
              GetHistoricalPointsV3(
                symbol: $symbol
                currency: $currency
                timestamp: $timestamp
              ) {
                thirtyDay {
                  ID
                  change
                  changePercentage
                }
                sixtyDay {
                  ID
                  change
                  changePercentage
                }
                oneYear {
                  ID
                  change
                  changePercentage
                }
                fiveYear {
                  ID
                  change
                  changePercentage
                }
              }
            }
          `,
          args.variables,
        ),
    }
  },
}
