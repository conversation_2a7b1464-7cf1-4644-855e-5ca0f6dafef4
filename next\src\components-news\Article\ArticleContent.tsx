import React, { type FC, useCallback, useEffect } from 'react'
import { AdvertisingSlot } from 'react-advertising'
import { LeftContent } from '~/src/components-news/Article/LeftContent'
import { RightContent } from '~/src/components-news/Article/RightContent'
import { NewsCategoryTitleDetailPage } from '~/src/components/news-category/news-category.component'
import type { NewsArticle } from '~/src/generated'
import gtmEvent from '~/src/hooks/Global/GoogleTag'
import styles from '~/src/pages/news/article/article-alias.module.scss'
import useRecordView from '~/src/utils/useRecordView'

interface ArticleContentProps {
  articleData: NewsArticle
  counter?: number
  infiniteScrollArticle?: boolean
}

/**
 * Article content component
 *
 * @param articleData - The article data
 * @param counter - The article counter
 */
export const ArticleContent: FC<ArticleContentProps> = ({
  articleData,
  counter,
  infiniteScrollArticle = false,
}) => {
  // The percentage to trigger the next article
  const percentToTrigger = 35

  // Reference for the infinite scroll
  const ref = React.useRef<HTMLDivElement>(null)

  // Record the view of the article in Drupal
  useRecordView(Boolean(articleData), articleData?.id)

  useEffect(() => {
    if (infiniteScrollArticle) {
      // Record the page view in Google Tag Manager using custom event
      gtmEvent(
        articleData?.urlAlias,
        articleData?.title,
        `${process.env.NEXT_PUBLIC_URL}${articleData?.urlAlias}`,
        articleData?.author.name,
        'ArticleScrollView',
      )
      gtmEvent(
        articleData?.urlAlias,
        articleData?.title,
        `${process.env.NEXT_PUBLIC_URL}${articleData?.urlAlias}`,
        articleData?.author.name,
        'ArticleView',
      )
    } else {
      gtmEvent(
        articleData?.urlAlias,
        articleData?.title,
        `${process.env.NEXT_PUBLIC_URL}${articleData?.urlAlias}`,
        articleData?.author.name,
        'ArticleView',
      )
    }
  }, [articleData?.id])

  /**
   * Handle the scroll event
   *
   * If the user scrolls to the bottom of the page,
   * push the next article to the history
   */
  const handleScroll = useCallback(() => {
    // Get the ref element
    const element = ref.current

    // If the element is not available, return
    if (!element) return

    // Get the element's position and dimensions
    const rect = element.getBoundingClientRect()

    // Get the viewport height
    const viewportHeight = window.innerHeight

    // Calculate the visible height
    const visibleHeight =
      Math.min(viewportHeight, rect.bottom) - Math.max(0, rect.top)

    // Calculate the visibility percentage
    const visibilityPercentage = (visibleHeight / viewportHeight) * 100

    // If the visibility percentage is greater than the threshold,
    // push the next article to the history
    if (
      visibilityPercentage >= percentToTrigger &&
      window.location.pathname !== articleData.urlAlias
    ) {
      window.history.pushState(null, '', articleData.urlAlias)
    }
  }, [articleData.urlAlias])

  // Add the scroll event listener
  useEffect(() => {
    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [handleScroll])

  return (
    <div className={styles.wrapper} ref={ref}>
      <div className="mx-auto w-full max-w-full px-5 md:flex md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <div className="flex md:w-[190px]">
          <NewsCategoryTitleDetailPage
            category={articleData?.category?.urlAlias ?? '/news'}
          />
        </div>
        <div className="md:w-[calc(100%_-_190px)] md:pl-10">
          <h1 className="mb-6 !font-lato text-[34px] font-bold leading-[39px]">
            {articleData?.title}
          </h1>
        </div>
      </div>
      <div className="mx-auto w-full max-w-full px-5 md:flex md:px-10 lg:px-10 xl:w-[1240px] xl:px-0">
        <LeftContent articleData={articleData} counter={counter} />{' '}
        <RightContent articleData={articleData} counter={counter} />
      </div>
      {counter === -1 && (
        <AdvertisingSlot
          id={'large-banner-1'}
          className={
            'mx-auto mt-[48px] md:mt-[80px] flex min-h-[250px] min-w-[300px] justify-center items-center w-[80%] min-w-[300px] md:min-w-[728px] md:min-h-[90px] lg:min-w-[970px] lg:min-h-[250px]'
          }
        />
      )}
    </div>
  )
}
