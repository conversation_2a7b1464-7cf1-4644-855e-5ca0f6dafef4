'use client'

import clsx from 'clsx'
import dynamic from 'next/dynamic'
import Head from 'next/head'
import Image from 'next/image'
import Link from 'next/link'
// import MarketIndicators from '../components/MarketIndicators/MarketIndicators'
import { AdvertisingSlot } from 'react-advertising'
import { BsPlusSquareFill } from 'react-icons/bs'
import MarketIndicesCell from '~/src/components-markets/MarketIndicesCell/MarketIndicesCell'
import MetalMonthAnnualCell from '~/src/components-metals/MetalMonthAnnualCell/MetalMonthAnnualCell'
import ShanghaiGold from '~/src/components-metals/ShanghaiGold/ShanghaiGold'
import SilverPricePGMCell from '~/src/components-metals/SilverPricePGMCell/SilverPricePGMCell'
// import InvestorInformation from "../components/InvestorInformation/InvestorInformation";
import Divider from '~/src/components/Divider/Divider'
import KGX from '~/src/components/KGX/KGX'
import MiningEquitiesWidget from '~/src/components/MiningEquities/Widget/MiningEquitiesWidget'
import { PressReleases } from '~/src/components/PressReleases/PressReleases'
import { GoldIndicators } from '~/src/components/gold-indicators/gold-indicators.component'
import { GoldRatiosSidebar } from '~/src/components/gold-ratios-sidebar/gold-ratios-sidebar.component'
import { InvestmentTrends } from '~/src/components/investment-trends/investment-trends.component'
import { CommentariesFeed } from '~/src/components/news-home-page/news-home-page-commentaries.component'
import { NewsHomepageDesktop } from '~/src/components/news-home-page/news-home-page-desktop.component'
import { NewsMiningTrendsCell } from '~/src/components/news-mining-trends/news-mining-trends.component'
import { getMetalsDataQuery } from '~/src/hooks/GlobalIndex/useGIMetalsData'
import { MiningEquitiesQueries } from '~/src/lib/MiningEquities/Queries'
import gridAreas from '~/src/styles/gridAreas.module.scss'
import { TimestampProvider } from '~/src/utils/ctxTimestamp'
import { ssrQueries } from '~/src/utils/ssr-wrappers'
import * as timestamps from '~/src/utils/timestamps'
import {
  CryptosTable,
  cryptoTableVariables,
} from '../components-cryptos/CryptosTable/CryptosTable'
import { LondonFixSidebar } from '../components-metals/LondonFixSidebar/LondonFixSidebar'
import CryptoMarkets, {
  cryptoMarketsVariables,
} from '../components/CryptoMarkets/CryptoMarkets'
import { ErrBoundary } from '../components/ErrBoundary/ErrBoundary'
import Layout from '../components/Layout/LayoutLarger'
import { BreakingNews } from '../components/breaking-news/breaking-news.component'
import { VideoNewsOutter } from '../components/video-news-outter-shell/video-news-outter-shell.component'
import { Barcharts } from '../features/bar-charts/barcharts'
import HomePageChartCell from '../features/home-page/HomePageChartCell'
import { cryptos } from '../lib/cryptos-factory.lib'
import { markets } from '../lib/markets-factory.lib'
import { metals } from '../lib/metals-factory.lib'
import { news } from '../lib/news-factory.lib'
import { est } from '../utils/time'
import styles from './home-page.module.scss'

const ExchangeRates = dynamic(
  async () =>
    await import('~/src/components/ExchangeRatesTable/ExchangeRatesTable'),
  { ssr: false },
)

const TradingViewCalendar = dynamic(
  async () =>
    await import('~/src/components/trading-view-iframes').then(
      (mod) => mod.TradingViewCalendar,
    ),
  { ssr: false },
)

export const getServerSideProps = async ({ res }) => {
  const params = {
    limit: 15,
    offset: 0,
  }

  const { dehydratedState } = await ssrQueries({
    ctxRes: res,
    queries: [
      news.nodeListQueue({
        variables: { ...params, queueId: 'latest_news' },
      }),
      news.nodeListNewsFeed({
        variables: { ...params, queueId: 'latest_news' },
      }),
      news.streetNews({ variables: { ...params } }),
      news.newsByCategoryGeneric({
        variables: { ...params, urlAlias: '/news/category/commodities' },
      }),
      news.newsByCategoryGeneric({
        variables: { ...params, urlAlias: '/news/category/cryptocurrencies' },
      }),
      news.newsByCategoryGeneric({
        variables: { ...params, urlAlias: '/news/category/mining' },
      }),
      news.newsOTWList({
        variables: { ...params },
      }),
      metals.metalMonthAnnual({
        variables: {
          symbol: 'AU',
          currency: 'USD',
          timestamp: timestamps.current(),
        },
      }),
      metals.silverPGM({
        variables: {
          currency: 'USD',
          timestamp: timestamps.current(),
        },
      }),
      metals.goldRatios({
        variables: {
          timestamp: timestamps.current(),
          symbols: '$XAU,$HUI,$SPX,$DOWI',
        },
      }),
      // metals.exchangeRatesTable(),
      // TODO: sorry i know this dupe request not good, we fix later
      cryptos.cryptosTable({
        variables: cryptoTableVariables,
      }),
      // TODO: sorry i know this dupe request not good, we fix later
      cryptos.cryptosTable({
        variables: cryptoMarketsVariables,
      }),
      metals.shanghaiFix({
        variables: {
          timestamp: timestamps.current(),
          symbol: 'SHAU',
          currency: 'CNY',
        },
      }),
      markets.regionIndices({
        variables: {
          timestamp: timestamps.current(),
        },
      }),
      markets.marketStatus(),
      getMetalsDataQuery(true),
      MiningEquitiesQueries.miningEquitiesTable(),
    ],
  })

  return {
    props: {
      dehydratedState,
    },
  }
}

const Home = ({ ssrTimestamp }) => {
  return (
    <TimestampProvider timestamp={ssrTimestamp}>
      <Layout title="Live Gold Prices | Gold News And Analysis | Mining News | KITCO">
        <Head>
          <meta
            name="description"
            content="KITCO Covers The Latest Gold News, Silver News, Live Gold Prices, Silver Prices, Gold Charts, Gold Rate, Mining News, ETF, FOREX, Bitcoin, Crypto, Stock Markets"
          />
          <link rel="canonical" href={process.env.NEXT_PUBLIC_URL} />
        </Head>
        <ErrBoundary>
          <div
            className={clsx(
              'block gap-[15px] px-[15px]',
              styles.tabletGridOrder,
            )}
          >
            <div
              className={clsx(
                'contents max-w-full',
                'tablet:gap-[15px] desktop:flex desktop:w-[200px] desktop:flex-col',
              )}
            >
              <div className={clsx('hidden desktop:block')}>
                <AdvertisingSlot
                  id={'square-1'}
                  className="mx-auto hidden min-h-[150px] min-w-[180px] items-center justify-center desktop:flex no-print"
                />
              </div>
              <div className={clsx(gridAreas.aa, 'order-7 tablet:order-none')}>
                <ErrBoundary enableReset errorTitle="Live Spot Gold">
                  <MetalMonthAnnualCell />
                </ErrBoundary>
              </div>
              <div className={clsx(gridAreas.bb, 'order-8 tablet:order-none')}>
                <ErrBoundary errorTitle="Silver Price & PGMs">
                  <SilverPricePGMCell />
                </ErrBoundary>
              </div>
              <div className={clsx(gridAreas.cc, 'order-10 tablet:order-none')}>
                <ErrBoundary errorTitle="Market Indices">
                  <MarketIndicesCell />
                </ErrBoundary>
              </div>
              <AdvertisingSlot
                id={'left-rail-1'}
                className="mx-auto hidden h-[600px] w-[160px] desktop:block no-print"
              />
              <div
                className={clsx(gridAreas.ff, 'order-[13] tablet:order-none')}
              >
                <ErrBoundary errorTitle="Shanghai Gold Benchmark Price">
                  <ShanghaiGold />
                </ErrBoundary>
              </div>
              <div className={clsx(gridAreas.ee, 'order-12 tablet:order-none')}>
                <ErrBoundary errorTitle="London Fix Gold">
                  <LondonFixSidebar />
                </ErrBoundary>
              </div>
              {/*<div className={clsx("hidden desktop:block", gridAreas.gg)}>*/}
              {/*  <InvestorInformation />*/}
              {/*</div>*/}

              <AdvertisingSlot
                id={'left-rail-2'}
                className="sticky top-[100px] mx-auto hidden h-[600px] w-[160px] desktop:block no-print"
              />
            </div>
            <div
              className={clsx(
                'contents',
                'desktop:flex desktop:flex-col desktop:gap-[15px]',
              )}
            >
              <div className={clsx(gridAreas.ss, 'order-1 tablet:order-none')}>
                {/* <ErrBoundary errorTitle="Kitco Gold Index Widget HomePage">
                  <KGX className="mx-auto mb-2.5 w-full" /> */}
                <ErrBoundary errorTitle="Kitco Global Index Widget HomePage">
                  <KGX
                    className="mx-auto mb-2.5 w-full"
                    disableDragDrop={true}
                  />
                </ErrBoundary>
              </div>
              <div className={clsx(gridAreas.jj, 'order-2 tablet:order-none')}>
                <ErrBoundary errorTitle="Breaking News">
                  <BreakingNews />
                </ErrBoundary>
              </div>
              <div className={clsx(gridAreas.kk, 'order-3 tablet:order-none')}>
                <AdvertisingSlot
                  id={'banner-1'}
                  className="flex justify-center mx-auto my-[15px] h-[280px] w-[336px] tablet:h-[90px] tablet:w-[728px] no-print min-[1270px]:hidden"
                />
                <ErrBoundary>
                  <NewsHomepageDesktop />
                </ErrBoundary>
                <AdvertisingSlot
                  id={'banner-2'}
                  className="flex justify-center mx-auto my-[15px] h-[280px] w-[336px] tablet:h-[90px] tablet:w-[728px] no-print min-[1270px]:hidden"
                />
              </div>
              <div className={clsx(gridAreas.ll, 'order-4 tablet:order-none')}>
                <ErrBoundary errorTitle="Video News">
                  <VideoNewsOutter />
                </ErrBoundary>
                <AdvertisingSlot
                  id="mining-content-billboard"
                  className="mx-auto mt-4 flex min-h-[600px] w-[100%] max-w-[300px] items-center justify-center border-[1px] border-[#ccc]
                    native-sm:h-[252px] native-sm:min-h-[200px] native-sm:max-w-[970px] no-print"
                />
              </div>
              {/*<div className={clsx(gridAreas.lll, "tablet:order-none order-4 desktop:hidden")}>*/}
              <Divider className={clsx('hidden desktop:block')} />
              <div
                className={clsx(
                  'block',
                  gridAreas.nn,
                  'order-6 tablet:order-none',
                )}
              >
                <ErrBoundary errorTitle="More Commentaries">
                  <CommentariesFeed />
                </ErrBoundary>
                <AdvertisingSlot
                  id={'banner-3'}
                  className="flex justify-center mx-auto my-[15px] h-[280px] w-[336px] tablet:h-[90px] tablet:w-[728px] no-print min-[1270px]:hidden"
                />
              </div>
              <Divider className={clsx('hidden desktop:block')} />
              <div
                className={clsx(
                  'flex grid-cols-[1fr_300px] flex-col gap-5 tablet:grid',
                  gridAreas.oo,
                  'order-[17] tablet:order-none',
                )}
              >
                <div className="">
                  <ErrBoundary errorTitle="New Mining Trends">
                    <NewsMiningTrendsCell />
                  </ErrBoundary>
                </div>
                <div className="hidden tablet:block">
                  <ErrBoundary errorTitle="Investment Trends">
                    <InvestmentTrends />
                  </ErrBoundary>
                </div>
              </div>
              <div
                className={clsx(gridAreas.qq, 'order-[18] tablet:order-none')}
              >
                <ErrBoundary errorTitle="Latest Press Releases">
                  <PressReleases />
                </ErrBoundary>
              </div>
              <div
                className={clsx(gridAreas.tt, 'order-[25] tablet:order-none')}
              >
                <ErrBoundary errorTitle="Mining Equities">
                  <MiningEquitiesWidget />
                </ErrBoundary>
              </div>
              <div
                className={clsx(
                  gridAreas.uu,
                  'order-[26] hidden tablet:order-none tablet:block',
                )}
              >
                <ErrBoundary errorTitle="Exchange Rates">
                  <ExchangeRates />
                </ErrBoundary>
              </div>
              <div
                className={clsx(gridAreas.vv, 'order-[27] tablet:order-none')}
              >
                <div className="flex">
                  <Image
                    width={35}
                    height={35}
                    className=""
                    src={'/crypto-logo/crypto-landing-icon.svg'}
                    alt="Kitco Crypto Logo"
                  />
                  <Link
                    href="/price/crypto"
                    className="ml-1 font-lato text-2xl font-semibold hover:text-[#c06a24]"
                  >
                    <h2>Crypto Market Live Quotes</h2>
                  </Link>
                </div>
                <div className="py-[18px]">
                  <span
                    className="text-xs text-black/60"
                    suppressHydrationWarning
                  >
                    Prices as of {est().format('MMM DD, YYYY hh:mm')} NY Time
                  </span>
                </div>
                <div className="relative border border-[#E2E8F0]">
                  <ErrBoundary errorTitle="Cryptos Table">
                    <CryptosTable />
                  </ErrBoundary>
                </div>
                <div className={styles.btnCryptoMarket}>
                  <Link
                    href="/price/crypto"
                    className={clsx(
                      'flex items-center gap-2',
                      'group hover:bg-[#1D61AE] active:bg-[#144985]',
                    )}
                  >
                    <div
                      className={clsx(
                        'flex items-center justify-center gap-2',
                        'w-full border border-t-0 border-[#E2E8F0] py-2',
                      )}
                    >
                      <BsPlusSquareFill className="mt-[.10rem] text-[#1D61AE] group-hover:bg-[#1D61AE] group-hover:text-[#ffffff] group-active:bg-[#1D61AE] group-active:text-[#ffffff]" />
                      <span className="font-bold text-[#1D61AE] underline group-hover:!text-[#ffffff] group-active:!text-[#ffffff]">
                        More coins...
                      </span>
                    </div>
                  </Link>
                </div>
              </div>
              <div
                className={clsx(
                  'hidden tablet:block desktop:hidden',
                  gridAreas.ddd,
                )}
              />
              <div
                className={clsx(
                  'hidden tablet:block desktop:hidden',
                  gridAreas.eee,
                )}
              ></div>
              <div
                className={clsx(
                  'block tablet:hidden',
                  gridAreas.hhh,
                  'order-[16] tablet:order-none',
                )}
              />
              <div
                className={clsx(
                  'block tablet:hidden',
                  gridAreas.iii,
                  'order-[19] tablet:order-none',
                )}
              />
            </div>
            <div
              className={clsx(
                'contents desktop:flex desktop:flex-col desktop:gap-[15px]',
              )}
            >
              <div
                className={clsx(
                  gridAreas.ww,
                  'order-2 max-w-full tablet:order-none',
                )}
              >
                <ErrBoundary enableReset>
                  <HomePageChartCell />
                </ErrBoundary>
              </div>
              <AdvertisingSlot
                id={'right-rail-1-hp'}
                className="hidden h-[250px] w-[300px] mx-auto desktop:block
                  amqdAds:w-[336px] amqdAds:h-[280px] amqdAds:flex amqdAds:justify-center no-print"
              />
              {/* <div className="block order-[19] tablet:order-none">
                <ErrBoundary errorTitle="London Fix Mobile">
                  <LondonFixGridMobile />
                </ErrBoundary>
              </div> */}
              <div
                className={clsx(
                  'block',
                  gridAreas.yy,
                  'order-[20] tablet:order-none',
                )}
              >
                <ErrBoundary errorTitle="Today's Crypto Markets">
                  <CryptoMarkets />
                </ErrBoundary>
              </div>
              <div
                className={clsx(
                  'block',
                  gridAreas.zz,
                  'order-[21] tablet:order-none',
                )}
              >
                <ErrBoundary errorTitle="Gold Ratios">
                  <GoldRatiosSidebar />
                </ErrBoundary>
              </div>
              <AdvertisingSlot
                id={'right-rail-2-hp'}
                className="mx-auto hidden h-[600px] w-[300px] desktop:block
                amqdAds:w-[336px] amqdAds:flex amqdAds:justify-center no-print"
              />
              <div
                className={clsx(
                  gridAreas.hh,
                  'order-[14] h-max border border-ktc-borders tablet:order-none',
                )}
              >
                <h2 className="bg-[#373737] text-center text-lg font-semibold capitalize leading-[38px] text-white">
                  <Link href="/markets/indices/$XAU" className="text-white">
                    XAU Index
                  </Link>
                </h2>
                <ErrBoundary errorTitle="Bar charts">
                  <Barcharts
                    className="py-[0.8em] desktop:max-h-[180px]"
                    symbol="$XAU"
                    href="/markets/indices/$XAU"
                  />
                </ErrBoundary>
              </div>
              <div
                className={clsx(
                  gridAreas.ii,
                  'order-[15] h-max border border-ktc-borders tablet:order-none',
                )}
              >
                <h2 className="bg-[#373737] text-center text-lg font-semibold capitalize leading-[38px] text-white">
                  <Link href="/markets/indices/$HUI" className="text-white">
                    HUI Index
                  </Link>
                </h2>
                <ErrBoundary errorTitle="Bar charts">
                  <Barcharts
                    className="py-[0.8em] desktop:max-h-[180px]"
                    symbol="$HUI"
                    href="/markets/indices/$HUI"
                  />
                </ErrBoundary>
              </div>
              <div
                className={clsx(
                  'block',
                  gridAreas.aaa,
                  'order-[22] tablet:order-none',
                )}
              >
                <ErrBoundary errorTitle="Gold Indicators">
                  <GoldIndicators />
                </ErrBoundary>
              </div>
              <div
                className={clsx(
                  'block',
                  gridAreas.bbb,
                  'order-[23] tablet:order-none',
                )}
              >
                {/* <MarketIndicators title="Market indicators" /> */}
              </div>
              <div
                className={clsx(
                  gridAreas.ccc,
                  'hidden max-w-full desktop:block',
                )}
              >
                <ErrBoundary errorTitle="TradingView Calendar">
                  <TradingViewCalendar />
                </ErrBoundary>
              </div>
              <AdvertisingSlot
                id={'right-rail-3-hp'}
                className="sticky top-[100px] mx-auto hidden h-[600px] w-[300px] desktop:block
                    amqdAds:w-[336px] amqdAds:flex amqdAds:justify-center no-print"
              />
              <AdvertisingSlot
                id={'footer'}
                className="after:font-['Font Awesome 5 Pro]' fixed bottom-0 left-1/2
                  z-20
                  w-[320px]
                  -translate-x-1/2 after:absolute after:right-0 after:top-[-25px] after:w-[20px] after:cursor-pointer after:rounded after:bg-[#373737] after:text-center after:text-lg after:font-bold after:leading-[1.1] after:text-white after:content-['X'] tablet:h-[90px] tablet:w-[728px] desktop:hidden no-print"
              />
            </div>
          </div>
        </ErrBoundary>
      </Layout>
    </TimestampProvider>
  )
}

export default Home
