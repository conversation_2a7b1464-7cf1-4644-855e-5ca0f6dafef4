@import '../../../styles/article.scss';

.parentWrapper {
  & .wrapper:not(:last-child) {
    margin-bottom: 2rem;
    padding-bottom: 3rem;
    border-bottom: solid 1px #e2e8f0;
  }

  @media screen and (min-width: 768px) {
    & .wrapper:not(:last-child) {
      margin-bottom: 4rem;
      padding-bottom: 5rem;
      border-bottom: solid 1px #e2e8f0;
    }
  }
}

.articleBodyStyles {
  & p {
    margin: 1em 0;
  }

  & blockquote {
    & p {
      margin: 0;
    }
  }

  & img[class*='align-right'],
  & img[align='right'],
  & img[style*='float:right'] {
    width: 100% !important;
    float: unset;
    margin-bottom: 0.625rem;
  }

  @media screen and (min-width: 768px) {
    & img[class*='align-right'],
    & img[align='right'],
    & img[style*='float:right'] {
      width: 50% !important;
      float: right;
    }
  }
}

.articleBulletNews {
  margin-top: -10px;
  font-family: 'Mulish' !important;
  font-size: 17px;
  line-height: 140%;
  font-weight: 500 !important;

  &::before {
    content: '';
    border-color: transparent #111;
    border-style: solid;
    border-width: 0.35em 0 0.35em 0.45em;
    display: block;
    width: 5px;
    height: 10px;
    left: -1em;
    top: 0.9em;
    position: relative;
  }
}

.articleWrapper {
  @include articleWrapper;
}

.exitsPresentationImage {
  margin-top: -1em;
}
