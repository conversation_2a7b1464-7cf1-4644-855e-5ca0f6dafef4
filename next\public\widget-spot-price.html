<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kitco Spot Price Widget</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: transparent;
        }
        .widget-wrapper {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        #widget_spot_price {
            width: 100%;
            max-width: 100%;
        }
    </style>
</head>
<body>
    <!-- Kitco Widget BEGIN -->
    <div class="widget-wrapper">
        <div id="widget_spot_price"></div>
    </div>
    <script src="https://storage.googleapis.com/kitco-widgets-storage/widgetSpotPrice.bundle.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof window.createSpotePriceWidget === 'function') {
                const config = {
                    "width": "100%",
                    "isTransparent": false,
                    "colorTheme": "light"
                };    
                window.createSpotePriceWidget("widget_spot_price", config); 
            } else {
                console.error('window.createSpotePriceWidget is not a function');
            }
        });
    </script>
    <!-- Kitco Widget END -->
</body>
</html> 